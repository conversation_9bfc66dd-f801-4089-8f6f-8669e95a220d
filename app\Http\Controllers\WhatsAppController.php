<?php

namespace App\Http\Controllers;

use App\Models\WhatsAppTemplate;
use App\Models\WhatsAppMessage;
use App\Models\WhatsAppContact;
use App\Models\Customer;
use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WhatsAppController extends Controller
{
    protected $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_whatsapp')->only(['dashboard', 'messages', 'contacts', 'templates']);
        $this->middleware('permission:manage_whatsapp')->only(['sendMessage', 'createTemplate', 'updateTemplate']);
        $this->whatsappService = $whatsappService;
    }

    /**
     * WhatsApp dashboard
     */
    public function dashboard(Request $request)
    {
        $days = $request->get('days', 30);
        $statistics = $this->whatsappService->getStatistics($days);

        // Get recent messages
        $recentMessages = WhatsAppMessage::with(['contact', 'customer', 'template'])
                                        ->orderBy('created_at', 'desc')
                                        ->limit(10)
                                        ->get();

        // Get failed messages
        $failedMessages = WhatsAppMessage::with(['contact', 'customer'])
                                        ->failed()
                                        ->orderBy('created_at', 'desc')
                                        ->limit(5)
                                        ->get();

        // Get active templates
        $activeTemplates = WhatsAppTemplate::active()
                                          ->orderBy('created_at', 'desc')
                                          ->limit(10)
                                          ->get();

        return view('whatsapp.dashboard', compact(
            'statistics',
            'recentMessages',
            'failedMessages',
            'activeTemplates'
        ));
    }

    /**
     * Message management
     */
    public function messages(Request $request)
    {
        $query = WhatsAppMessage::with(['contact', 'customer', 'template', 'sentBy']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('message_type')) {
            $query->where('message_type', $request->message_type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('phone_number', 'like', "%{$search}%")
                  ->orWhereHas('contact', function ($contactQuery) use ($search) {
                      $contactQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'failed') {
            $query->failed();
        } elseif ($request->filter === 'scheduled') {
            $query->scheduled();
        } elseif ($request->filter === 'pending') {
            $query->pending();
        }

        $messages = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('whatsapp.messages.index', compact('messages'));
    }

    /**
     * Contact management
     */
    public function contacts(Request $request)
    {
        $query = WhatsAppContact::with(['customer']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('opt_in_status')) {
            $query->where('opt_in_status', $request->opt_in_status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('phone_number', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'blocked') {
            $query->blocked();
        } elseif ($request->filter === 'opted_out') {
            $query->where('opt_in_status', 'opted_out');
        } elseif ($request->filter === 'can_receive') {
            $query->canReceiveMessages();
        }

        $contacts = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('whatsapp.contacts.index', compact('contacts'));
    }

    /**
     * Template management
     */
    public function templates(Request $request)
    {
        $query = WhatsAppTemplate::with(['createdBy']);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('use_case')) {
            $query->where('use_case', $request->use_case);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('template_code', 'like', "%{$search}%")
                  ->orWhere('body_content', 'like', "%{$search}%");
            });
        }

        // Special filters
        if ($request->filter === 'active') {
            $query->active();
        } elseif ($request->filter === 'pending_approval') {
            $query->where('status', 'pending_approval');
        }

        $templates = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('whatsapp.templates.index', compact('templates'));
    }

    /**
     * Send WhatsApp message
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'phone_number' => 'required|string',
            'message_type' => 'required|in:text,template',
            'text_content' => 'required_if:message_type,text|string|max:4096',
            'template_id' => 'required_if:message_type,template|exists:whats_app_templates,id',
            'template_data' => 'nullable|array',
            'customer_id' => 'nullable|exists:customers,id',
            'priority' => 'nullable|in:low,normal,high,urgent,emergency',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        try {
            if ($request->message_type === 'template') {
                $template = WhatsAppTemplate::findOrFail($request->template_id);
                $message = $this->whatsappService->sendTemplateMessage(
                    $request->phone_number,
                    $template,
                    $request->template_data ?? [],
                    [
                        'customer_id' => $request->customer_id,
                        'priority' => $request->priority ?? 'normal',
                        'scheduled_at' => $request->scheduled_at,
                    ]
                );
            } else {
                $message = $this->whatsappService->sendTextMessage(
                    $request->phone_number,
                    $request->text_content,
                    [
                        'customer_id' => $request->customer_id,
                        'priority' => $request->priority ?? 'normal',
                        'scheduled_at' => $request->scheduled_at,
                    ]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'WhatsApp message sent successfully.',
                'whatsapp_message' => $message,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Webhook endpoint for WhatsApp
     */
    public function webhook(Request $request)
    {
        // Verify webhook (GET request)
        if ($request->isMethod('GET')) {
            $mode = $request->query('hub_mode');
            $token = $request->query('hub_verify_token');
            $challenge = $request->query('hub_challenge');

            if ($mode === 'subscribe' && $token === config('whatsapp.webhook_verify_token')) {
                return response($challenge, 200);
            }

            return response('Forbidden', 403);
        }

        // Process webhook (POST request)
        if ($request->isMethod('POST')) {
            $webhookData = $request->all();

            if ($this->whatsappService->handleWebhook($webhookData)) {
                return response('OK', 200);
            }

            return response('Error processing webhook', 500);
        }

        return response('Method not allowed', 405);
    }

    /**
     * Get WhatsApp statistics
     */
    public function getStatistics(Request $request)
    {
        $days = $request->get('days', 30);
        $statistics = $this->whatsappService->getStatistics($days);

        return response()->json($statistics);
    }
}
