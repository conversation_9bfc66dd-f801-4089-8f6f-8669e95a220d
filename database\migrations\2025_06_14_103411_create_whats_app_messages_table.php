<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whats_app_messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique();
            $table->foreignId('template_id')->nullable()->constrained('whats_app_templates');
            $table->unsignedBigInteger('contact_id')->nullable();
            // $table->foreign('contact_id')->references('id')->on('whats_app_contacts');
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->string('phone_number');
            $table->enum('message_type', ['template', 'text', 'image', 'document', 'video', 'audio', 'location', 'contact']);
            $table->json('content');
            $table->json('template_data')->nullable();
            $table->enum('status', ['pending', 'queued', 'sent', 'delivered', 'read', 'failed', 'cancelled', 'scheduled'])->default('pending');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->json('webhook_data')->nullable();
            $table->enum('priority', ['low', 'normal', 'high', 'urgent', 'emergency'])->default('normal');
            $table->timestamp('scheduled_at')->nullable();
            $table->integer('retry_count')->default(0);
            $table->decimal('cost', 8, 4)->default(0);
            $table->string('gateway_message_id')->nullable();
            $table->json('gateway_response')->nullable();
            $table->string('triggered_by_event')->nullable();
            $table->string('related_model_type')->nullable();
            $table->unsignedBigInteger('related_model_id')->nullable();
            $table->foreignId('sent_by')->nullable()->constrained('users');
            $table->timestamps();

            $table->index(['phone_number', 'status']);
            $table->index(['status', 'created_at']);
            $table->index(['priority', 'scheduled_at']);
            $table->index(['related_model_type', 'related_model_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whats_app_messages');
    }
};
