<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'GCMS') }} - Gas Cylinder Management System</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom Styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .slide-in-left {
            animation: slideInLeft 1s ease-out;
        }

        .slide-in-right {
            animation: slideInRight 1s ease-out;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body class="min-h-screen gradient-bg">
    <div class="min-h-screen flex">
        <!-- Left Side - Illustration & Branding -->
        <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>

            <!-- Animated Background Elements -->
            <div class="absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full floating-animation"></div>
            <div class="absolute top-40 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full floating-animation" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 left-20 w-24 h-24 bg-white bg-opacity-10 rounded-full floating-animation" style="animation-delay: 4s;"></div>

            <div class="relative z-10 flex flex-col justify-center items-center p-12 text-white slide-in-left">
                <!-- Logo/Brand -->
                <div class="mb-8 text-center">
                    <div class="w-24 h-24 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-gas-pump text-4xl text-white"></i>
                    </div>
                    <h1 class="text-4xl font-bold mb-2">GCMS</h1>
                    <p class="text-xl opacity-90">Gas Cylinder Management System</p>
                </div>

                <!-- Illustration -->
                <div class="text-center mb-8">
                    <div class="relative">
                        <!-- Main Cylinder Illustration -->
                        <div class="w-32 h-48 mx-auto mb-6 relative">
                            <div class="absolute inset-0 bg-gradient-to-b from-gray-300 to-gray-500 rounded-t-full rounded-b-lg shadow-2xl floating-animation"></div>
                            <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-6 bg-yellow-400 rounded-full"></div>
                            <div class="absolute top-8 left-1/2 transform -translate-x-1/2 w-20 h-2 bg-blue-500 rounded-full"></div>
                            <div class="absolute top-12 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-green-500 rounded-full"></div>
                        </div>

                        <!-- Floating Icons -->
                        <div class="absolute -top-4 -left-8 w-12 h-12 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center floating-animation">
                            <i class="fas fa-qrcode text-white"></i>
                        </div>
                        <div class="absolute -top-4 -right-8 w-12 h-12 bg-green-500 bg-opacity-80 rounded-full flex items-center justify-center floating-animation" style="animation-delay: 1s;">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <div class="absolute -bottom-4 -left-8 w-12 h-12 bg-purple-500 bg-opacity-80 rounded-full flex items-center justify-center floating-animation" style="animation-delay: 2s;">
                            <i class="fas fa-mobile-alt text-white"></i>
                        </div>
                        <div class="absolute -bottom-4 -right-8 w-12 h-12 bg-orange-500 bg-opacity-80 rounded-full flex items-center justify-center floating-animation" style="animation-delay: 3s;">
                            <i class="fas fa-truck text-white"></i>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="text-center space-y-3">
                    <div class="flex items-center text-sm opacity-90">
                        <i class="fas fa-check-circle text-green-400 mr-3"></i>
                        <span>Real-time Cylinder Tracking</span>
                    </div>
                    <div class="flex items-center text-sm opacity-90">
                        <i class="fas fa-check-circle text-green-400 mr-3"></i>
                        <span>QR Code Management</span>
                    </div>
                    <div class="flex items-center text-sm opacity-90">
                        <i class="fas fa-check-circle text-green-400 mr-3"></i>
                        <span>Mobile-First Design</span>
                    </div>
                    <div class="flex items-center text-sm opacity-90">
                        <i class="fas fa-check-circle text-green-400 mr-3"></i>
                        <span>Advanced Analytics</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-md slide-in-right">
                <!-- Mobile Logo (visible on small screens) -->
                <div class="lg:hidden text-center mb-8">
                    <div class="w-16 h-16 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-gas-pump text-2xl text-white"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-white mb-1">GCMS</h1>
                    <p class="text-white opacity-90">Gas Cylinder Management</p>
                </div>

                <!-- Login Card -->
                <div class="glass-effect rounded-2xl p-8 shadow-2xl">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-white mb-2">Welcome Back</h2>
                        <p class="text-white opacity-80">Sign in to your GCMS account</p>
                    </div>

                    <!-- Login Form -->
                    <form method="POST" action="{{ route('login') }}" class="space-y-6">
                        @csrf

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-white mb-2">
                                <i class="fas fa-envelope mr-2"></i>Email Address
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ old('email') }}"
                                   required
                                   autofocus
                                   class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent transition-all duration-200"
                                   placeholder="Enter your email">
                            @error('email')
                                <p class="mt-2 text-sm text-red-300">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-white mb-2">
                                <i class="fas fa-lock mr-2"></i>Password
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="password"
                                       name="password"
                                       required
                                       class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent transition-all duration-200"
                                       placeholder="Enter your password">
                                <button type="button"
                                        onclick="togglePassword()"
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white opacity-70 hover:opacity-100 transition-opacity">
                                    <i id="password-icon" class="fas fa-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <p class="mt-2 text-sm text-red-300">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Remember Me -->
                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input type="checkbox"
                                       name="remember"
                                       class="w-4 h-4 text-white bg-white bg-opacity-20 border-white border-opacity-30 rounded focus:ring-white focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-white opacity-80">Remember me</span>
                            </label>

                            @if (Route::has('password.request'))
                                <a href="{{ route('password.request') }}"
                                   class="text-sm text-white opacity-80 hover:opacity-100 transition-opacity">
                                    Forgot password?
                                </a>
                            @endif
                        </div>

                        <!-- Login Button -->
                        <button type="submit"
                                class="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign In
                        </button>
                    </form>

                    <!-- Demo Credentials -->
                    <div class="mt-6 p-4 bg-white bg-opacity-10 rounded-lg cursor-pointer hover:bg-opacity-20 transition-all duration-200" onclick="fillDemoCredentials()">
                        <p class="text-xs text-white opacity-80 text-center mb-2">Demo Credentials (Click to fill):</p>
                        <div class="text-xs text-white opacity-90 text-center space-y-1">
                            <div><strong>Email:</strong> <EMAIL></div>
                            <div><strong>Password:</strong> password123</div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="mt-8 text-center">
                        <p class="text-xs text-white opacity-60">
                            © {{ date('Y') }} GCMS. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.classList.remove('fa-eye');
                passwordIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                passwordIcon.classList.remove('fa-eye-slash');
                passwordIcon.classList.add('fa-eye');
            }
        }

        function fillDemoCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password123';
        }

        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const button = document.querySelector('button[type="submit"]');
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Signing In...';
            button.disabled = true;
        });
    </script>
</body>
</html>