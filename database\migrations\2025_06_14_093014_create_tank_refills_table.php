<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tank_refills', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->foreignId('tank_id')->constrained();
            //$table->unsignedBigInteger('supplier_id')->nullable();
            $table->timestamp('scheduled_date');
            $table->enum('status', ['pending', 'completed', 'canceled'])->default('pending');
            $table->decimal('quantity', 10, 2);
            $table->timestamps();

            // $table->index(['supplier_id', 'status']);
        });

        // Optional: add foreign key in separate call if needed
        // Schema::table('tank_refills', function (Blueprint $table) {
        //     $table->foreign('supplier_id')->references('id')->on('suppliers');
        // });
    }

    public function down(): void
    {
        Schema::dropIfExists('tank_refills');
    }
};