<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Financial Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                @can('view_financial')
                    <a href="{{ route('financial.invoices') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        📄 Invoices
                    </a>
                    <a href="{{ route('financial.payments') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        💰 Payments
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Financial Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($statistics['total_invoices']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Invoices</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                            ${{ number_format($statistics['total_revenue'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            ${{ number_format($statistics['paid_amount'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Paid Amount</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                            ${{ number_format($statistics['outstanding_amount'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Outstanding</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            ${{ number_format($statistics['overdue_amount'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Overdue</div>
                    </div>
                </div>
            </div>

            <!-- Aging Report -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Accounts Receivable Aging
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div class="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                            <div class="text-lg font-bold text-green-800 dark:text-green-200">
                                ${{ number_format($agingReport['current'], 2) }}
                            </div>
                            <div class="text-sm text-green-600 dark:text-green-400">Current</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                            <div class="text-lg font-bold text-yellow-800 dark:text-yellow-200">
                                ${{ number_format($agingReport['1_30_days'], 2) }}
                            </div>
                            <div class="text-sm text-yellow-600 dark:text-yellow-400">1-30 Days</div>
                        </div>
                        <div class="text-center p-4 bg-orange-50 dark:bg-orange-900 rounded-lg">
                            <div class="text-lg font-bold text-orange-800 dark:text-orange-200">
                                ${{ number_format($agingReport['31_60_days'], 2) }}
                            </div>
                            <div class="text-sm text-orange-600 dark:text-orange-400">31-60 Days</div>
                        </div>
                        <div class="text-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                            <div class="text-lg font-bold text-red-800 dark:text-red-200">
                                ${{ number_format($agingReport['61_90_days'], 2) }}
                            </div>
                            <div class="text-sm text-red-600 dark:text-red-400">61-90 Days</div>
                        </div>
                        <div class="text-center p-4 bg-red-100 dark:bg-red-800 rounded-lg">
                            <div class="text-lg font-bold text-red-900 dark:text-red-100">
                                ${{ number_format($agingReport['over_90_days'], 2) }}
                            </div>
                            <div class="text-sm text-red-700 dark:text-red-300">Over 90 Days</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Invoices Alert -->
            @if($overdueInvoices->count() > 0)
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Overdue Invoices Alert
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            You have {{ $overdueInvoices->count() }} overdue invoices totaling ${{ number_format($overdueInvoices->sum('outstanding_amount'), 2) }}
                        </div>
                    </div>
                </div>
                <div class="max-h-48 overflow-y-auto">
                    @foreach($overdueInvoices->take(5) as $invoice)
                        <div class="flex justify-between items-center py-2 border-b border-red-200 dark:border-red-700 last:border-b-0">
                            <div>
                                <span class="font-medium text-red-800 dark:text-red-200">{{ $invoice->invoice_number }}</span>
                                <span class="text-red-600 dark:text-red-400">- {{ $invoice->customer->name }}</span>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-red-800 dark:text-red-200">
                                    ${{ number_format($invoice->outstanding_amount, 2) }}
                                </div>
                                <div class="text-xs text-red-600 dark:text-red-400">
                                    {{ $invoice->getDaysOverdue() }} days overdue
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                @if($overdueInvoices->count() > 5)
                    <div class="mt-4">
                        <a href="{{ route('financial.invoices', ['filter' => 'overdue']) }}" 
                           class="text-red-600 hover:text-red-800 text-sm font-medium">
                            View all {{ $overdueInvoices->count() }} overdue invoices →
                        </a>
                    </div>
                @endif
            </div>
            @endif

            <!-- Recent Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Invoices -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Recent Invoices
                            </h3>
                            <a href="{{ route('financial.invoices') }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($recentInvoices as $invoice)
                                @php $statusLabel = $invoice->getStatusLabel(); @endphp
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $invoice->invoice_number }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $invoice->customer->name }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            ${{ number_format($invoice->total_amount, 2) }}
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                            {{ $statusLabel['label'] }}
                                        </span>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No recent invoices
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Recent Payments
                            </h3>
                            <a href="{{ route('financial.payments') }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($recentPayments as $payment)
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $payment->payment_number }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $payment->customer->name ?? $payment->invoice->customer->name }}
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ $payment->getPaymentMethodLabel() }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium {{ $payment->isRefund() ? 'text-red-600' : 'text-green-600' }}">
                                            {{ $payment->isRefund() ? '-' : '' }}${{ number_format(abs($payment->amount), 2) }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $payment->payment_date->format('M d, Y') }}
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No recent payments
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
