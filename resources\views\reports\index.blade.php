<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Reports & Analytics') }}
            </h2>
            <div class="flex space-x-2">
                <button onclick="generateReport('business')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    📊 Business Report
                </button>
                <button onclick="openExportModal()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    📤 Export Reports
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Report Generation Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Business Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">📈</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Business Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Comprehensive overview of business performance, growth metrics, and key insights.
                        </p>
                        <button onclick="generateReport('business')" 
                                class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Generate Report
                        </button>
                    </div>
                </div>

                <!-- Sales Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">💰</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Sales Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Detailed sales analysis, revenue trends, and product performance metrics.
                        </p>
                        <button onclick="generateReport('sales')" 
                                class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            Generate Report
                        </button>
                    </div>
                </div>

                <!-- Inventory Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">📦</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Inventory Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Stock levels, utilization rates, and inventory optimization insights.
                        </p>
                        <button onclick="generateReport('inventory')" 
                                class="w-full bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                            Generate Report
                        </button>
                    </div>
                </div>

                <!-- Financial Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">💳</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Financial Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Revenue analysis, payment tracking, and accounts receivable insights.
                        </p>
                        <button onclick="generateReport('financial')" 
                                class="w-full bg-emerald-500 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded">
                            Generate Report
                        </button>
                    </div>
                </div>

                <!-- Customer Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">👥</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Customer Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Customer behavior, retention rates, and satisfaction analytics.
                        </p>
                        <button onclick="generateReport('customer')" 
                                class="w-full bg-pink-500 hover:bg-pink-700 text-white font-bold py-2 px-4 rounded">
                            Generate Report
                        </button>
                    </div>
                </div>

                <!-- Custom Report -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="text-3xl mr-3">⚙️</div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Custom Report
                            </h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Build custom reports with specific metrics and date ranges.
                        </p>
                        <button onclick="openCustomReportModal()" 
                                class="w-full bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                            Build Custom
                        </button>
                    </div>
                </div>
            </div>

            <!-- Report Display Area -->
            <div id="reportDisplay" class="hidden bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 id="reportTitle" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Report Results
                        </h3>
                        <div class="flex space-x-2">
                            <button onclick="exportCurrentReport('csv')" 
                                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm">
                                Export CSV
                            </button>
                            <button onclick="exportCurrentReport('pdf')" 
                                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm">
                                Export PDF
                            </button>
                            <button onclick="closeReport()" 
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded text-sm">
                                Close
                            </button>
                        </div>
                    </div>
                    <div id="reportContent" class="space-y-6">
                        <!-- Report content will be dynamically loaded here -->
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                    <div class="mt-3 text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mt-4">Generating Report...</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Please wait while we compile your data.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Export Report</h3>
                <form id="exportForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Report Type</label>
                        <select name="report_type" required 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="business">Business Report</option>
                            <option value="sales">Sales Report</option>
                            <option value="inventory">Inventory Report</option>
                            <option value="financial">Financial Report</option>
                            <option value="customer">Customer Report</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Format</label>
                        <select name="format" required 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="csv">CSV</option>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <input type="date" name="start_date" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <input type="date" name="end_date" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Locations</label>
                        <select name="location_ids[]" multiple 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @foreach($accessibleLocations as $location)
                                <option value="{{ $location->id }}">{{ $location->name }}</option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple locations</p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeExportModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Export Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentReportData = null;
        let currentReportType = null;

        // Generate report
        async function generateReport(reportType) {
            showLoading();
            currentReportType = reportType;
            
            try {
                const response = await fetch(`/reports/${reportType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        start_date: getDefaultStartDate(),
                        end_date: getDefaultEndDate()
                    })
                });

                const data = await response.json();
                currentReportData = data;
                
                displayReport(data, reportType);
                hideLoading();
            } catch (error) {
                console.error('Report generation failed:', error);
                alert('Failed to generate report. Please try again.');
                hideLoading();
            }
        }

        // Display report
        function displayReport(data, reportType) {
            const reportDisplay = document.getElementById('reportDisplay');
            const reportTitle = document.getElementById('reportTitle');
            const reportContent = document.getElementById('reportContent');
            
            reportTitle.textContent = `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`;
            reportContent.innerHTML = formatReportContent(data, reportType);
            
            reportDisplay.classList.remove('hidden');
            reportDisplay.scrollIntoView({ behavior: 'smooth' });
        }

        // Format report content
        function formatReportContent(data, reportType) {
            let html = '';
            
            // Report info
            if (data.report_info) {
                html += `
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Report Information</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            Generated: ${new Date(data.report_info.generated_at).toLocaleString()}<br>
                            Period: ${data.report_info.period.days} days<br>
                            Locations: ${data.report_info.locations.join(', ')}
                        </p>
                    </div>
                `;
            }
            
            // Format each section
            Object.keys(data).forEach(section => {
                if (section === 'report_info') return;
                
                html += `
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                            ${section.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h4>
                        ${formatSectionContent(data[section])}
                    </div>
                `;
            });
            
            return html;
        }

        // Format section content
        function formatSectionContent(content) {
            if (typeof content !== 'object') {
                return `<p class="text-gray-700 dark:text-gray-300">${content}</p>`;
            }
            
            let html = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
            
            Object.keys(content).forEach(key => {
                const value = content[key];
                html += `
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                        <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
                            ${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            ${typeof value === 'number' ? value.toLocaleString() : value}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // Export current report
        function exportCurrentReport(format) {
            if (!currentReportData || !currentReportType) {
                alert('No report to export. Please generate a report first.');
                return;
            }
            
            const params = new URLSearchParams({
                report_type: currentReportType,
                format: format,
                start_date: getDefaultStartDate(),
                end_date: getDefaultEndDate()
            });
            
            window.open(`/reports/export?${params.toString()}`, '_blank');
        }

        // Modal functions
        function openExportModal() {
            document.getElementById('exportModal').classList.remove('hidden');
        }

        function closeExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        function openCustomReportModal() {
            alert('Custom report builder coming soon!');
        }

        function closeReport() {
            document.getElementById('reportDisplay').classList.add('hidden');
        }

        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
        }

        // Helper functions
        function getDefaultStartDate() {
            const date = new Date();
            date.setDate(date.getDate() - 30);
            return date.toISOString().split('T')[0];
        }

        function getDefaultEndDate() {
            return new Date().toISOString().split('T')[0];
        }

        // Export form handler
        document.getElementById('exportForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }
            
            window.open(`/reports/export?${params.toString()}`, '_blank');
            closeExportModal();
        });
    </script>
</x-app-layout>
