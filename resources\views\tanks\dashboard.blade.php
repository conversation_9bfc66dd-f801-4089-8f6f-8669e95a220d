<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Tank Monitoring Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                @can('view_tanks')
                    <a href="{{ route('tanks.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        🛢️ All Tanks
                    </a>
                @endcan
                @can('manage_refills')
                    <a href="{{ route('tanks.refills.index') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        🚛 Refills
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Tank Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($statistics['total_tanks']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Tanks</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($statistics['active_tanks']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Active</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($statistics['critical_tanks']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Critical</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                            {{ number_format($statistics['tanks_needing_refill']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Need Refill</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {{ number_format($statistics['average_fill_percentage'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Avg Fill Level</div>
                    </div>
                </div>
            </div>

            <!-- Alert Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($alertsStats['active_alerts']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Active Alerts</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-red-800 dark:text-red-300">
                            {{ number_format($alertsStats['critical_alerts']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Critical</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-red-900 dark:text-red-200">
                            {{ number_format($alertsStats['emergency_alerts']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Emergency</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-yellow-600 dark:text-yellow-400">
                            {{ number_format($alertsStats['overdue_alerts']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Overdue</div>
                    </div>
                </div>
            </div>

            <!-- Critical Tanks Alert -->
            @if($criticalTanks->count() > 0)
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Critical Tank Levels Alert
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            {{ $criticalTanks->count() }} tanks are at critical levels and require immediate attention
                        </div>
                    </div>
                </div>
                <div class="max-h-48 overflow-y-auto">
                    @foreach($criticalTanks->take(5) as $tank)
                        <div class="flex justify-between items-center py-2 border-b border-red-200 dark:border-red-700 last:border-b-0">
                            <div>
                                <span class="font-medium text-red-800 dark:text-red-200">{{ $tank->tank_number }}</span>
                                <span class="text-red-600 dark:text-red-400">- {{ $tank->gasType->name }}</span>
                                <span class="text-red-500 dark:text-red-500">@ {{ $tank->location->name }}</span>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-red-800 dark:text-red-200">
                                    {{ number_format($tank->current_level, 1) }} {{ $tank->getUnitLabel() }}
                                </div>
                                <div class="text-xs text-red-600 dark:text-red-400">
                                    {{ number_format($tank->getCurrentLevelPercentage(), 1) }}% full
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                @if($criticalTanks->count() > 5)
                    <div class="mt-4">
                        <a href="{{ route('tanks.index', ['filter' => 'critical']) }}" 
                           class="text-red-600 hover:text-red-800 text-sm font-medium">
                            View all {{ $criticalTanks->count() }} critical tanks →
                        </a>
                    </div>
                @endif
            </div>
            @endif

            <!-- Active Alerts -->
            @if($activeAlerts->count() > 0)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Active Alerts
                        </h3>
                        <a href="{{ route('tanks.alerts.index') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm">
                            View All →
                        </a>
                    </div>
                    <div class="space-y-3">
                        @foreach($activeAlerts as $alert)
                            @php $severityLabel = $alert->getSeverityLabel(); @endphp
                            <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-{{ $severityLabel['color'] }}-500">
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ $alert->getAlertTypeLabel() }}
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $alert->tank->tank_number }} - {{ $alert->tank->gasType->name }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-500">
                                        {{ $alert->message }}
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $severityLabel['color'] }}-100 text-{{ $severityLabel['color'] }}-800">
                                        {{ $severityLabel['label'] }}
                                    </span>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        {{ $alert->triggered_at->diffForHumans() }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Dashboard Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Tanks Needing Refill -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Tanks Needing Refill
                            </h3>
                            <a href="{{ route('tanks.index', ['filter' => 'needs_refill']) }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($tanksNeedingRefill->take(5) as $tank)
                                @php $levelStatus = $tank->getLevelStatus(); @endphp
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $tank->tank_number }}
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ $tank->gasType->name }} @ {{ $tank->location->name }}
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                            <div class="bg-{{ $levelStatus['color'] }}-600 h-2 rounded-full" 
                                                 style="width: {{ $levelStatus['percentage'] }}%"></div>
                                        </div>
                                    </div>
                                    <div class="text-right ml-4">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ number_format($tank->current_level, 1) }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $tank->getUnitLabel() }}
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $levelStatus['color'] }}-100 text-{{ $levelStatus['color'] }}-800">
                                            {{ $levelStatus['status'] }}
                                        </span>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No tanks need refill at this time
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Upcoming Refills -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Upcoming Refills
                            </h3>
                            <a href="{{ route('tanks.refills.index') }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($upcomingRefills as $refill)
                                @php $statusLabel = $refill->getStatusLabel(); @endphp
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $refill->tank->tank_number }}
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ $refill->supplier->name ?? 'No supplier' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-500">
                                            {{ number_format($refill->requested_quantity, 1) }} {{ $refill->tank->getUnitLabel() }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $refill->scheduled_date->format('M d') }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $refill->scheduled_date->format('H:i') }}
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                            {{ $statusLabel['label'] }}
                                        </span>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No upcoming refills scheduled
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);

        // Real-time updates for critical alerts
        function checkCriticalAlerts() {
            fetch('/tanks/alerts/check')
                .then(response => response.json())
                .then(data => {
                    if (data.critical_alerts > 0) {
                        // Show notification or update UI
                        console.log('Critical alerts detected:', data.critical_alerts);
                    }
                })
                .catch(error => console.error('Error checking alerts:', error));
        }

        // Check for critical alerts every minute
        setInterval(checkCriticalAlerts, 60000);
    </script>
</x-app-layout>
