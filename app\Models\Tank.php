<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Tank extends Model
{
    use HasFactory;

    protected $fillable = [
        'tank_number',
        'location_id',
        'gas_type_id',
        'supplier_id',
        'tank_type',
        'capacity',
        'current_level',
        'unit_of_measurement',
        'min_level',
        'max_level',
        'reorder_level',
        'critical_level',
        'status',
        'installation_date',
        'last_refill_date',
        'next_maintenance_date',
        'last_inspection_date',
        'pressure_rating',
        'temperature',
        'pressure',
        'valve_status',
        'safety_systems',
        'compliance_certificate',
        'certificate_expiry',
        'notes',
        'sensor_id',
        'auto_refill_enabled',
        'refill_threshold',
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'current_level' => 'decimal:2',
        'min_level' => 'decimal:2',
        'max_level' => 'decimal:2',
        'reorder_level' => 'decimal:2',
        'critical_level' => 'decimal:2',
        'pressure_rating' => 'decimal:2',
        'temperature' => 'decimal:2',
        'pressure' => 'decimal:2',
        'refill_threshold' => 'decimal:2',
        'installation_date' => 'date',
        'last_refill_date' => 'date',
        'next_maintenance_date' => 'date',
        'last_inspection_date' => 'date',
        'certificate_expiry' => 'date',
        'safety_systems' => 'array',
        'auto_refill_enabled' => 'boolean',
    ];

    /**
     * Tank types
     */
    const TANK_TYPES = [
        'storage' => 'Storage Tank',
        'transport' => 'Transport Tank',
        'bulk' => 'Bulk Tank',
        'cylinder_bank' => 'Cylinder Bank',
        'manifold' => 'Manifold System',
    ];

    /**
     * Tank statuses
     */
    const STATUSES = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'maintenance' => 'Under Maintenance',
        'inspection' => 'Under Inspection',
        'empty' => 'Empty',
        'full' => 'Full',
        'critical' => 'Critical Level',
        'out_of_service' => 'Out of Service',
    ];

    /**
     * Units of measurement
     */
    const UNITS = [
        'liters' => 'Liters',
        'gallons' => 'Gallons',
        'cubic_meters' => 'Cubic Meters',
        'kilograms' => 'Kilograms',
        'pounds' => 'Pounds',
        'percentage' => 'Percentage',
    ];

    /**
     * Valve statuses
     */
    const VALVE_STATUSES = [
        'open' => 'Open',
        'closed' => 'Closed',
        'partially_open' => 'Partially Open',
        'maintenance' => 'Under Maintenance',
    ];

    /**
     * Get the location for this tank
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the gas type for this tank
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the supplier for this tank
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get tank readings
     */
    public function readings(): HasMany
    {
        return $this->hasMany(TankReading::class);
    }

    /**
     * Get tank refills
     */
    public function refills(): HasMany
    {
        return $this->hasMany(TankRefill::class);
    }

    /**
     * Get tank maintenance records
     */
    public function maintenanceRecords(): HasMany
    {
        return $this->hasMany(TankMaintenance::class);
    }

    /**
     * Get tank alerts
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(TankAlert::class);
    }

    /**
     * Generate unique tank number
     */
    public static function generateTankNumber(): string
    {
        $prefix = 'TNK-' . date('Ymd') . '-';
        $lastTank = static::where('tank_number', 'like', $prefix . '%')
                          ->orderBy('id', 'desc')
                          ->first();

        if ($lastTank) {
            $lastNumber = (int) substr($lastTank->tank_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get tank type label
     */
    public function getTankTypeLabel(): string
    {
        return self::TANK_TYPES[$this->tank_type] ?? ucfirst(str_replace('_', ' ', $this->tank_type));
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'active' => ['label' => 'Active', 'color' => 'green'],
            'inactive' => ['label' => 'Inactive', 'color' => 'gray'],
            'maintenance' => ['label' => 'Under Maintenance', 'color' => 'yellow'],
            'inspection' => ['label' => 'Under Inspection', 'color' => 'blue'],
            'empty' => ['label' => 'Empty', 'color' => 'red'],
            'full' => ['label' => 'Full', 'color' => 'green'],
            'critical' => ['label' => 'Critical Level', 'color' => 'red'],
            'out_of_service' => ['label' => 'Out of Service', 'color' => 'red'],
            default => ['label' => ucfirst(str_replace('_', ' ', $this->status)), 'color' => 'gray']
        };
    }

    /**
     * Get unit of measurement label
     */
    public function getUnitLabel(): string
    {
        return self::UNITS[$this->unit_of_measurement] ?? ucfirst(str_replace('_', ' ', $this->unit_of_measurement));
    }

    /**
     * Calculate current level percentage
     */
    public function getCurrentLevelPercentage(): float
    {
        if ($this->capacity <= 0) {
            return 0;
        }

        return ($this->current_level / $this->capacity) * 100;
    }

    /**
     * Check if tank needs refill
     */
    public function needsRefill(): bool
    {
        return $this->current_level <= $this->reorder_level;
    }

    /**
     * Check if tank is at critical level
     */
    public function isCritical(): bool
    {
        return $this->current_level <= $this->critical_level;
    }

    /**
     * Check if tank is empty
     */
    public function isEmpty(): bool
    {
        return $this->current_level <= $this->min_level;
    }

    /**
     * Check if tank is full
     */
    public function isFull(): bool
    {
        return $this->current_level >= $this->max_level;
    }

    /**
     * Get remaining capacity
     */
    public function getRemainingCapacity(): float
    {
        return $this->capacity - $this->current_level;
    }

    /**
     * Get level status with color
     */
    public function getLevelStatus(): array
    {
        $percentage = $this->getCurrentLevelPercentage();

        if ($this->isCritical()) {
            return ['status' => 'Critical', 'color' => 'red', 'percentage' => $percentage];
        } elseif ($this->needsRefill()) {
            return ['status' => 'Low', 'color' => 'yellow', 'percentage' => $percentage];
        } elseif ($this->isFull()) {
            return ['status' => 'Full', 'color' => 'green', 'percentage' => $percentage];
        } else {
            return ['status' => 'Normal', 'color' => 'blue', 'percentage' => $percentage];
        }
    }

    /**
     * Check if maintenance is due
     */
    public function isMaintenanceDue(): bool
    {
        return $this->next_maintenance_date && $this->next_maintenance_date->isPast();
    }

    /**
     * Check if inspection is due
     */
    public function isInspectionDue(): bool
    {
        return $this->last_inspection_date &&
               $this->last_inspection_date->addMonths(6)->isPast(); // 6 months inspection cycle
    }

    /**
     * Check if certificate is expired
     */
    public function isCertificateExpired(): bool
    {
        return $this->certificate_expiry && $this->certificate_expiry->isPast();
    }

    /**
     * Update tank level
     */
    public function updateLevel(float $newLevel, string $source = 'manual'): bool
    {
        if ($newLevel < 0 || $newLevel > $this->capacity) {
            return false;
        }

        $oldLevel = $this->current_level;
        $this->current_level = $newLevel;

        // Update status based on level
        if ($this->isCritical()) {
            $this->status = 'critical';
        } elseif ($this->isEmpty()) {
            $this->status = 'empty';
        } elseif ($this->isFull()) {
            $this->status = 'full';
        } else {
            $this->status = 'active';
        }

        $this->save();

        // Create reading record
        TankReading::create([
            'tank_id' => $this->id,
            'reading_value' => $newLevel,
            'reading_type' => 'level',
            'unit' => $this->unit_of_measurement,
            'source' => $source,
            'recorded_at' => now(),
            'recorded_by' => auth()->id(),
        ]);

        // Check for alerts
        $this->checkAndCreateAlerts($oldLevel, $newLevel);

        return true;
    }

    /**
     * Schedule refill
     */
    public function scheduleRefill(array $refillData): TankRefill
    {
        return TankRefill::create([
            'tank_id' => $this->id,
            'supplier_id' => $refillData['supplier_id'] ?? $this->supplier_id,
            'scheduled_date' => $refillData['scheduled_date'],
            'requested_quantity' => $refillData['requested_quantity'],
            'status' => 'scheduled',
            'priority' => $this->isCritical() ? 'urgent' : 'normal',
            'notes' => $refillData['notes'] ?? null,
            'requested_by' => auth()->id(),
        ]);
    }

    /**
     * Check and create alerts
     */
    private function checkAndCreateAlerts(float $oldLevel, float $newLevel): void
    {
        // Critical level alert
        if ($newLevel <= $this->critical_level && $oldLevel > $this->critical_level) {
            TankAlert::create([
                'tank_id' => $this->id,
                'alert_type' => 'critical_level',
                'severity' => 'critical',
                'message' => "Tank {$this->tank_number} has reached critical level: {$newLevel} {$this->getUnitLabel()}",
                'triggered_at' => now(),
                'status' => 'active',
            ]);
        }

        // Low level alert
        if ($newLevel <= $this->reorder_level && $oldLevel > $this->reorder_level) {
            TankAlert::create([
                'tank_id' => $this->id,
                'alert_type' => 'low_level',
                'severity' => 'warning',
                'message' => "Tank {$this->tank_number} needs refill: {$newLevel} {$this->getUnitLabel()}",
                'triggered_at' => now(),
                'status' => 'active',
            ]);
        }

        // Auto-refill trigger
        if ($this->auto_refill_enabled && $newLevel <= $this->refill_threshold) {
            $this->triggerAutoRefill();
        }
    }

    /**
     * Trigger automatic refill
     */
    private function triggerAutoRefill(): void
    {
        // Check if there's already a pending refill
        $pendingRefill = $this->refills()->whereIn('status', ['scheduled', 'in_progress'])->first();

        if (!$pendingRefill) {
            $this->scheduleRefill([
                'scheduled_date' => now()->addDays(1),
                'requested_quantity' => $this->capacity - $this->current_level,
                'notes' => 'Auto-scheduled refill due to low level',
            ]);
        }
    }

    /**
     * Scope for tanks needing refill
     */
    public function scopeNeedsRefill($query)
    {
        return $query->whereRaw('current_level <= reorder_level');
    }

    /**
     * Scope for critical tanks
     */
    public function scopeCritical($query)
    {
        return $query->whereRaw('current_level <= critical_level');
    }

    /**
     * Scope for active tanks
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for tanks at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for tanks with maintenance due
     */
    public function scopeMaintenanceDue($query)
    {
        return $query->where('next_maintenance_date', '<=', now());
    }

    /**
     * Scope for tanks with expired certificates
     */
    public function scopeCertificateExpired($query)
    {
        return $query->where('certificate_expiry', '<=', now());
    }
}
