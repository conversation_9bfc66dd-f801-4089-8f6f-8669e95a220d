<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->constrained();
            $table->foreignId('gas_type_id')->constrained();
            $table->enum('movement_type', ['in', 'out', 'transfer_in', 'transfer_out', 'adjustment', 'damaged', 'expired']);
            $table->integer('quantity');
            $table->string('reference_type')->nullable(); // order, transfer, adjustment, etc.
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->foreignId('cylinder_id')->nullable()->constrained();
            $table->foreignId('user_id')->constrained();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['location_id', 'gas_type_id', 'created_at']);
            $table->index(['movement_type', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
