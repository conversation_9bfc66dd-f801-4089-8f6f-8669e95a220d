<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Customer;
use App\Models\Cylinder;
use App\Models\Tank;
use App\Models\TankAlert;
use App\Models\Location;
use App\Models\Invoice;
use App\Services\QRCodeService;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MobileController extends Controller
{
    protected $qrCodeService;
    protected $analyticsService;

    public function __construct(QRCodeService $qrCodeService, AnalyticsService $analyticsService)
    {
        $this->middleware('auth');
        $this->qrCodeService = $qrCodeService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * Mobile dashboard
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        // Get mobile-optimized dashboard data
        $dashboardData = [
            'quick_stats' => $this->getQuickStats($accessibleLocationIds),
            'recent_activities' => $this->getRecentActivities($accessibleLocationIds),
            'urgent_alerts' => $this->getUrgentAlerts($accessibleLocationIds),
            'today_tasks' => $this->getTodayTasks($accessibleLocationIds),
        ];

        if ($request->expectsJson()) {
            return response()->json($dashboardData);
        }

        return view('mobile.dashboard', compact('dashboardData', 'user'));
    }

    /**
     * Mobile QR scanner
     */
    public function qrScanner(Request $request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'scanner_config' => [
                    'formats' => ['QR_CODE', 'CODE_128', 'CODE_39'],
                    'camera' => 'environment',
                    'torch' => true,
                    'zoom' => true,
                ]
            ]);
        }

        return view('mobile.qr-scanner');
    }

    /**
     * Process QR scan
     */
    public function processQRScan(Request $request)
    {
        $request->validate([
            'qr_data' => 'required|string',
            'scan_type' => 'nullable|string|in:cylinder,customer,order,tank',
        ]);

        try {
            $result = $this->qrCodeService->processQRCode($request->qr_data, $request->scan_type);

            return response()->json([
                'success' => true,
                'data' => $result,
                'actions' => $this->getAvailableActions($result),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mobile customer search
     */
    public function customerSearch(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = Customer::whereIn('location_id', $accessibleLocationIds);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('customer_code', 'like', "%{$search}%");
            });
        }

        $customers = $query->with(['location'])
                          ->orderBy('name')
                          ->limit(20)
                          ->get()
                          ->map(function ($customer) {
                              return [
                                  'id' => $customer->id,
                                  'name' => $customer->name,
                                  'phone' => $customer->phone,
                                  'email' => $customer->email,
                                  'customer_code' => $customer->customer_code,
                                  'location' => $customer->location->name,
                                  'status' => $customer->status,
                                  'avatar' => $customer->getAvatarUrl(),
                              ];
                          });

        if ($request->expectsJson()) {
            return response()->json($customers);
        }

        return view('mobile.customer-search', compact('customers'));
    }

    /**
     * Mobile customer details
     */
    public function customerDetails(Customer $customer, Request $request)
    {
        $user = Auth::user();

        // Check access
        if (!$user->hasLocationAccess($customer->location_id)) {
            abort(403, 'Access denied');
        }

        $customer->load(['location', 'orders' => function ($query) {
            $query->latest()->limit(5);
        }, 'rentals' => function ($query) {
            $query->latest()->limit(5);
        }]);

        $customerData = [
            'customer' => $customer,
            'recent_orders' => $customer->orders,
            'active_rentals' => $customer->rentals->where('status', 'active'),
            'outstanding_invoices' => $customer->invoices()->unpaid()->get(),
            'quick_actions' => $this->getCustomerQuickActions($customer),
        ];

        if ($request->expectsJson()) {
            return response()->json($customerData);
        }

        return view('mobile.customer-details', compact('customerData'));
    }

    /**
     * Mobile tank monitoring
     */
    public function tankMonitoring(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $tanks = Tank::whereIn('location_id', $accessibleLocationIds)
                    ->with(['gasType', 'location'])
                    ->get()
                    ->map(function ($tank) {
                        $levelStatus = $tank->getLevelStatus();
                        return [
                            'id' => $tank->id,
                            'tank_number' => $tank->tank_number,
                            'gas_type' => $tank->gasType->name,
                            'location' => $tank->location->name,
                            'current_level' => $tank->current_level,
                            'capacity' => $tank->capacity,
                            'percentage' => $levelStatus['percentage'],
                            'status' => $levelStatus['status'],
                            'color' => $levelStatus['color'],
                            'unit' => $tank->getUnitLabel(),
                            'is_critical' => $tank->isCritical(),
                            'needs_refill' => $tank->needsRefill(),
                        ];
                    });

        if ($request->expectsJson()) {
            return response()->json($tanks);
        }

        return view('mobile.tank-monitoring', compact('tanks'));
    }

    /**
     * Mobile order creation
     */
    public function createOrder(Request $request)
    {
        if ($request->isMethod('POST')) {
            $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'location_id' => 'required|exists:locations,id',
                'delivery_type' => 'required|in:delivery,pickup',
                'items' => 'required|array|min:1',
                'items.*.gas_type_id' => 'required|exists:gas_types,id',
                'items.*.quantity' => 'required|integer|min:1',
                'delivery_address' => 'required_if:delivery_type,delivery|string',
                'delivery_date' => 'required|date|after_or_equal:today',
                'notes' => 'nullable|string|max:1000',
            ]);

            try {
                $order = Order::create([
                    'order_number' => Order::generateOrderNumber(),
                    'customer_id' => $request->customer_id,
                    'location_id' => $request->location_id,
                    'delivery_type' => $request->delivery_type,
                    'delivery_address' => $request->delivery_address,
                    'delivery_date' => $request->delivery_date,
                    'status' => 'pending',
                    'notes' => $request->notes,
                    'created_by' => Auth::id(),
                ]);

                // Add order items
                foreach ($request->items as $item) {
                    $order->items()->create([
                        'gas_type_id' => $item['gas_type_id'],
                        'quantity' => $item['quantity'],
                        'unit_price' => 0, // Will be calculated later
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully',
                    'order' => $order->load(['customer', 'items.gasType']),
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], 400);
            }
        }

        // GET request - return form data
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $formData = [
            'locations' => Location::whereIn('id', $accessibleLocationIds)->get(),
            'gas_types' => \App\Models\GasType::active()->get(),
        ];

        if ($request->expectsJson()) {
            return response()->json($formData);
        }

        return view('mobile.create-order', compact('formData'));
    }

    /**
     * Mobile offline page
     */
    public function offline()
    {
        return view('mobile.offline');
    }

    /**
     * Get quick stats for mobile dashboard
     */
    protected function getQuickStats($locationIds): array
    {
        return [
            'pending_orders' => Order::whereIn('location_id', $locationIds)
                                   ->where('status', 'pending')
                                   ->count(),
            'active_rentals' => \App\Models\Rental::whereIn('location_id', $locationIds)
                                                 ->where('status', 'active')
                                                 ->count(),
            'critical_tanks' => Tank::whereIn('location_id', $locationIds)
                                   ->critical()
                                   ->count(),
            'overdue_invoices' => Invoice::whereIn('location_id', $locationIds)
                                        ->overdue()
                                        ->count(),
        ];
    }

    /**
     * Get recent activities
     */
    protected function getRecentActivities($locationIds): array
    {
        $activities = collect();

        // Recent orders
        $recentOrders = Order::whereIn('location_id', $locationIds)
                            ->with(['customer'])
                            ->latest()
                            ->limit(5)
                            ->get()
                            ->map(function ($order) {
                                return [
                                    'type' => 'order',
                                    'title' => "Order #{$order->order_number}",
                                    'subtitle' => $order->customer->name,
                                    'time' => $order->created_at->diffForHumans(),
                                    'status' => $order->status,
                                    'url' => "/orders/{$order->id}",
                                ];
                            });

        $activities = $activities->merge($recentOrders);

        return $activities->sortByDesc('time')->take(10)->values()->toArray();
    }

    /**
     * Get urgent alerts
     */
    protected function getUrgentAlerts($locationIds): array
    {
        return TankAlert::whereHas('tank', function ($query) use ($locationIds) {
                        $query->whereIn('location_id', $locationIds);
                    })
                    ->whereIn('severity', ['critical', 'emergency'])
                    ->where('status', 'active')
                    ->with(['tank'])
                    ->latest()
                    ->limit(5)
                    ->get()
                    ->map(function ($alert) {
                        return [
                            'id' => $alert->id,
                            'type' => $alert->alert_type,
                            'message' => $alert->message,
                            'severity' => $alert->severity,
                            'tank' => $alert->tank->tank_number,
                            'time' => $alert->triggered_at->diffForHumans(),
                        ];
                    })
                    ->toArray();
    }

    /**
     * Get today's tasks
     */
    protected function getTodayTasks($locationIds): array
    {
        $tasks = collect();

        // Scheduled deliveries
        $deliveries = Order::whereIn('location_id', $locationIds)
                          ->where('delivery_date', today())
                          ->where('status', 'confirmed')
                          ->with(['customer'])
                          ->get()
                          ->map(function ($order) {
                              return [
                                  'type' => 'delivery',
                                  'title' => "Delivery to {$order->customer->name}",
                                  'time' => $order->delivery_date->format('H:i'),
                                  'priority' => 'normal',
                                  'url' => "/orders/{$order->id}",
                              ];
                          });

        $tasks = $tasks->merge($deliveries);

        return $tasks->sortBy('time')->values()->toArray();
    }

    /**
     * Get available actions for QR scan result
     */
    protected function getAvailableActions($result): array
    {
        $actions = [];

        switch ($result['type']) {
            case 'cylinder':
                $actions = [
                    ['label' => 'View Details', 'action' => 'view_cylinder', 'icon' => '👁️'],
                    ['label' => 'Update Status', 'action' => 'update_status', 'icon' => '🔄'],
                    ['label' => 'Assign to Order', 'action' => 'assign_order', 'icon' => '📦'],
                ];
                break;
            case 'customer':
                $actions = [
                    ['label' => 'View Profile', 'action' => 'view_customer', 'icon' => '👤'],
                    ['label' => 'Create Order', 'action' => 'create_order', 'icon' => '➕'],
                    ['label' => 'View Orders', 'action' => 'view_orders', 'icon' => '📋'],
                ];
                break;
        }

        return $actions;
    }

    /**
     * Get customer quick actions
     */
    protected function getCustomerQuickActions($customer): array
    {
        return [
            ['label' => 'Call', 'action' => 'call', 'icon' => '📞', 'url' => "tel:{$customer->phone}"],
            ['label' => 'WhatsApp', 'action' => 'whatsapp', 'icon' => '💬', 'url' => "https://wa.me/{$customer->phone}"],
            ['label' => 'New Order', 'action' => 'new_order', 'icon' => '➕', 'url' => "/mobile/orders/create?customer_id={$customer->id}"],
            ['label' => 'View Orders', 'action' => 'view_orders', 'icon' => '📋', 'url' => "/customers/{$customer->id}/orders"],
        ];
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }
}
