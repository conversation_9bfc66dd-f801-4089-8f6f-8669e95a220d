<?php $__env->startSection('title', 'Database Configuration'); ?>

<?php $__env->startSection('content'); ?>
<div class="px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-database text-2xl text-blue-600"></i>
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Database Configuration</h2>
        <p class="text-gray-600">Configure your MySQL database connection for GCMS</p>
    </div>

    <!-- Alerts Container -->
    <div id="alerts-container"></div>

    <!-- Database Configuration Form -->
    <div class="max-w-2xl mx-auto">
        <form id="database-form" class="space-y-6">
            <?php echo csrf_field(); ?>
            
            <!-- Database Host -->
            <div>
                <label for="db_host" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-server mr-2"></i>Database Host
                </label>
                <input type="text" 
                       id="db_host" 
                       name="db_host" 
                       value="127.0.0.1"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="127.0.0.1 or localhost"
                       required>
                <p class="text-sm text-gray-500 mt-1">Usually 127.0.0.1 or localhost for local installations</p>
            </div>

            <!-- Database Port -->
            <div>
                <label for="db_port" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-plug mr-2"></i>Database Port
                </label>
                <input type="number" 
                       id="db_port" 
                       name="db_port" 
                       value="3306"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="3306"
                       required>
                <p class="text-sm text-gray-500 mt-1">Default MySQL port is 3306</p>
            </div>

            <!-- Database Name -->
            <div>
                <label for="db_name" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-database mr-2"></i>Database Name
                </label>
                <input type="text" 
                       id="db_name" 
                       name="db_name" 
                       value="gcms_database"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="gcms_database"
                       required>
                <p class="text-sm text-gray-500 mt-1">The database must already exist in MySQL</p>
            </div>

            <!-- Database Username -->
            <div>
                <label for="db_username" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user mr-2"></i>Database Username
                </label>
                <input type="text" 
                       id="db_username" 
                       name="db_username" 
                       value="root"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="root"
                       required>
                <p class="text-sm text-gray-500 mt-1">MySQL username with full privileges to the database</p>
            </div>

            <!-- Database Password -->
            <div>
                <label for="db_password" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock mr-2"></i>Database Password
                </label>
                <div class="relative">
                    <input type="password" 
                           id="db_password" 
                           name="db_password" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-12"
                           placeholder="Enter database password">
                    <button type="button" 
                            onclick="togglePassword('db_password')"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="db_password_icon"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mt-1">Leave empty if no password is set</p>
            </div>

            <!-- Test Connection Button -->
            <div class="bg-gray-50 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Test Database Connection</h3>
                        <p class="text-sm text-gray-600">Verify your database credentials before proceeding</p>
                    </div>
                    <button type="button" 
                            id="test-connection-btn"
                            onclick="testDatabaseConnection()"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-plug mr-2"></i>
                        Test Connection
                    </button>
                </div>
                <div id="connection-result" class="mt-4 hidden"></div>
            </div>
        </form>

        <!-- Database Setup Instructions -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">
                <i class="fas fa-info-circle mr-2"></i>
                Database Setup Instructions
            </h3>
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">1. Create Database:</h4>
                    <div class="bg-blue-100 rounded p-3">
                        <code class="text-sm text-blue-800">
                            mysql -u root -p<br>
                            CREATE DATABASE gcms_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;<br>
                            GRANT ALL PRIVILEGES ON gcms_database.* TO 'root'@'localhost';<br>
                            FLUSH PRIVILEGES;<br>
                            EXIT;
                        </code>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">2. Verify Connection:</h4>
                    <div class="bg-blue-100 rounded p-3">
                        <code class="text-sm text-blue-800">
                            mysql -u root -p gcms_database<br>
                            SHOW TABLES; # Should show empty result for new database
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
        <a href="<?php echo e(route('install.permissions')); ?>" 
           class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <a href="<?php echo e(route('install.database.setup')); ?>" 
           id="continue-btn"
           class="inline-flex items-center px-6 py-2 bg-gray-300 text-gray-500 font-medium rounded-lg cursor-not-allowed"
           style="pointer-events: none;">
            Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let connectionTested = false;

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye text-gray-400 hover:text-gray-600';
    }
}

function testDatabaseConnection() {
    const btn = $('#test-connection-btn');
    const resultDiv = $('#connection-result');
    
    // Show loading state
    showLoading(btn);
    resultDiv.removeClass('hidden');
    
    // Get form data
    const formData = {
        db_host: $('#db_host').val(),
        db_port: $('#db_port').val(),
        db_name: $('#db_name').val(),
        db_username: $('#db_username').val(),
        db_password: $('#db_password').val(),
        _token: $('input[name="_token"]').val()
    };
    
    // Test connection
    $.ajax({
        url: '<?php echo e(route("install.test.database")); ?>',
        method: 'POST',
        data: formData,
        success: function(response) {
            hideLoading(btn);
            
            if (response.success) {
                resultDiv.html(`
                    <div class="flex items-center p-3 bg-green-100 border border-green-200 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 mr-3"></i>
                        <div>
                            <p class="text-green-800 font-medium">Connection Successful!</p>
                            <p class="text-green-700 text-sm">Database configuration saved. You can now proceed to the next step.</p>
                        </div>
                    </div>
                `);
                
                // Enable continue button
                connectionTested = true;
                const continueBtn = $('#continue-btn');
                continueBtn.removeClass('bg-gray-300 text-gray-500 cursor-not-allowed')
                          .addClass('bg-blue-600 hover:bg-blue-700 text-white')
                          .css('pointer-events', 'auto');
                
                showAlert('Database connection successful! Configuration saved.', 'success');
            } else {
                resultDiv.html(`
                    <div class="flex items-center p-3 bg-red-100 border border-red-200 rounded-lg">
                        <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                        <div>
                            <p class="text-red-800 font-medium">Connection Failed</p>
                            <p class="text-red-700 text-sm">${response.message}</p>
                        </div>
                    </div>
                `);
                
                showAlert('Database connection failed: ' + response.message, 'error');
            }
        },
        error: function(xhr) {
            hideLoading(btn);
            
            let errorMessage = 'An error occurred while testing the connection.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            resultDiv.html(`
                <div class="flex items-center p-3 bg-red-100 border border-red-200 rounded-lg">
                    <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                    <div>
                        <p class="text-red-800 font-medium">Connection Error</p>
                        <p class="text-red-700 text-sm">${errorMessage}</p>
                    </div>
                </div>
            `);
            
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                showValidationErrors(xhr.responseJSON.errors);
            } else {
                showAlert('Database connection error: ' + errorMessage, 'error');
            }
        }
    });
}

// Auto-fill common database configurations
$(document).ready(function() {
    // Add some helpful presets
    $('#db_name').on('blur', function() {
        if ($(this).val() === '') {
            $(this).val('gcms_database');
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('installation.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\GCMS\resources\views/installation/database.blade.php ENDPATH**/ ?>