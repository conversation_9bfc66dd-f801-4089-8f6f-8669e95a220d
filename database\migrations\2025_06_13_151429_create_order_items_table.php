<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('gas_type_id')->constrained();
            $table->integer('quantity');
            $table->decimal('rate', 10, 2);
            $table->integer('rental_days')->nullable();
            $table->json('cylinder_ids')->nullable(); // Array of assigned cylinder IDs
            $table->decimal('subtotal', 10, 2);
            $table->timestamps();

            $table->index(['order_id', 'gas_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
