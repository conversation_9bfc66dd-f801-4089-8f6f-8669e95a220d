<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whats_app_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('template_code')->unique();
            $table->enum('category', ['marketing', 'utility', 'authentication', 'order_update', 'delivery_update', 'payment_reminder', 'emergency_alert', 'maintenance_alert', 'appointment_reminder']);
            $table->string('language', 10)->default('en');
            $table->enum('status', ['draft', 'pending_approval', 'approved', 'rejected', 'active', 'inactive'])->default('draft');
            $table->enum('header_type', ['none', 'text', 'image', 'video', 'document'])->default('none');
            $table->text('header_content')->nullable();
            $table->text('body_content');
            $table->text('footer_content')->nullable();
            $table->enum('button_type', ['none', 'call_to_action', 'quick_reply', 'url', 'phone'])->default('none');
            $table->json('button_content')->nullable();
            $table->json('variables')->nullable();
            $table->enum('use_case', [
                'order_confirmation', 'order_status_update', 'delivery_notification', 'payment_reminder',
                'appointment_reminder', 'tank_refill_alert', 'maintenance_reminder', 'emergency_alert',
                'welcome_message', 'promotional_offer'
            ])->nullable();
            $table->json('trigger_events')->nullable();
            $table->boolean('is_active')->default(true);
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->string('whatsapp_template_id')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['category', 'status']);
            $table->index(['use_case', 'is_active']);
            $table->index(['status', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whats_app_templates');
    }
};
