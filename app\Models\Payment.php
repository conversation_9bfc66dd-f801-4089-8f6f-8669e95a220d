<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_number',
        'invoice_id',
        'customer_id',
        'amount',
        'payment_method',
        'payment_reference',
        'payment_date',
        'status',
        'gateway_transaction_id',
        'gateway_response',
        'notes',
        'processed_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'datetime',
        'gateway_response' => 'array',
    ];

    /**
     * Payment methods
     */
    const PAYMENT_METHODS = [
        'cash' => 'Cash',
        'card' => 'Credit/Debit Card',
        'bank_transfer' => 'Bank Transfer',
        'check' => 'Check',
        'digital_wallet' => 'Digital Wallet',
        'refund' => 'Refund',
    ];

    /**
     * Payment statuses
     */
    const STATUSES = [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
        'refunded' => 'Refunded',
    ];

    /**
     * Get the invoice for this payment
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the customer for this payment
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who processed this payment
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Generate unique payment number
     */
    public static function generatePaymentNumber(): string
    {
        $prefix = 'PAY-' . date('Ymd') . '-';
        $lastPayment = static::where('payment_number', 'like', $prefix . '%')
                            ->orderBy('id', 'desc')
                            ->first();

        if ($lastPayment) {
            $lastNumber = (int) substr($lastPayment->payment_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get payment method label
     */
    public function getPaymentMethodLabel(): string
    {
        return self::PAYMENT_METHODS[$this->payment_method] ?? ucfirst(str_replace('_', ' ', $this->payment_method));
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'processing' => ['label' => 'Processing', 'color' => 'blue'],
            'completed' => ['label' => 'Completed', 'color' => 'green'],
            'failed' => ['label' => 'Failed', 'color' => 'red'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'gray'],
            'refunded' => ['label' => 'Refunded', 'color' => 'purple'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Check if payment is refund
     */
    public function isRefund(): bool
    {
        return $this->amount < 0 || $this->payment_method === 'refund';
    }

    /**
     * Mark payment as completed
     */
    public function markAsCompleted(): bool
    {
        if ($this->status !== 'pending' && $this->status !== 'processing') {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'processed_by' => auth()->id(),
        ]);

        return true;
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed($reason = null): bool
    {
        if ($this->status === 'completed') {
            return false;
        }

        $this->update([
            'status' => 'failed',
            'notes' => $reason,
        ]);

        return true;
    }

    /**
     * Scope for completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for refunds
     */
    public function scopeRefunds($query)
    {
        return $query->where('amount', '<', 0)
                    ->orWhere('payment_method', 'refund');
    }

    /**
     * Auto-generate payment number before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (!$payment->payment_number) {
                $payment->payment_number = static::generatePaymentNumber();
            }
        });
    }
}
