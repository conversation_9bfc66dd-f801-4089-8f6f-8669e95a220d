<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsAppTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'template_code',
        'category',
        'language',
        'status',
        'header_type',
        'header_content',
        'body_content',
        'footer_content',
        'button_type',
        'button_content',
        'variables',
        'use_case',
        'trigger_events',
        'is_active',
        'approval_status',
        'whatsapp_template_id',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'variables' => 'array',
        'trigger_events' => 'array',
        'button_content' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Template categories
     */
    const CATEGORIES = [
        'marketing' => 'Marketing',
        'utility' => 'Utility',
        'authentication' => 'Authentication',
        'order_update' => 'Order Update',
        'delivery_update' => 'Delivery Update',
        'payment_reminder' => 'Payment Reminder',
        'emergency_alert' => 'Emergency Alert',
        'maintenance_alert' => 'Maintenance Alert',
        'appointment_reminder' => 'Appointment Reminder',
    ];

    /**
     * Template statuses
     */
    const STATUSES = [
        'draft' => 'Draft',
        'pending_approval' => 'Pending Approval',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'active' => 'Active',
        'inactive' => 'Inactive',
    ];

    /**
     * Header types
     */
    const HEADER_TYPES = [
        'none' => 'None',
        'text' => 'Text',
        'image' => 'Image',
        'video' => 'Video',
        'document' => 'Document',
    ];

    /**
     * Button types
     */
    const BUTTON_TYPES = [
        'none' => 'None',
        'call_to_action' => 'Call to Action',
        'quick_reply' => 'Quick Reply',
        'url' => 'URL Button',
        'phone' => 'Phone Button',
    ];

    /**
     * Languages
     */
    const LANGUAGES = [
        'en' => 'English',
        'hi' => 'Hindi',
        'es' => 'Spanish',
        'fr' => 'French',
        'de' => 'German',
        'pt' => 'Portuguese',
        'ar' => 'Arabic',
    ];

    /**
     * Use cases
     */
    const USE_CASES = [
        'order_confirmation' => 'Order Confirmation',
        'order_status_update' => 'Order Status Update',
        'delivery_notification' => 'Delivery Notification',
        'payment_reminder' => 'Payment Reminder',
        'appointment_reminder' => 'Appointment Reminder',
        'tank_refill_alert' => 'Tank Refill Alert',
        'maintenance_reminder' => 'Maintenance Reminder',
        'emergency_alert' => 'Emergency Alert',
        'welcome_message' => 'Welcome Message',
        'promotional_offer' => 'Promotional Offer',
    ];

    /**
     * Get messages sent using this template
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'template_id');
    }

    /**
     * Get the user who created this template
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get category label
     */
    public function getCategoryLabel(): string
    {
        return self::CATEGORIES[$this->category] ?? ucfirst(str_replace('_', ' ', $this->category));
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'draft' => ['label' => 'Draft', 'color' => 'gray'],
            'pending_approval' => ['label' => 'Pending Approval', 'color' => 'yellow'],
            'approved' => ['label' => 'Approved', 'color' => 'green'],
            'rejected' => ['label' => 'Rejected', 'color' => 'red'],
            'active' => ['label' => 'Active', 'color' => 'green'],
            'inactive' => ['label' => 'Inactive', 'color' => 'gray'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get header type label
     */
    public function getHeaderTypeLabel(): string
    {
        return self::HEADER_TYPES[$this->header_type] ?? ucfirst(str_replace('_', ' ', $this->header_type));
    }

    /**
     * Get button type label
     */
    public function getButtonTypeLabel(): string
    {
        return self::BUTTON_TYPES[$this->button_type] ?? ucfirst(str_replace('_', ' ', $this->button_type));
    }

    /**
     * Get use case label
     */
    public function getUseCaseLabel(): string
    {
        return self::USE_CASES[$this->use_case] ?? ucfirst(str_replace('_', ' ', $this->use_case));
    }

    /**
     * Replace variables in template content
     */
    public function replaceVariables(array $data): array
    {
        $content = [
            'header' => $this->header_content,
            'body' => $this->body_content,
            'footer' => $this->footer_content,
        ];

        foreach ($content as $key => $text) {
            if ($text) {
                foreach ($data as $variable => $value) {
                    $text = str_replace("{{$variable}}", $value, $text);
                }
                $content[$key] = $text;
            }
        }

        return $content;
    }

    /**
     * Get template variables from content
     */
    public function extractVariables(): array
    {
        $variables = [];
        $content = $this->body_content . ' ' . $this->header_content . ' ' . $this->footer_content;

        preg_match_all('/\{\{(\w+)\}\}/', $content, $matches);

        if (!empty($matches[1])) {
            $variables = array_unique($matches[1]);
        }

        return $variables;
    }

    /**
     * Check if template can be triggered by event
     */
    public function canBeTriggeredBy(string $event): bool
    {
        return in_array($event, $this->trigger_events ?? []);
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    /**
     * Scope for specific category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for specific use case
     */
    public function scopeUseCase($query, $useCase)
    {
        return $query->where('use_case', $useCase);
    }

    /**
     * Scope for templates that can be triggered by event
     */
    public function scopeTriggeredBy($query, $event)
    {
        return $query->whereJsonContains('trigger_events', $event);
    }
}
