<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Customer;
use App\Models\Location;
use App\Services\FinancialService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FinancialController extends Controller
{
    protected $financialService;

    public function __construct(FinancialService $financialService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_financial')->only(['dashboard', 'invoices', 'payments']);
        $this->middleware('permission:manage_financial')->only(['processPayment', 'processRefund']);
        $this->financialService = $financialService;
    }

    /**
     * Financial dashboard
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $days = $request->get('days', 30);
        $statistics = $this->financialService->getFinancialStatistics($accessibleLocationIds, $days);
        $agingReport = $this->financialService->getAgingReport($accessibleLocationIds);

        // Get recent invoices and payments
        $recentInvoices = Invoice::with(['customer', 'location'])
                                ->whereIn('location_id', $accessibleLocationIds)
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        $recentPayments = Payment::with(['invoice.customer', 'customer'])
                                ->whereHas('invoice', function ($q) use ($accessibleLocationIds) {
                                    $q->whereIn('location_id', $accessibleLocationIds);
                                })
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        // Get overdue invoices
        $overdueInvoices = Invoice::with(['customer', 'location'])
                                 ->whereIn('location_id', $accessibleLocationIds)
                                 ->overdue()
                                 ->orderBy('due_date', 'asc')
                                 ->get();

        return view('financial.dashboard', compact(
            'statistics',
            'agingReport',
            'recentInvoices',
            'recentPayments',
            'overdueInvoices'
        ));
    }

    /**
     * Invoice management
     */
    public function invoices(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = Invoice::with(['customer', 'location']);

        // Apply location filtering
        $query->whereIn('location_id', $accessibleLocationIds);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('customer')) {
            $query->where('customer_id', $request->customer);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'overdue') {
            $query->overdue();
        } elseif ($request->filter === 'pending') {
            $query->pending();
        } elseif ($request->filter === 'paid') {
            $query->paid();
        }

        $invoices = $query->orderBy('created_at', 'desc')->paginate(20);

        $customers = Customer::active()->get();
        $locations = $this->getAccessibleLocations($user);

        return view('financial.invoices.index', compact('invoices', 'customers', 'locations'));
    }

    /**
     * Show invoice details
     */
    public function showInvoice(Invoice $invoice)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($invoice->location_id)) {
            abort(403, 'You do not have access to this invoice.');
        }

        $invoice->load(['items', 'payments', 'customer', 'location', 'order', 'rental']);

        return view('financial.invoices.show', compact('invoice'));
    }

    /**
     * Process payment for invoice
     */
    public function processPayment(Request $request, Invoice $invoice)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($invoice->location_id)) {
            abort(403, 'You do not have access to this invoice.');
        }

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $invoice->outstanding_amount,
            'payment_method' => 'required|in:cash,card,bank_transfer,check,digital_wallet',
            'payment_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $payment = $this->financialService->processPayment($invoice, [
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_reference' => $request->payment_reference,
                'notes' => $request->notes,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully.',
                'payment' => $payment,
                'invoice' => $invoice->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
