<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">

    <title>Customer Search - {{ config('app.name', 'GCMS') }}</title>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        .search-container {
            position: sticky;
            top: 0;
            z-index: 10;
            background: white;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .customer-card {
            transition: all 0.2s ease;
            transform: translateX(0);
        }
        
        .customer-card:active {
            transform: scale(0.98);
            background-color: #f3f4f6;
        }
        
        .search-input {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .loading-skeleton {
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .slide-up {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-blue-600 text-white">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <button onclick="history.back()" class="p-2 rounded-full hover:bg-blue-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                <h1 class="font-semibold text-lg">Customer Search</h1>
            </div>
            <button id="scanBtn" onclick="openQRScanner()" class="p-2 rounded-full hover:bg-blue-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Search Container -->
    <div class="search-container p-4">
        <div class="relative">
            <input 
                type="text" 
                id="searchInput" 
                class="search-input pl-12" 
                placeholder="Search customers by name, phone, or code..."
                autocomplete="off"
            >
            <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
            <button id="clearBtn" class="absolute right-4 top-1/2 transform -translate-y-1/2 hidden" onclick="clearSearch()">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        
        <!-- Search Filters -->
        <div class="flex space-x-2 mt-3 overflow-x-auto pb-2">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="active">Active</button>
            <button class="filter-btn" data-filter="inactive">Inactive</button>
            <button class="filter-btn" data-filter="recent">Recent</button>
        </div>
    </div>

    <!-- Results Container -->
    <div class="p-4 pb-20">
        <!-- Loading State -->
        <div id="loadingState" class="hidden">
            <div class="space-y-3">
                @for($i = 0; $i < 5; $i++)
                <div class="bg-white rounded-lg p-4 loading-skeleton">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div class="flex-1">
                            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
                @endfor
            </div>
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="text-center py-12">
            <div class="text-6xl mb-4">🔍</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Search for Customers</h3>
            <p class="text-gray-600 text-sm">Enter a name, phone number, or customer code to find customers</p>
        </div>

        <!-- No Results State -->
        <div id="noResultsState" class="text-center py-12 hidden">
            <div class="text-6xl mb-4">😔</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Customers Found</h3>
            <p class="text-gray-600 text-sm">Try adjusting your search terms or filters</p>
        </div>

        <!-- Results List -->
        <div id="resultsList" class="space-y-3">
            <!-- Results will be populated here -->
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="grid grid-cols-5 py-2">
            <a href="/mobile/dashboard" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
                <span class="text-xs mt-1">Dashboard</span>
            </a>
            
            <a href="/mobile/qr-scanner" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                </svg>
                <span class="text-xs mt-1">Scan</span>
            </a>
            
            <a href="/mobile/customers/search" class="flex flex-col items-center py-2 text-blue-600">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <span class="text-xs mt-1">Customers</span>
            </a>
            
            <a href="/mobile/tanks" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                <span class="text-xs mt-1">Tanks</span>
            </a>
            
            <a href="/mobile/menu" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
                <span class="text-xs mt-1">Menu</span>
            </a>
        </div>
    </nav>

    <style>
        .filter-btn {
            background: #f3f4f6;
            color: #6b7280;
            border: none;
            border-radius: 20px;
            padding: 6px 16px;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            transition: all 0.2s ease;
        }
        
        .filter-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        .filter-btn:hover {
            background: #e5e7eb;
        }
        
        .filter-btn.active:hover {
            background: #2563eb;
        }
    </style>

    <script>
        let searchTimeout;
        let currentFilter = 'all';
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.getElementById('clearBtn');
            
            // Search input handling
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                
                if (query.length > 0) {
                    clearBtn.classList.remove('hidden');
                } else {
                    clearBtn.classList.add('hidden');
                }
                
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            });
            
            // Filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                    performSearch(searchInput.value.trim());
                });
            });
            
            // Focus search input
            searchInput.focus();
        });
        
        async function performSearch(query) {
            const loadingState = document.getElementById('loadingState');
            const emptyState = document.getElementById('emptyState');
            const noResultsState = document.getElementById('noResultsState');
            const resultsList = document.getElementById('resultsList');
            
            // Hide all states
            loadingState.classList.add('hidden');
            emptyState.classList.add('hidden');
            noResultsState.classList.add('hidden');
            resultsList.innerHTML = '';
            
            if (query.length === 0) {
                emptyState.classList.remove('hidden');
                return;
            }
            
            // Show loading
            loadingState.classList.remove('hidden');
            
            try {
                const response = await fetch(`/mobile/customers/search?search=${encodeURIComponent(query)}&filter=${currentFilter}`, {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const customers = await response.json();
                loadingState.classList.add('hidden');
                
                if (customers.length === 0) {
                    noResultsState.classList.remove('hidden');
                } else {
                    displayResults(customers);
                }
            } catch (error) {
                console.error('Search failed:', error);
                loadingState.classList.add('hidden');
                noResultsState.classList.remove('hidden');
            }
        }
        
        function displayResults(customers) {
            const resultsList = document.getElementById('resultsList');
            
            resultsList.innerHTML = customers.map((customer, index) => `
                <div class="customer-card bg-white rounded-lg p-4 shadow-sm slide-up" 
                     style="animation-delay: ${index * 0.1}s"
                     onclick="viewCustomer(${customer.id})">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-lg">
                                ${customer.name.charAt(0).toUpperCase()}
                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-gray-900 truncate">${customer.name}</h3>
                                <span class="status-badge ${customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} text-xs px-2 py-1 rounded-full">
                                    ${customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                                </span>
                            </div>
                            <p class="text-gray-600 text-sm truncate">${customer.phone}</p>
                            <div class="flex items-center justify-between mt-1">
                                <p class="text-gray-500 text-xs">${customer.customer_code}</p>
                                <p class="text-gray-500 text-xs">${customer.location}</p>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function viewCustomer(customerId) {
            window.location.href = `/mobile/customers/${customerId}`;
        }
        
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.getElementById('clearBtn');
            
            searchInput.value = '';
            clearBtn.classList.add('hidden');
            
            document.getElementById('emptyState').classList.remove('hidden');
            document.getElementById('resultsList').innerHTML = '';
            document.getElementById('noResultsState').classList.add('hidden');
            
            searchInput.focus();
        }
        
        function openQRScanner() {
            window.location.href = '/mobile/qr-scanner';
        }
    </script>
</body>
</html>
