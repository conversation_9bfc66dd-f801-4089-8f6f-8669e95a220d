<?php

namespace App\Http\Controllers;

use App\Models\Cylinder;
use App\Models\Customer;
use App\Models\GasType;
use App\Models\Location;
use App\Models\Order;
use App\Models\Rental;
use App\Models\Invoice;
use App\Models\Tank;
use App\Models\TankAlert;
use App\Models\WhatsAppMessage;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    protected $analyticsService;

    // Removed middleware enforcement for testing environment
    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Display the advanced analytics dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $dateRange = $request->get('date_range', 30);

        // Get user's accessible locations
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();

        // Get dashboard data for the new UI
        $dashboardData = $this->getSimpleDashboardData($locationIds);

        // Get comprehensive analytics
        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        // Get real-time data
        $realTimeData = $this->getRealTimeData($locationIds);

        // Use the new dashboard view
        return view('dashboard', compact(
            'dashboardData',
            'analytics',
            'realTimeData',
            'accessibleLocations',
            'user',
            'dateRange'
        ));
    }

    /**
     * Get simplified dashboard data for the new UI
     */
    private function getSimpleDashboardData($locationIds)
    {
        // Basic counts
        $totalCylinders = Cylinder::when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))->count();
        $activeRentals = Rental::where('status', 'active')
                              ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                              ->count();
        $pendingOrders = Order::where('status', 'pending')
                             ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                             ->count();

        // Revenue calculation
        $monthlyRevenue = Invoice::where('status', 'paid')
                                ->whereMonth('created_at', now()->month)
                                ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                ->sum('total_amount');

        // Cylinder status breakdown
        $cylinderStats = [
            'cylinders_available' => Cylinder::where('status', 'available')
                                           ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                           ->count(),
            'cylinders_rented' => Cylinder::where('status', 'rented')
                                        ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                        ->count(),
            'cylinders_maintenance' => Cylinder::where('status', 'maintenance')
                                             ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                             ->count(),
            'cylinders_overdue' => Rental::where('status', 'active')
                                        ->where('return_date', '<', now())
                                        ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                        ->count(),
        ];

        // Tank levels
        $tankLevels = Tank::select('name', 'current_level', 'capacity', 'min_level')
                         ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                         ->get()
                         ->map(function ($tank) {
                             $percentage = $tank->capacity > 0 ? round(($tank->current_level / $tank->capacity) * 100) : 0;
                             return [
                                 'name' => $tank->name,
                                 'level' => $percentage,
                                 'status' => $this->getTankStatus($tank->current_level, $tank->capacity, $tank->min_level ?? 0)
                             ];
                         })
                         ->take(5);

        // Recent orders
        $recentOrders = Order::with('customer')
                            ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                            ->latest()
                            ->take(5)
                            ->get()
                            ->map(function ($order) {
                                return [
                                    'order_number' => $order->order_number,
                                    'customer_name' => $order->customer->name ?? 'Unknown',
                                    'total' => $order->total_amount,
                                    'status' => $order->status,
                                    'created_at' => $order->created_at->format('M j, Y')
                                ];
                            });

        return array_merge([
            'total_cylinders' => $totalCylinders,
            'active_rentals' => $activeRentals,
            'pending_orders' => $pendingOrders,
            'monthly_revenue' => $monthlyRevenue,
            'tank_levels' => $tankLevels,
            'recent_orders' => $recentOrders,
        ], $cylinderStats);
    }

    /**
     * Get tank status based on level
     */
    private function getTankStatus($currentLevel, $capacity, $minLevel)
    {
        if ($capacity == 0) return 'unknown';

        $percentage = ($currentLevel / $capacity) * 100;
        $minPercentage = ($minLevel / $capacity) * 100;

        if ($percentage <= $minPercentage) return 'critical';
        if ($percentage <= 30) return 'low';
        if ($percentage <= 70) return 'medium';
        return 'good';
    }

    /**
     * Get real-time dashboard data
     */
    public function getRealTimeData($locationIds = null): array
    {
        return [
            'active_orders' => Order::whereIn('status', ['pending', 'confirmed', 'in_progress'])
                                  ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                  ->count(),
            'active_rentals' => Rental::where('status', 'active')
                                    ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                    ->count(),
            'critical_tanks' => Tank::critical()
                                  ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                  ->count(),
            'active_alerts' => TankAlert::active()
                                      ->when($locationIds, fn($q) => $q->whereHas('tank', function($query) use ($locationIds) {
                                          $query->whereIn('location_id', $locationIds);
                                      }))
                                      ->count(),
            'pending_messages' => WhatsAppMessage::pending()->count(),
            'overdue_invoices' => Invoice::overdue()
                                        ->when($locationIds, fn($q) => $q->whereIn('location_id', $locationIds))
                                        ->count(),
        ];
    }

    /**
     * Get appropriate dashboard view based on user role
     */
    private function getDashboardView($user)
    {
        $role = $user->getPrimaryRole();

        return match($role) {
            'super_admin', 'admin' => 'dashboard.admin',
            'auditor' => 'dashboard.auditor',
            'location_manager' => 'dashboard.location-manager',
            'staff' => 'dashboard.staff',
            'customer' => 'dashboard.customer',
            default => 'dashboard'
        };
    }

    /**
     * Get locations accessible to the user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        if ($user->hasRole('location_manager')) {
            return $user->managedLocations()->active()->get()
                       ->merge($user->locations()->active()->get())
                       ->unique('id');
        }

        return $user->locations()->active()->get();
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats($user, $accessibleLocations)
    {
        $locationIds = $accessibleLocations->pluck('id')->toArray();

        $stats = [
            'total_cylinders' => 0,
            'cylinders_by_status' => [],
            'total_customers' => 0,
            'active_orders' => 0,
            'monthly_revenue' => 0,
            'gas_types' => [],
        ];

        if (!empty($locationIds)) {
            // Cylinder statistics
            $stats['total_cylinders'] = Cylinder::whereIn('location_id', $locationIds)->count();

            $stats['cylinders_by_status'] = Cylinder::whereIn('location_id', $locationIds)
                ->selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Customer statistics
            $stats['total_customers'] = Customer::active()->count();

            // Order statistics
            $stats['active_orders'] = Order::whereIn('location_id', $locationIds)
                ->whereIn('status', ['pending', 'assigned', 'in_progress'])
                ->count();

            // Revenue statistics (current month)
            $stats['monthly_revenue'] = Order::whereIn('location_id', $locationIds)
                ->where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount');

            // Gas type statistics
            $stats['gas_types'] = GasType::active()
                ->withCount(['cylinders' => function ($query) use ($locationIds) {
                    $query->whereIn('location_id', $locationIds);
                }])
                ->get()
                ->map(function ($gasType) use ($locationIds) {
                    return [
                        'name' => $gasType->name,
                        'code' => $gasType->code,
                        'color' => $gasType->getDisplayColor(),
                        'total' => $gasType->cylinders_count,
                        'full' => $gasType->getCylinderCountByStatus('full', $locationIds),
                        'empty' => $gasType->getCylinderCountByStatus('empty', $locationIds),
                        'in_use' => $gasType->getCylinderCountByStatus('in_use', $locationIds),
                        'damaged' => $gasType->getCylinderCountByStatus('damaged', $locationIds),
                    ];
                });
        }

        return $stats;
    }

    /**
     * Get recent orders
     */
    private function getRecentOrders($user, $accessibleLocations)
    {
        $locationIds = $accessibleLocations->pluck('id')->toArray();

        if (empty($locationIds)) {
            return collect();
        }

        return Order::with(['customer', 'location', 'assignedTo'])
            ->whereIn('location_id', $locationIds)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get alerts and notifications
     */
    private function getAlerts($user, $accessibleLocations)
    {
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $alerts = [];

        if (!empty($locationIds)) {
            // Expired cylinders
            $expiredCylinders = Cylinder::whereIn('location_id', $locationIds)
                ->expired()
                ->count();

            if ($expiredCylinders > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Expired Cylinders',
                    'message' => "{$expiredCylinders} cylinders have expired and need attention.",
                    'action_url' => route('cylinders.index', ['filter' => 'expired']),
                ];
            }

            // Cylinders due for inspection
            $inspectionDue = Cylinder::whereIn('location_id', $locationIds)
                ->dueForInspection()
                ->count();

            if ($inspectionDue > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Inspection Due',
                    'message' => "{$inspectionDue} cylinders are due for inspection.",
                    'action_url' => route('cylinders.index', ['filter' => 'inspection_due']),
                ];
            }

            // Pending orders
            $pendingOrders = Order::whereIn('location_id', $locationIds)
                ->where('status', 'pending')
                ->count();

            if ($pendingOrders > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Pending Orders',
                    'message' => "{$pendingOrders} orders are waiting to be assigned.",
                    'action_url' => route('orders.index', ['status' => 'pending']),
                ];
            }
        }

        return collect($alerts);
    }

    /**
     * Get dashboard data for API/AJAX requests
     */
    public function getData(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);
        $realTimeData = $this->getRealTimeData($locationIds);

        return response()->json([
            'analytics' => $analytics,
            'real_time' => $realTimeData,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        return response()->json($analytics['revenue']);
    }

    /**
     * Get operational metrics
     */
    public function getOperationalMetrics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        return response()->json($analytics['operations']);
    }

    /**
     * Get customer analytics
     */
    public function getCustomerAnalytics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        return response()->json($analytics['customer']);
    }

    /**
     * Get inventory analytics
     */
    public function getInventoryAnalytics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds);

        return response()->json($analytics['inventory']);
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        return response()->json($analytics['performance']);
    }

    /**
     * Export analytics data
     */
    public function exportAnalytics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();
        $dateRange = $request->get('date_range', 30);
        $format = $request->get('format', 'json');

        $analytics = $this->analyticsService->getDashboardAnalytics($locationIds, $dateRange);

        switch ($format) {
            case 'csv':
                return $this->exportToCsv($analytics);
            case 'pdf':
                return $this->exportToPdf($analytics);
            default:
                return response()->json($analytics);
        }
    }

    /**
     * Export analytics to CSV
     */
    protected function exportToCsv($analytics)
    {
        $filename = 'analytics_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($analytics) {
            $file = fopen('php://output', 'w');

            // Write headers
            fputcsv($file, ['Metric', 'Value', 'Category']);

            // Write overview data
            foreach ($analytics['overview'] as $key => $value) {
                fputcsv($file, [ucfirst(str_replace('_', ' ', $key)), $value, 'Overview']);
            }

            // Write revenue data
            foreach ($analytics['revenue'] as $key => $value) {
                if (!is_array($value)) {
                    fputcsv($file, [ucfirst(str_replace('_', ' ', $key)), $value, 'Revenue']);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export analytics to PDF (placeholder)
     */
    protected function exportToPdf($analytics)
    {
        // This would typically use a PDF library like DomPDF or TCPDF
        // For now, return JSON with a message
        return response()->json([
            'message' => 'PDF export functionality would be implemented here',
            'data' => $analytics
        ]);
    }
}
