# 🌐 **GCMS Server Installation Guide (No Terminal Access)**

## 🎯 **Complete Web-Based Installation**

This guide helps you install GCMS on your server without needing terminal/SSH access. Everything is done through your web browser.

---

## 📋 **Pre-Installation Checklist**

### **✅ Before You Start:**
1. **Files Uploaded**: All GCMS files uploaded to your server
2. **Domain/Subdomain**: Pointing to the `public` folder
3. **PHP Version**: 8.1 or higher
4. **Database**: MySQL database created
5. **Composer**: Dependencies installed (see below)

---

## 🚀 **Installation Methods**

### **Method 1: If Composer Dependencies Are Installed**

#### **Step 1: Access Web Installer**
Visit: `https://yourdomain.com/server-install.php`

#### **Step 2: Choose Installation Type**
- **🟢 Quick Setup**: Import sample data (recommended for testing)
- **🔵 Manual Setup**: Custom configuration

#### **Step 3: Complete Installation**
- Follow the web-based wizard
- No terminal commands needed
- Everything done through browser

### **Method 2: If Composer Dependencies Are Missing**

#### **Contact Your Hosting Provider**
Ask them to run this command in your GCMS directory:
```bash
composer install --no-dev --optimize-autoloader
```

#### **Alternative: Manual Dependency Upload**
1. Run `composer install` locally
2. Upload the entire `vendor` folder to your server
3. Proceed with Method 1

---

## 🔧 **Server Configuration Requirements**

### **✅ PHP Requirements:**
- **PHP Version**: 8.1 or higher
- **Extensions**: 
  - BCMath, Ctype, Fileinfo, JSON, Mbstring
  - OpenSSL, PDO, Tokenizer, XML, GD
  - MySQL/MySQLi, Curl

### **✅ File Permissions:**
```
storage/ - 755 (writable)
bootstrap/cache/ - 755 (writable)
public/ - 755
```

### **✅ Database:**
- **MySQL**: 5.7+ or 8.0+
- **Database Created**: Empty database ready
- **User Permissions**: CREATE, ALTER, DROP, INSERT, UPDATE, DELETE, SELECT

---

## 🌐 **Web Installation Process**

### **🟢 Quick Setup (Recommended)**

#### **What It Does:**
- ✅ Checks system requirements
- ✅ Runs database migrations
- ✅ Imports comprehensive sample data
- ✅ Creates admin user (<EMAIL> / password123)
- ✅ Configures company settings
- ✅ Completes installation

#### **Sample Data Included:**
- **3 Locations**: Main Warehouse, North Branch, South Branch
- **5 Gas Types**: O2, N2, Ar, CO2, C2H2
- **5 Customers**: Business and individual customers
- **10 Cylinders**: With QR codes and realistic data
- **5 Orders & Rentals**: Sample transactions
- **5 Storage Tanks**: With monitoring data
- **Financial Data**: Invoices, payments, settings

#### **Time**: ~2-3 minutes

### **🔵 Manual Setup (Custom)**

#### **Step-by-Step Process:**
1. **Requirements Check**: System validation
2. **Database Setup**: Run migrations and seeders
3. **Admin Account**: Create your admin user
4. **Company Info**: Enter your company details
5. **Configuration**: System settings
6. **Completion**: Finalize installation

#### **Time**: ~10-15 minutes

---

## 📁 **File Structure After Upload**

### **✅ Required Files on Server:**
```
your-domain.com/
├── public/
│   ├── index.php
│   ├── server-install.php ← Installation file
│   └── ... (other public files)
├── app/
├── config/
├── database/
├── resources/
├── routes/
├── storage/ ← Must be writable
├── vendor/ ← Composer dependencies
├── .env ← Environment configuration
├── artisan
└── composer.json
```

---

## ⚙️ **Environment Configuration**

### **✅ .env File Setup:**
Create/edit `.env` file with your server details:

```env
APP_NAME="Gas Cylinder Management System"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# Email Configuration (Optional)
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
```

---

## 🔐 **Security Considerations**

### **✅ After Installation:**
1. **Delete Installation File**: Remove `public/server-install.php`
2. **Secure .env**: Ensure `.env` is not web-accessible
3. **Update Passwords**: Change default admin password
4. **SSL Certificate**: Enable HTTPS
5. **File Permissions**: Verify correct permissions

### **✅ Production Settings:**
```env
APP_ENV=production
APP_DEBUG=false
```

---

## 🚨 **Troubleshooting**

### **❌ Common Issues:**

#### **"Dependencies not installed"**
- **Solution**: Contact hosting provider to run `composer install`
- **Alternative**: Upload `vendor` folder manually

#### **"Database connection failed"**
- **Check**: Database credentials in `.env`
- **Verify**: Database exists and user has permissions
- **Test**: Database connection from hosting control panel

#### **"Permission denied"**
- **Fix**: Set `storage/` and `bootstrap/cache/` to 755
- **Contact**: Hosting provider for permission assistance

#### **"500 Internal Server Error"**
- **Check**: Error logs in hosting control panel
- **Verify**: PHP version compatibility
- **Ensure**: All files uploaded correctly

### **✅ Getting Help:**
1. **Check Error Logs**: In your hosting control panel
2. **Contact Support**: Your hosting provider
3. **Verify Requirements**: PHP version and extensions

---

## 🎯 **Installation URLs**

### **✅ Access Points:**
- **Installation**: `https://yourdomain.com/server-install.php`
- **After Install**: `https://yourdomain.com/`
- **Login**: `https://yourdomain.com/login`
- **Dashboard**: `https://yourdomain.com/dashboard`

### **✅ Default Credentials (Quick Setup):**
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: Super Administrator

---

## 📊 **Post-Installation**

### **✅ What You'll Have:**
- **Complete GCMS**: Fully functional gas cylinder management
- **User Management**: Role-based access control
- **Inventory System**: Cylinder tracking with QR codes
- **Order Management**: Complete order processing
- **Financial System**: Invoicing and payment tracking
- **Analytics**: Business intelligence dashboard
- **Mobile Ready**: PWA-enabled mobile interface

### **✅ Next Steps:**
1. **Login**: Access your GCMS dashboard
2. **Customize**: Update company information
3. **Add Data**: Start adding your real data
4. **Train Users**: Set up additional user accounts
5. **Go Live**: Start managing your gas cylinders

---

## 🎉 **Success!**

### **🟢 Installation Complete:**
Your GCMS system is now ready for production use! You have a complete gas cylinder management platform with all modern features.

### **🚀 Features Available:**
- ✅ **User Management** with role-based permissions
- ✅ **Customer Database** with complete profiles
- ✅ **Cylinder Inventory** with QR code tracking
- ✅ **Order Processing** with automated workflows
- ✅ **Rental Management** with billing automation
- ✅ **Financial Tracking** with invoicing and payments
- ✅ **Tank Monitoring** with real-time alerts
- ✅ **Analytics Dashboard** with business intelligence
- ✅ **Mobile Interface** with PWA capabilities
- ✅ **Reporting System** with PDF/Excel export

**🎯 Your professional Gas Cylinder Management System is ready to streamline your operations! 🚀**

---

*Server Installation Guide*  
*Version: 1.0*  
*Updated: July 6, 2025*
