<?php

namespace App\Services;

use App\Models\WhatsAppTemplate;
use App\Models\WhatsAppMessage;
use App\Models\WhatsAppContact;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WhatsAppService
{
    protected $apiUrl;
    protected $accessToken;
    protected $phoneNumberId;
    protected $webhookVerifyToken;

    public function __construct()
    {
        $this->apiUrl = config('whatsapp.api_url', 'https://graph.facebook.com/v18.0');
        $this->accessToken = config('whatsapp.access_token');
        $this->phoneNumberId = config('whatsapp.phone_number_id');
        $this->webhookVerifyToken = config('whatsapp.webhook_verify_token');
    }

    /**
     * Send template message
     */
    public function sendTemplateMessage(
        string $phoneNumber, 
        WhatsAppTemplate $template, 
        array $templateData = [],
        array $options = []
    ): WhatsAppMessage {
        DB::beginTransaction();
        
        try {
            // Get or create contact
            $contact = $this->getOrCreateContact($phoneNumber, $options['customer_id'] ?? null);
            
            // Check if contact can receive messages
            if (!$contact->canReceiveMessages()) {
                throw new \Exception("Contact {$phoneNumber} cannot receive messages (blocked or opted out)");
            }

            // Replace template variables
            $content = $template->replaceVariables($templateData);

            // Create message record
            $message = WhatsAppMessage::create([
                'template_id' => $template->id,
                'contact_id' => $contact->id,
                'customer_id' => $options['customer_id'] ?? null,
                'phone_number' => $contact->formatted_number,
                'message_type' => 'template',
                'content' => $content,
                'template_data' => $templateData,
                'status' => 'pending',
                'priority' => $options['priority'] ?? 'normal',
                'scheduled_at' => $options['scheduled_at'] ?? null,
                'triggered_by_event' => $options['triggered_by_event'] ?? null,
                'related_model_type' => $options['related_model_type'] ?? null,
                'related_model_id' => $options['related_model_id'] ?? null,
                'sent_by' => Auth::id(),
            ]);

            // Send immediately if not scheduled
            if (!$message->scheduled_at || $message->scheduled_at->isPast()) {
                $this->sendMessage($message);
            }

            DB::commit();
            return $message;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Send text message
     */
    public function sendTextMessage(
        string $phoneNumber, 
        string $text, 
        array $options = []
    ): WhatsAppMessage {
        DB::beginTransaction();
        
        try {
            // Get or create contact
            $contact = $this->getOrCreateContact($phoneNumber, $options['customer_id'] ?? null);
            
            // Check if contact can receive messages
            if (!$contact->canReceiveMessages()) {
                throw new \Exception("Contact {$phoneNumber} cannot receive messages (blocked or opted out)");
            }

            // Create message record
            $message = WhatsAppMessage::create([
                'contact_id' => $contact->id,
                'customer_id' => $options['customer_id'] ?? null,
                'phone_number' => $contact->formatted_number,
                'message_type' => 'text',
                'content' => ['text' => $text],
                'status' => 'pending',
                'priority' => $options['priority'] ?? 'normal',
                'scheduled_at' => $options['scheduled_at'] ?? null,
                'triggered_by_event' => $options['triggered_by_event'] ?? null,
                'related_model_type' => $options['related_model_type'] ?? null,
                'related_model_id' => $options['related_model_id'] ?? null,
                'sent_by' => Auth::id(),
            ]);

            // Send immediately if not scheduled
            if (!$message->scheduled_at || $message->scheduled_at->isPast()) {
                $this->sendMessage($message);
            }

            DB::commit();
            return $message;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Send message via WhatsApp API
     */
    protected function sendMessage(WhatsAppMessage $message): bool
    {
        try {
            $payload = $this->buildMessagePayload($message);
            
            $response = Http::withToken($this->accessToken)
                          ->post("{$this->apiUrl}/{$this->phoneNumberId}/messages", $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                $message->markAsSent(
                    $responseData['messages'][0]['id'] ?? null,
                    $responseData
                );
                
                // Update contact last message timestamp
                $message->contact->updateLastMessage();
                
                return true;
            } else {
                $errorData = $response->json();
                $message->markAsFailed(
                    $errorData['error']['message'] ?? 'Unknown error',
                    $errorData
                );
                
                Log::error('WhatsApp message failed', [
                    'message_id' => $message->id,
                    'error' => $errorData,
                ]);
                
                return false;
            }
        } catch (\Exception $e) {
            $message->markAsFailed($e->getMessage());
            
            Log::error('WhatsApp message exception', [
                'message_id' => $message->id,
                'exception' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Build message payload for WhatsApp API
     */
    protected function buildMessagePayload(WhatsAppMessage $message): array
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $message->phone_number,
        ];

        if ($message->message_type === 'template' && $message->template) {
            $payload['type'] = 'template';
            $payload['template'] = [
                'name' => $message->template->template_code,
                'language' => ['code' => $message->template->language],
            ];

            // Add template parameters if any
            if ($message->template_data) {
                $components = [];
                
                // Header parameters
                if ($message->template->header_type === 'text' && !empty($message->template_data['header'])) {
                    $components[] = [
                        'type' => 'header',
                        'parameters' => array_map(fn($value) => ['type' => 'text', 'text' => $value], $message->template_data['header'])
                    ];
                }
                
                // Body parameters
                if (!empty($message->template_data['body'])) {
                    $components[] = [
                        'type' => 'body',
                        'parameters' => array_map(fn($value) => ['type' => 'text', 'text' => $value], $message->template_data['body'])
                    ];
                }
                
                if (!empty($components)) {
                    $payload['template']['components'] = $components;
                }
            }
        } else {
            $payload['type'] = 'text';
            $payload['text'] = ['body' => $message->content['text']];
        }

        return $payload;
    }

    /**
     * Get or create WhatsApp contact
     */
    protected function getOrCreateContact(string $phoneNumber, int $customerId = null): WhatsAppContact
    {
        $formattedNumber = WhatsAppContact::formatPhoneNumber($phoneNumber);
        
        $contact = WhatsAppContact::where('formatted_number', $formattedNumber)->first();
        
        if (!$contact) {
            $contactData = [
                'phone_number' => $phoneNumber,
                'formatted_number' => $formattedNumber,
                'status' => 'active',
                'opt_in_status' => 'opted_in', // Default to opted in, can be changed based on business logic
            ];
            
            if ($customerId) {
                $customer = Customer::find($customerId);
                if ($customer) {
                    $contactData['customer_id'] = $customerId;
                    $contactData['name'] = $customer->name;
                }
            }
            
            $contact = WhatsAppContact::create($contactData);
        } elseif ($customerId && !$contact->customer_id) {
            // Link existing contact to customer
            $contact->update(['customer_id' => $customerId]);
        }
        
        return $contact;
    }

    /**
     * Process scheduled messages
     */
    public function processScheduledMessages(): int
    {
        $overdueMessages = WhatsAppMessage::overdue()->limit(100)->get();
        $sentCount = 0;

        foreach ($overdueMessages as $message) {
            if ($this->sendMessage($message)) {
                $sentCount++;
            }
        }

        return $sentCount;
    }

    /**
     * Trigger automated messages based on events
     */
    public function triggerAutomatedMessages(string $event, array $data): int
    {
        $templates = WhatsAppTemplate::active()
                                   ->triggeredBy($event)
                                   ->get();

        $sentCount = 0;

        foreach ($templates as $template) {
            try {
                $this->sendTemplateMessage(
                    $data['phone_number'],
                    $template,
                    $data['template_data'] ?? [],
                    [
                        'customer_id' => $data['customer_id'] ?? null,
                        'triggered_by_event' => $event,
                        'related_model_type' => $data['related_model_type'] ?? null,
                        'related_model_id' => $data['related_model_id'] ?? null,
                        'priority' => $data['priority'] ?? 'normal',
                    ]
                );
                
                $sentCount++;
            } catch (\Exception $e) {
                Log::error('Failed to send automated WhatsApp message', [
                    'event' => $event,
                    'template_id' => $template->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $sentCount;
    }

    /**
     * Handle webhook from WhatsApp
     */
    public function handleWebhook(array $webhookData): bool
    {
        try {
            if (isset($webhookData['entry'])) {
                foreach ($webhookData['entry'] as $entry) {
                    if (isset($entry['changes'])) {
                        foreach ($entry['changes'] as $change) {
                            if ($change['field'] === 'messages') {
                                $this->processMessageStatus($change['value']);
                            }
                        }
                    }
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('WhatsApp webhook processing failed', [
                'webhook_data' => $webhookData,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Process message status updates from webhook
     */
    protected function processMessageStatus(array $statusData): void
    {
        if (isset($statusData['statuses'])) {
            foreach ($statusData['statuses'] as $status) {
                $message = WhatsAppMessage::where('gateway_message_id', $status['id'])->first();
                
                if ($message) {
                    switch ($status['status']) {
                        case 'delivered':
                            $message->markAsDelivered();
                            break;
                        case 'read':
                            $message->markAsRead();
                            break;
                        case 'failed':
                            $message->markAsFailed($status['errors'][0]['title'] ?? 'Delivery failed');
                            break;
                    }
                    
                    // Store webhook data
                    $message->update(['webhook_data' => $status]);
                }
            }
        }
    }

    /**
     * Get WhatsApp statistics
     */
    public function getStatistics($days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $messages = WhatsAppMessage::where('created_at', '>=', $startDate)->get();
        
        return [
            'total_messages' => $messages->count(),
            'sent_messages' => $messages->where('status', 'sent')->count(),
            'delivered_messages' => $messages->where('status', 'delivered')->count(),
            'read_messages' => $messages->where('status', 'read')->count(),
            'failed_messages' => $messages->where('status', 'failed')->count(),
            'template_messages' => $messages->where('message_type', 'template')->count(),
            'text_messages' => $messages->where('message_type', 'text')->count(),
            'total_cost' => $messages->sum('cost'),
            'delivery_rate' => $messages->count() > 0 ? ($messages->whereIn('status', ['delivered', 'read'])->count() / $messages->count()) * 100 : 0,
            'read_rate' => $messages->where('status', 'delivered')->count() > 0 ? ($messages->where('status', 'read')->count() / $messages->where('status', 'delivered')->count()) * 100 : 0,
        ];
    }
}
