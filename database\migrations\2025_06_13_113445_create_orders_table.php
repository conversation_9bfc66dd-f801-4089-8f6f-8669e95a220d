<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('customer_id')->constrained();
            $table->foreignId('location_id')->constrained();
            $table->enum('type', ['sale', 'rental']);
            $table->enum('status', ['pending', 'assigned', 'in_progress', 'delivered', 'completed', 'cancelled']);
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->string('delivery_otp', 6)->nullable();
            $table->json('delivery_proof')->nullable(); // Photos, signatures, etc.
            $table->timestamps();

            $table->index(['status', 'location_id']);
            $table->index(['customer_id', 'status']);
            $table->index('order_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
