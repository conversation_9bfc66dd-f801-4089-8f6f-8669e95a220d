<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class CustomReportService
{
    /**
     * Get available tables for reporting
     */
    public function getAvailableTables(): array
    {
        $tables = [
            'customers' => [
                'label' => 'Customers',
                'description' => 'Customer information and details',
                'fields' => $this->getTableFields('customers')
            ],
            'orders' => [
                'label' => 'Orders',
                'description' => 'Order transactions and details',
                'fields' => $this->getTableFields('orders')
            ],
            'cylinders' => [
                'label' => 'Cylinders',
                'description' => 'Cylinder inventory and tracking',
                'fields' => $this->getTableFields('cylinders')
            ],
            'rentals' => [
                'label' => 'Rentals',
                'description' => 'Rental agreements and billing',
                'fields' => $this->getTableFields('rentals')
            ],
            'invoices' => [
                'label' => 'Invoices',
                'description' => 'Invoice and payment information',
                'fields' => $this->getTableFields('invoices')
            ],
            'tanks' => [
                'label' => 'Tanks',
                'description' => 'Tank levels and monitoring',
                'fields' => $this->getTableFields('tanks')
            ],
        ];

        return $tables;
    }

    /**
     * Build custom report
     */
    public function buildCustomReport(array $config): array
    {
        $query = $this->buildQuery($config);
        $data = $query->get();

        return [
            'config' => $config,
            'data' => $data,
            'summary' => $this->generateSummary($data, $config),
            'generated_at' => now(),
            'total_records' => $data->count(),
        ];
    }

    /**
     * Build SQL query from configuration
     */
    protected function buildQuery(array $config)
    {
        $table = $config['table'];
        $query = DB::table($table);

        // Select fields
        if (!empty($config['fields'])) {
            $query->select($config['fields']);
        }

        // Apply filters
        if (!empty($config['filters'])) {
            foreach ($config['filters'] as $filter) {
                $this->applyFilter($query, $filter);
            }
        }

        // Apply date range
        if (!empty($config['date_range'])) {
            $dateField = $config['date_field'] ?? 'created_at';
            $query->whereBetween($dateField, [
                $config['date_range']['start'],
                $config['date_range']['end']
            ]);
        }

        // Apply sorting
        if (!empty($config['sort'])) {
            $query->orderBy($config['sort']['field'], $config['sort']['direction'] ?? 'asc');
        }

        // Apply grouping
        if (!empty($config['group_by'])) {
            $query->groupBy($config['group_by']);
        }

        // Apply aggregations
        if (!empty($config['aggregations'])) {
            foreach ($config['aggregations'] as $aggregation) {
                $this->applyAggregation($query, $aggregation);
            }
        }

        // Apply limit
        if (!empty($config['limit'])) {
            $query->limit($config['limit']);
        }

        return $query;
    }

    /**
     * Apply filter to query
     */
    protected function applyFilter($query, array $filter): void
    {
        $field = $filter['field'];
        $operator = $filter['operator'];
        $value = $filter['value'];

        switch ($operator) {
            case 'equals':
                $query->where($field, '=', $value);
                break;
            case 'not_equals':
                $query->where($field, '!=', $value);
                break;
            case 'contains':
                $query->where($field, 'like', "%{$value}%");
                break;
            case 'starts_with':
                $query->where($field, 'like', "{$value}%");
                break;
            case 'ends_with':
                $query->where($field, 'like', "%{$value}");
                break;
            case 'greater_than':
                $query->where($field, '>', $value);
                break;
            case 'less_than':
                $query->where($field, '<', $value);
                break;
            case 'between':
                $query->whereBetween($field, [$value['min'], $value['max']]);
                break;
            case 'in':
                $query->whereIn($field, $value);
                break;
            case 'not_in':
                $query->whereNotIn($field, $value);
                break;
            case 'is_null':
                $query->whereNull($field);
                break;
            case 'is_not_null':
                $query->whereNotNull($field);
                break;
        }
    }

    /**
     * Apply aggregation to query
     */
    protected function applyAggregation($query, array $aggregation): void
    {
        $function = $aggregation['function'];
        $field = $aggregation['field'];
        $alias = $aggregation['alias'] ?? "{$function}_{$field}";

        switch ($function) {
            case 'count':
                $query->selectRaw("COUNT({$field}) as {$alias}");
                break;
            case 'sum':
                $query->selectRaw("SUM({$field}) as {$alias}");
                break;
            case 'avg':
                $query->selectRaw("AVG({$field}) as {$alias}");
                break;
            case 'min':
                $query->selectRaw("MIN({$field}) as {$alias}");
                break;
            case 'max':
                $query->selectRaw("MAX({$field}) as {$alias}");
                break;
        }
    }

    /**
     * Get table fields with metadata
     */
    protected function getTableFields(string $table): array
    {
        $columns = Schema::getColumnListing($table);
        $fields = [];

        foreach ($columns as $column) {
            $fields[$column] = [
                'name' => $column,
                'label' => ucfirst(str_replace('_', ' ', $column)),
                'type' => $this->getColumnType($table, $column),
                'filterable' => $this->isFilterable($column),
                'sortable' => true,
                'aggregatable' => $this->isAggregatable($table, $column),
            ];
        }

        return $fields;
    }

    /**
     * Get column type
     */
    protected function getColumnType(string $table, string $column): string
    {
        try {
            $columnType = Schema::getColumnType($table, $column);
            
            // Map database types to report types
            $typeMap = [
                'integer' => 'number',
                'bigint' => 'number',
                'decimal' => 'number',
                'float' => 'number',
                'double' => 'number',
                'boolean' => 'boolean',
                'date' => 'date',
                'datetime' => 'datetime',
                'timestamp' => 'datetime',
                'time' => 'time',
                'text' => 'text',
                'string' => 'text',
            ];

            return $typeMap[$columnType] ?? 'text';
        } catch (\Exception $e) {
            return 'text';
        }
    }

    /**
     * Check if column is filterable
     */
    protected function isFilterable(string $column): bool
    {
        $nonFilterable = ['id', 'created_at', 'updated_at', 'deleted_at'];
        return !in_array($column, $nonFilterable);
    }

    /**
     * Check if column is aggregatable
     */
    protected function isAggregatable(string $table, string $column): bool
    {
        $type = $this->getColumnType($table, $column);
        return in_array($type, ['number']);
    }

    /**
     * Generate report summary
     */
    protected function generateSummary($data, array $config): array
    {
        $summary = [
            'total_records' => $data->count(),
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'table' => $config['table'],
        ];

        // Add field-specific summaries
        if (!empty($config['fields'])) {
            foreach ($config['fields'] as $field) {
                $values = $data->pluck($field)->filter();
                
                if ($values->isNotEmpty()) {
                    $fieldSummary = [
                        'total_values' => $values->count(),
                        'unique_values' => $values->unique()->count(),
                    ];

                    // Add numeric summaries
                    if ($values->every(function ($value) {
                        return is_numeric($value);
                    })) {
                        $fieldSummary['sum'] = $values->sum();
                        $fieldSummary['average'] = $values->avg();
                        $fieldSummary['min'] = $values->min();
                        $fieldSummary['max'] = $values->max();
                    }

                    $summary['fields'][$field] = $fieldSummary;
                }
            }
        }

        return $summary;
    }

    /**
     * Export custom report
     */
    public function exportCustomReport(array $reportData, string $format): string
    {
        $filename = 'custom_report_' . now()->format('Y-m-d_H-i-s');
        
        switch ($format) {
            case 'csv':
                return $this->exportToCSV($reportData, $filename);
            case 'excel':
                return $this->exportToExcel($reportData, $filename);
            case 'pdf':
                return $this->exportToPDF($reportData, $filename);
            case 'json':
                return $this->exportToJSON($reportData, $filename);
            default:
                throw new \InvalidArgumentException('Unsupported export format: ' . $format);
        }
    }

    /**
     * Export to CSV
     */
    protected function exportToCSV(array $reportData, string $filename): string
    {
        $filepath = storage_path("app/exports/{$filename}.csv");
        
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $file = fopen($filepath, 'w');
        
        // Write header
        if (!empty($reportData['data'])) {
            $firstRow = $reportData['data']->first();
            if ($firstRow) {
                fputcsv($file, array_keys((array) $firstRow));
            }
        }

        // Write data
        foreach ($reportData['data'] as $row) {
            fputcsv($file, array_values((array) $row));
        }

        fclose($file);
        
        return $filename . '.csv';
    }

    /**
     * Export to Excel (simplified as CSV for now)
     */
    protected function exportToExcel(array $reportData, string $filename): string
    {
        // For now, export as CSV (would use PhpSpreadsheet in production)
        return $this->exportToCSV($reportData, $filename);
    }

    /**
     * Export to PDF (simplified as HTML for now)
     */
    protected function exportToPDF(array $reportData, string $filename): string
    {
        $filepath = storage_path("app/exports/{$filename}.html");
        
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        $html = $this->generateReportHTML($reportData);
        file_put_contents($filepath, $html);
        
        return $filename . '.html';
    }

    /**
     * Export to JSON
     */
    protected function exportToJSON(array $reportData, string $filename): string
    {
        $filepath = storage_path("app/exports/{$filename}.json");
        
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        file_put_contents($filepath, json_encode($reportData, JSON_PRETTY_PRINT));
        
        return $filename . '.json';
    }

    /**
     * Generate HTML for report
     */
    protected function generateReportHTML(array $reportData): string
    {
        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Custom Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f5f5f5; }
                .header { background-color: #3b82f6; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Custom Report</h1>
                <p>Generated on: " . $reportData['generated_at'] . "</p>
                <p>Total Records: " . $reportData['total_records'] . "</p>
            </div>
            
            <table>
        ";

        // Add table header
        if (!empty($reportData['data'])) {
            $firstRow = $reportData['data']->first();
            if ($firstRow) {
                $html .= "<thead><tr>";
                foreach (array_keys((array) $firstRow) as $header) {
                    $html .= "<th>" . ucfirst(str_replace('_', ' ', $header)) . "</th>";
                }
                $html .= "</tr></thead>";
            }
        }

        // Add table body
        $html .= "<tbody>";
        foreach ($reportData['data'] as $row) {
            $html .= "<tr>";
            foreach ((array) $row as $value) {
                $html .= "<td>" . htmlspecialchars($value) . "</td>";
            }
            $html .= "</tr>";
        }
        $html .= "</tbody></table></body></html>";

        return $html;
    }
}
