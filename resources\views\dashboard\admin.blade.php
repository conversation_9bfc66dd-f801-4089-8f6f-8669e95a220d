<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Analytics Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                <select id="dateRangeSelector" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="7" {{ $dateRange == 7 ? 'selected' : '' }}>Last 7 Days</option>
                    <option value="30" {{ $dateRange == 30 ? 'selected' : '' }}>Last 30 Days</option>
                    <option value="90" {{ $dateRange == 90 ? 'selected' : '' }}>Last 90 Days</option>
                    <option value="365" {{ $dateRange == 365 ? 'selected' : '' }}>Last Year</option>
                </select>
                <button onclick="exportData('csv')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    📊 Export CSV
                </button>
                <button onclick="refreshDashboard()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    🔄 Refresh
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Real-time Status Bar -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg shadow-lg">
                <div class="grid grid-cols-2 md:grid-cols-6 gap-4 text-center">
                    <div class="real-time-metric" data-metric="active_orders">
                        <div class="text-2xl font-bold" id="activeOrders">{{ $realTimeData['active_orders'] }}</div>
                        <div class="text-sm opacity-90">Active Orders</div>
                    </div>
                    <div class="real-time-metric" data-metric="active_rentals">
                        <div class="text-2xl font-bold" id="activeRentals">{{ $realTimeData['active_rentals'] }}</div>
                        <div class="text-sm opacity-90">Active Rentals</div>
                    </div>
                    <div class="real-time-metric" data-metric="critical_tanks">
                        <div class="text-2xl font-bold text-red-300" id="criticalTanks">{{ $realTimeData['critical_tanks'] }}</div>
                        <div class="text-sm opacity-90">Critical Tanks</div>
                    </div>
                    <div class="real-time-metric" data-metric="active_alerts">
                        <div class="text-2xl font-bold text-yellow-300" id="activeAlerts">{{ $realTimeData['active_alerts'] }}</div>
                        <div class="text-sm opacity-90">Active Alerts</div>
                    </div>
                    <div class="real-time-metric" data-metric="pending_messages">
                        <div class="text-2xl font-bold text-green-300" id="pendingMessages">{{ $realTimeData['pending_messages'] }}</div>
                        <div class="text-sm opacity-90">Pending Messages</div>
                    </div>
                    <div class="real-time-metric" data-metric="overdue_invoices">
                        <div class="text-2xl font-bold text-orange-300" id="overdueInvoices">{{ $realTimeData['overdue_invoices'] }}</div>
                        <div class="text-sm opacity-90">Overdue Invoices</div>
                    </div>
                </div>
            </div>

            <!-- Overview Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg animate-fade-in">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 counter" data-target="{{ $analytics['overview']['total_orders'] }}">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Orders</div>
                        <div class="text-xs text-green-500 mt-1">
                            +{{ number_format($analytics['revenue']['revenue_growth'], 1) }}% growth
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg animate-fade-in">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400 counter" data-target="{{ $analytics['overview']['total_revenue'] }}">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
                        <div class="text-xs text-blue-500 mt-1">
                            ${{ number_format($analytics['revenue']['average_order_value'], 2) }} avg order
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg animate-fade-in">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-purple-600 dark:text-purple-400 counter" data-target="{{ $analytics['overview']['new_customers'] }}">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">New Customers</div>
                        <div class="text-xs text-purple-500 mt-1">
                            {{ number_format($analytics['customer']['customer_retention_rate'], 1) }}% retention
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg animate-fade-in">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-orange-600 dark:text-orange-400 counter" data-target="{{ $analytics['inventory']['cylinder_utilization_rate'] }}">0</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Utilization Rate %</div>
                        <div class="text-xs text-orange-500 mt-1">
                            {{ $analytics['inventory']['total_cylinders'] }} total cylinders
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Revenue Chart -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Revenue Trends
                        </h3>
                        <div class="h-64">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Operations Chart -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Operational Metrics
                        </h3>
                        <div class="h-64">
                            <canvas id="operationsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Order Fulfillment
                        </h3>
                        <div class="flex items-center justify-center h-32">
                            <div class="relative w-24 h-24">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $analytics['operations']['order_fulfillment_rate'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-green-600">{{ number_format($analytics['operations']['order_fulfillment_rate'], 1) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Customer Satisfaction
                        </h3>
                        <div class="flex items-center justify-center h-32">
                            <div class="relative w-24 h-24">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-blue-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $analytics['operations']['customer_satisfaction'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-blue-600">{{ number_format($analytics['operations']['customer_satisfaction'], 1) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            System Performance
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Uptime</span>
                                <span class="text-sm font-medium text-green-600">{{ number_format($analytics['performance']['system_uptime'], 1) }}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Message Delivery</span>
                                <span class="text-sm font-medium text-blue-600">{{ number_format($analytics['performance']['message_delivery_rate'], 1) }}%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Error Rate</span>
                                <span class="text-sm font-medium text-red-600">{{ number_format($analytics['performance']['error_rates'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Quick Actions
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <a href="{{ route('orders.index') }}" class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                            <div class="text-2xl mb-2">📦</div>
                            <div class="text-sm font-medium text-blue-800 dark:text-blue-200">Orders</div>
                        </a>
                        
                        <a href="{{ route('rentals.index') }}" class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                            <div class="text-2xl mb-2">🔄</div>
                            <div class="text-sm font-medium text-green-800 dark:text-green-200">Rentals</div>
                        </a>
                        
                        <a href="{{ route('financial.dashboard') }}" class="flex flex-col items-center p-4 bg-emerald-50 dark:bg-emerald-900 rounded-lg hover:bg-emerald-100 dark:hover:bg-emerald-800 transition-colors">
                            <div class="text-2xl mb-2">💰</div>
                            <div class="text-sm font-medium text-emerald-800 dark:text-emerald-200">Financial</div>
                        </a>
                        
                        <a href="{{ route('tanks.dashboard') }}" class="flex flex-col items-center p-4 bg-orange-50 dark:bg-orange-900 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-800 transition-colors">
                            <div class="text-2xl mb-2">🛢️</div>
                            <div class="text-sm font-medium text-orange-800 dark:text-orange-200">Tanks</div>
                        </a>
                        
                        <a href="{{ route('whatsapp.dashboard') }}" class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors">
                            <div class="text-2xl mb-2">💬</div>
                            <div class="text-sm font-medium text-purple-800 dark:text-purple-200">WhatsApp</div>
                        </a>
                        
                        <a href="{{ route('customers.index') }}" class="flex flex-col items-center p-4 bg-pink-50 dark:bg-pink-900 rounded-lg hover:bg-pink-100 dark:hover:bg-pink-800 transition-colors">
                            <div class="text-2xl mb-2">👥</div>
                            <div class="text-sm font-medium text-pink-800 dark:text-pink-200">Customers</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Analytics data from backend
        const analyticsData = @json($analytics);
        
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            initializeCounters();
            initializeRealTimeUpdates();
        });

        // Initialize Chart.js charts
        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(analyticsData.revenue.daily_revenue || {}),
                    datasets: [{
                        label: 'Daily Revenue',
                        data: Object.values(analyticsData.revenue.daily_revenue || {}),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Operations Chart
            const operationsCtx = document.getElementById('operationsChart').getContext('2d');
            new Chart(operationsCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(analyticsData.operations.order_status_distribution || {}),
                    datasets: [{
                        data: Object.values(analyticsData.operations.order_status_distribution || {}),
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)',
                            'rgb(239, 68, 68)',
                            'rgb(139, 92, 246)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Initialize counter animations
        function initializeCounters() {
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current).toLocaleString();
                }, 16);
            });
        }

        // Initialize real-time updates
        function initializeRealTimeUpdates() {
            setInterval(updateRealTimeData, 30000); // Update every 30 seconds
        }

        // Update real-time data
        async function updateRealTimeData() {
            try {
                const response = await fetch('/dashboard/data');
                const data = await response.json();
                
                // Update real-time metrics
                document.getElementById('activeOrders').textContent = data.real_time.active_orders;
                document.getElementById('activeRentals').textContent = data.real_time.active_rentals;
                document.getElementById('criticalTanks').textContent = data.real_time.critical_tanks;
                document.getElementById('activeAlerts').textContent = data.real_time.active_alerts;
                document.getElementById('pendingMessages').textContent = data.real_time.pending_messages;
                document.getElementById('overdueInvoices').textContent = data.real_time.overdue_invoices;
                
                // Add pulse animation to updated elements
                document.querySelectorAll('.real-time-metric').forEach(element => {
                    element.classList.add('animate-pulse');
                    setTimeout(() => element.classList.remove('animate-pulse'), 1000);
                });
            } catch (error) {
                console.error('Failed to update real-time data:', error);
            }
        }

        // Date range selector
        document.getElementById('dateRangeSelector').addEventListener('change', function() {
            const dateRange = this.value;
            window.location.href = `?date_range=${dateRange}`;
        });

        // Refresh dashboard
        function refreshDashboard() {
            location.reload();
        }

        // Export data
        function exportData(format) {
            const dateRange = document.getElementById('dateRangeSelector').value;
            window.open(`/dashboard/export?format=${format}&date_range=${dateRange}`, '_blank');
        }
    </script>

    <style>
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .real-time-metric {
            transition: all 0.3s ease;
        }

        .real-time-metric:hover {
            transform: scale(1.05);
        }
    </style>
</x-app-layout>
