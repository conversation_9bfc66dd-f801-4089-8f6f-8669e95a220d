<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Inventory Management') }}
            </h2>
            <div class="flex space-x-2">
                @can('create_transfers')
                    <a href="{{ route('inventory.transfers.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        🚚 Request Transfer
                    </a>
                @endcan
                @can('manage_inventory')
                    <button onclick="syncInventory()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        🔄 Sync Inventory
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($statistics['total_cylinders']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">Total Cylinders</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($statistics['available_cylinders']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">Available</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                            {{ number_format($statistics['in_use_cylinders']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">In Use</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($statistics['damaged_cylinders']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">Damaged</div>
                    </div>
                </div>
            </div>

            <!-- Alerts Section -->
            @if($criticalStockAlerts->count() > 0 || $lowStockAlerts->count() > 0 || $pendingTransfers->count() > 0)
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Critical Stock Alerts -->
                @if($criticalStockAlerts->count() > 0)
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-red-600 dark:text-red-400 mb-4">
                            🚨 Critical Stock ({{ $criticalStockAlerts->count() }})
                        </h3>
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            @foreach($criticalStockAlerts as $alert)
                                <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900 rounded-lg">
                                    <div>
                                        <div class="font-medium text-red-800 dark:text-red-200">
                                            {{ $alert->gasType->name }}
                                        </div>
                                        <div class="text-sm text-red-600 dark:text-red-400">
                                            {{ $alert->location->name }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-bold text-red-800 dark:text-red-200">
                                            {{ $alert->available_count }}
                                        </div>
                                        <div class="text-xs text-red-600 dark:text-red-400">
                                            Min: {{ $alert->min_stock_level }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Low Stock Alerts -->
                @if($lowStockAlerts->count() > 0)
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-yellow-600 dark:text-yellow-400 mb-4">
                            ⚠️ Low Stock ({{ $lowStockAlerts->count() }})
                        </h3>
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            @foreach($lowStockAlerts as $alert)
                                <div class="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                                    <div>
                                        <div class="font-medium text-yellow-800 dark:text-yellow-200">
                                            {{ $alert->gasType->name }}
                                        </div>
                                        <div class="text-sm text-yellow-600 dark:text-yellow-400">
                                            {{ $alert->location->name }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-bold text-yellow-800 dark:text-yellow-200">
                                            {{ $alert->available_count }}
                                        </div>
                                        <div class="text-xs text-yellow-600 dark:text-yellow-400">
                                            Reorder: {{ $alert->reorder_level }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Pending Transfers -->
                @if($pendingTransfers->count() > 0)
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-4">
                            📋 Pending Transfers ({{ $pendingTransfers->count() }})
                        </h3>
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            @foreach($pendingTransfers as $transfer)
                                <div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="font-medium text-blue-800 dark:text-blue-200">
                                                {{ $transfer->transfer_number }}
                                            </div>
                                            <div class="text-sm text-blue-600 dark:text-blue-400">
                                                {{ $transfer->fromLocation->name }} → {{ $transfer->toLocation->name }}
                                            </div>
                                            <div class="text-xs text-blue-500 dark:text-blue-500">
                                                {{ $transfer->gasType->name }} ({{ $transfer->quantity }})
                                            </div>
                                        </div>
                                        <a href="{{ route('inventory.transfers.show', $transfer) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm">
                                            View
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @endif

            <!-- Inventory by Location -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Inventory by Location
                        </h3>
                        <a href="{{ route('inventory.transfers.index') }}" 
                           class="text-blue-600 hover:text-blue-800">
                            View All Transfers →
                        </a>
                    </div>

                    @if($inventoryOverview->count() > 0)
                        <div class="space-y-6">
                            @foreach($inventoryOverview as $locationId => $inventories)
                                @php $location = $inventories->first()->location; @endphp
                                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                    <div class="flex justify-between items-center mb-4">
                                        <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100">
                                            📍 {{ $location->name }}
                                            <span class="text-sm text-gray-500">({{ $location->getTypeLabel() }})</span>
                                        </h4>
                                        <a href="{{ route('inventory.show', $location) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm">
                                            View Details
                                        </a>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($inventories as $inventory)
                                            @php $status = $inventory->getStockStatus(); @endphp
                                            <div class="border border-gray-100 dark:border-gray-700 rounded-lg p-3">
                                                <div class="flex justify-between items-start mb-2">
                                                    <div>
                                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                                            {{ $inventory->gasType->name }}
                                                        </div>
                                                        <div class="text-sm text-gray-500">
                                                            {{ $inventory->gasType->code }}
                                                        </div>
                                                    </div>
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $status['color'] }}-100 text-{{ $status['color'] }}-800">
                                                        {{ $status['label'] }}
                                                    </span>
                                                </div>
                                                
                                                <div class="grid grid-cols-2 gap-2 text-sm">
                                                    <div>
                                                        <span class="text-gray-500">Full:</span>
                                                        <span class="font-medium text-green-600">{{ $inventory->full_count }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="text-gray-500">Empty:</span>
                                                        <span class="font-medium text-gray-600">{{ $inventory->empty_count }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="text-gray-500">In Use:</span>
                                                        <span class="font-medium text-blue-600">{{ $inventory->in_use_count }}</span>
                                                    </div>
                                                    <div>
                                                        <span class="text-gray-500">Damaged:</span>
                                                        <span class="font-medium text-red-600">{{ $inventory->damaged_count }}</span>
                                                    </div>
                                                </div>
                                                
                                                <div class="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                                                    <div class="text-xs text-gray-500">
                                                        Available: <span class="font-medium">{{ $inventory->available_count }}</span> / 
                                                        Total: <span class="font-medium">{{ $inventory->total_count }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400">
                                No inventory data available. 
                                <button onclick="syncInventory()" class="text-blue-600 hover:text-blue-800">
                                    Sync inventory
                                </button> 
                                to get started.
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        async function syncInventory() {
            try {
                const response = await fetch('{{ route("inventory.sync") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Sync error:', error);
                alert('Failed to sync inventory. Please try again.');
            }
        }
    </script>
</x-app-layout>
