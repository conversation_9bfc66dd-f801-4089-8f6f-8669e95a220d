<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'location_id',
        'gas_type_id',
        'movement_type',
        'quantity',
        'reference_type',
        'reference_id',
        'cylinder_id',
        'user_id',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Movement types
     */
    const MOVEMENT_TYPES = [
        'in' => 'Stock In',
        'out' => 'Stock Out',
        'transfer_in' => 'Transfer In',
        'transfer_out' => 'Transfer Out',
        'adjustment' => 'Adjustment',
        'damaged' => 'Damaged',
        'expired' => 'Expired',
    ];

    /**
     * Get the location for this movement
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the gas type for this movement
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the user who created this movement
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the cylinder for this movement
     */
    public function cylinder(): BelongsTo
    {
        return $this->belongsTo(Cylinder::class);
    }

    /**
     * Get the reference model (polymorphic)
     */
    public function reference()
    {
        if ($this->reference_type && $this->reference_id) {
            return $this->morphTo('reference', 'reference_type', 'reference_id');
        }
        return null;
    }

    /**
     * Get movement type label
     */
    public function getMovementTypeLabel(): string
    {
        return self::MOVEMENT_TYPES[$this->movement_type] ?? ucfirst(str_replace('_', ' ', $this->movement_type));
    }

    /**
     * Get movement direction (positive or negative)
     */
    public function getDirection(): string
    {
        return in_array($this->movement_type, ['in', 'transfer_in', 'adjustment']) ? 'positive' : 'negative';
    }

    /**
     * Get signed quantity (positive for in, negative for out)
     */
    public function getSignedQuantity(): int
    {
        return $this->getDirection() === 'positive' ? $this->quantity : -$this->quantity;
    }

    /**
     * Scope for specific movement type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    /**
     * Scope for specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for specific gas type
     */
    public function scopeForGasType($query, $gasTypeId)
    {
        return $query->where('gas_type_id', $gasTypeId);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Create a stock movement record
     */
    public static function createMovement($data)
    {
        $movement = static::create([
            'location_id' => $data['location_id'],
            'gas_type_id' => $data['gas_type_id'],
            'movement_type' => $data['movement_type'],
            'quantity' => $data['quantity'],
            'reference_type' => $data['reference_type'] ?? null,
            'reference_id' => $data['reference_id'] ?? null,
            'cylinder_id' => $data['cylinder_id'] ?? null,
            'user_id' => $data['user_id'] ?? auth()->id(),
            'notes' => $data['notes'] ?? null,
            'metadata' => $data['metadata'] ?? null,
        ]);

        // Update inventory counts
        static::updateInventoryFromMovement($movement);

        return $movement;
    }

    /**
     * Update inventory based on movement
     */
    protected static function updateInventoryFromMovement(StockMovement $movement)
    {
        $inventory = Inventory::firstOrCreate(
            [
                'location_id' => $movement->location_id,
                'gas_type_id' => $movement->gas_type_id,
            ],
            [
                'full_count' => 0,
                'empty_count' => 0,
                'damaged_count' => 0,
                'maintenance_count' => 0,
                'in_use_count' => 0,
                'in_transit_count' => 0,
                'expired_count' => 0,
                'min_stock_level' => 10,
                'max_stock_level' => 100,
                'reorder_level' => 20,
            ]
        );

        // Sync with actual cylinder counts
        $inventory->syncWithCylinders();
    }
}
