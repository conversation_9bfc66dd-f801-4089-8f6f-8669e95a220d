<?php

namespace App\Http\Controllers;

use App\Services\ComplianceService;
use App\Services\AuditTrailService;
use App\Services\ReportService;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ComplianceController extends Controller
{
    protected $complianceService;
    protected $auditTrailService;
    protected $reportService;

    public function __construct(
        ComplianceService $complianceService,
        AuditTrailService $auditTrailService,
        ReportService $reportService
    ) {
        $this->middleware('auth');
        $this->middleware('permission:view_compliance', ['only' => ['index', 'dashboard']];
        $this->middleware('permission:generate_reports', ['only' => ['generateReport', 'export']];
        $this->complianceService = $complianceService;
        $this->auditTrailService = $auditTrailService;
        $this->reportService = $reportService;
    }

    /**
     * Compliance dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);
        $locationIds = $accessibleLocations->pluck('id')->toArray();

        $complianceData = [
            'status' => $this->complianceService->getComplianceStatus($locationIds),
            'cylinder_compliance' => $this->complianceService->trackCylinderCompliance($locationIds),
            'tank_compliance' => $this->complianceService->trackTankCompliance($locationIds),
            'regulatory_compliance' => $this->complianceService->trackRegulatoryCompliance($locationIds),
            'alerts' => $this->complianceService->generateComplianceAlerts($locationIds),
        ];

        return view('compliance.index', compact('complianceData', 'accessibleLocations'));
    }

    /**
     * Generate compliance report
     */
    public function generateComplianceReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateComplianceReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        // Log the report generation
        $this->auditTrailService->logActivity(
            'generate_compliance_report',
            null,
            null,
            ['location_ids' => $locationIds, 'date_range' => [$request->start_date, $request->end_date]],
            'Generated compliance report'
        );

        return response()->json($report);
    }

    /**
     * Generate audit trail report
     */
    public function generateAuditReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'user_id' => 'nullable|exists:users,id',
        ]);

        $report = $this->auditTrailService->generateAuditReport(
            $request->start_date,
            $request->end_date,
            $request->user_id
        );

        // Log the audit report generation
        $this->auditTrailService->logActivity(
            'generate_audit_report',
            null,
            null,
            ['user_id' => $request->user_id, 'date_range' => [$request->start_date, $request->end_date]],
            'Generated audit trail report'
        );

        return response()->json($report);
    }

    /**
     * Get compliance alerts
     */
    public function getAlerts(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $alerts = $this->complianceService->generateComplianceAlerts($accessibleLocationIds);

        return response()->json($alerts);
    }

    /**
     * Get audit trail for specific model
     */
    public function getModelAuditTrail(Request $request)
    {
        $request->validate([
            'model_type' => 'required|string',
            'model_id' => 'required|integer',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $auditTrail = $this->auditTrailService->getModelAuditTrail(
            $request->model_type,
            $request->model_id,
            $request->limit ?? 50
        );

        return response()->json($auditTrail);
    }

    /**
     * Export compliance report
     */
    public function exportComplianceReport(Request $request)
    {
        $request->validate([
            'format' => 'required|in:pdf,excel,csv',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateComplianceReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        switch ($request->format) {
            case 'excel':
                $filename = $this->reportService->exportToExcel($report, 'compliance');
                break;
            case 'pdf':
                $filename = $this->reportService->exportToPDF($report, 'compliance');
                break;
            case 'csv':
            default:
                $filename = $this->reportService->exportToExcel($report, 'compliance'); // CSV version
                break;
        }

        // Log the export
        $this->auditTrailService->logActivity(
            'export_compliance_report',
            null,
            null,
            ['format' => $request->format, 'filename' => $filename],
            'Exported compliance report'
        );

        return response()->download(storage_path('app/exports/' . $filename));
    }

    /**
     * Export audit trail
     */
    public function exportAuditTrail(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,json',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'user_id' => 'nullable|exists:users,id',
        ]);

        $filename = $this->auditTrailService->exportAuditData(
            $request->start_date,
            $request->end_date,
            $request->format
        );

        // Log the export
        $this->auditTrailService->logActivity(
            'export_audit_trail',
            null,
            null,
            ['format' => $request->format, 'filename' => $filename],
            'Exported audit trail'
        );

        return response()->download(storage_path('app/exports/' . $filename));
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
