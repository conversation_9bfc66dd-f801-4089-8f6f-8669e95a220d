<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Location;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $locations = [
            [
                'name' => 'Main Branch - Downtown',
                'type' => 'branch',
                'address' => '123 Main Street, Downtown, City 12345',
                'phone' => '******-MAIN-001',
                'manager_id' => null, // Will be assigned later
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'is_active' => true,
            ],
            [
                'name' => 'Central Warehouse',
                'type' => 'warehouse',
                'address' => '456 Industrial Blvd, Industrial Zone, City 12346',
                'phone' => '******-WARE-001',
                'manager_id' => null,
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'is_active' => true,
            ],
            [
                'name' => 'North Distribution Center',
                'type' => 'distribution_center',
                'address' => '789 North Avenue, North District, City 12347',
                'phone' => '******-DIST-001',
                'manager_id' => null,
                'latitude' => 40.7831,
                'longitude' => -73.9712,
                'is_active' => true,
            ],
            [
                'name' => 'South Branch',
                'type' => 'branch',
                'address' => '321 South Road, South District, City 12348',
                'phone' => '******-SOUTH-01',
                'manager_id' => null,
                'latitude' => 40.6892,
                'longitude' => -74.0445,
                'is_active' => true,
            ],
            [
                'name' => 'East Warehouse',
                'type' => 'warehouse',
                'address' => '654 East Street, East Zone, City 12349',
                'phone' => '******-EAST-001',
                'manager_id' => null,
                'latitude' => 40.7282,
                'longitude' => -73.7949,
                'is_active' => true,
            ],
        ];

        foreach ($locations as $location) {
            Location::create($location);
        }

        $this->command->info('Locations created successfully!');
    }
}
