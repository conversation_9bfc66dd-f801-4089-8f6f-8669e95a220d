<?php

namespace App\Http\Controllers;

use App\Models\Tank;
use App\Models\TankReading;
use App\Models\TankRefill;
use App\Models\TankAlert;
use App\Models\Location;
use App\Models\GasType;
use App\Models\Supplier;
use App\Services\TankService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TankController extends Controller
{
    protected $tankService;

    public function __construct(TankService $tankService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_tanks', ['only' => ['index', 'show', 'dashboard']];
        $this->middleware('permission:manage_tanks', ['only' => ['create', 'store', 'edit', 'update', 'destroy']];
        $this->middleware('permission:manage_tank_readings', ['only' => ['updateReading', 'readings']];
        $this->middleware('permission:manage_refills', ['only' => ['scheduleRefill', 'completeRefill']];
        $this->tankService = $tankService;
    }

    /**
     * Tank monitoring dashboard
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $statistics = $this->tankService->getTankStatistics($accessibleLocationIds);
        $alertsStats = $this->tankService->getAlertsStatistics($accessibleLocationIds);
        $refillStats = $this->tankService->getRefillStatistics($accessibleLocationIds);

        // Get critical tanks
        $criticalTanks = Tank::with(['gasType', 'location'])
                            ->whereIn('location_id', $accessibleLocationIds)
                            ->critical()
                            ->orderBy('current_level', 'asc')
                            ->get();

        // Get tanks needing refill
        $tanksNeedingRefill = Tank::with(['gasType', 'location'])
                                 ->whereIn('location_id', $accessibleLocationIds)
                                 ->needsRefill()
                                 ->orderBy('current_level', 'asc')
                                 ->get();

        // Get active alerts
        $activeAlerts = TankAlert::with(['tank.gasType', 'tank.location'])
                                ->whereHas('tank', function ($q) use ($accessibleLocationIds) {
                                    $q->whereIn('location_id', $accessibleLocationIds);
                                })
                                ->active()
                                ->orderBy('severity', 'desc')
                                ->orderBy('triggered_at', 'asc')
                                ->limit(10)
                                ->get();

        // Get upcoming refills
        $upcomingRefills = TankRefill::with(['tank.gasType', 'tank.location', 'supplier'])
                                   ->whereHas('tank', function ($q) use ($accessibleLocationIds) {
                                       $q->whereIn('location_id', $accessibleLocationIds);
                                   })
                                   ->whereIn('status', ['scheduled', 'confirmed'])
                                   ->orderBy('scheduled_date', 'asc')
                                   ->limit(10)
                                   ->get();

        return view('tanks.dashboard', compact(
            'statistics',
            'alertsStats',
            'refillStats',
            'criticalTanks',
            'tanksNeedingRefill',
            'activeAlerts',
            'upcomingRefills'
        ));
    }

    /**
     * Display tank listing
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = Tank::with(['gasType', 'location', 'supplier']);

        // Apply location filtering
        $query->whereIn('location_id', $accessibleLocationIds);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('tank_type')) {
            $query->where('tank_type', $request->tank_type);
        }

        if ($request->filled('gas_type')) {
            $query->where('gas_type_id', $request->gas_type);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('tank_number', 'like', "%{$search}%")
                  ->orWhereHas('gasType', function ($gasQuery) use ($search) {
                      $gasQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'critical') {
            $query->critical();
        } elseif ($request->filter === 'needs_refill') {
            $query->needsRefill();
        } elseif ($request->filter === 'maintenance_due') {
            $query->maintenanceDue();
        }

        $tanks = $query->orderBy('created_at', 'desc')->paginate(20);

        $gasTypes = GasType::active()->get();
        $locations = $this->getAccessibleLocations($user);

        return view('tanks.index', compact('tanks', 'gasTypes', 'locations'));
    }

    /**
     * Show tank details
     */
    public function show(Tank $tank)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($tank->location_id)) {
            abort(403, 'You do not have access to this tank.');
        }

        $tank->load(['gasType', 'location', 'supplier']);

        // Get recent readings
        $recentReadings = $tank->readings()
                              ->with('recordedBy')
                              ->orderBy('recorded_at', 'desc')
                              ->limit(20)
                              ->get();

        // Get active alerts
        $activeAlerts = $tank->alerts()
                            ->active()
                            ->orderBy('severity', 'desc')
                            ->orderBy('triggered_at', 'desc')
                            ->get();

        // Get recent refills
        $recentRefills = $tank->refills()
                             ->with(['supplier', 'requestedBy', 'receivedBy'])
                             ->orderBy('created_at', 'desc')
                             ->limit(10)
                             ->get();

        return view('tanks.show', compact('tank', 'recentReadings', 'activeAlerts', 'recentRefills'));
    }

    /**
     * Update tank reading
     */
    public function updateReading(Request $request, Tank $tank)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($tank->location_id)) {
            abort(403, 'You do not have access to this tank.');
        }

        $request->validate([
            'reading_value' => 'required|numeric|min:0|max:' . $tank->capacity,
            'reading_type' => 'required|in:level,temperature,pressure,flow_rate,consumption',
            'source' => 'required|in:manual,sensor,automatic,inspection,maintenance',
            'notes' => 'nullable|string|max:1000',
            'temperature' => 'nullable|numeric',
            'pressure' => 'nullable|numeric',
            'humidity' => 'nullable|numeric',
        ]);

        try {
            $reading = $this->tankService->updateTankReading($tank, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Tank reading updated successfully.',
                'reading' => $reading,
                'tank' => $tank->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
