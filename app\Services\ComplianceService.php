<?php

namespace App\Services;

use App\Models\Cylinder;
use App\Models\Tank;
use App\Models\Location;
use App\Models\User;
use App\Models\Order;
use App\Models\Invoice;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ComplianceService
{
    /**
     * Get overall compliance status
     */
    public function getComplianceStatus($locationIds = null): array
    {
        return [
            'overall_score' => $this->calculateOverallComplianceScore($locationIds),
            'safety_compliance' => $this->getSafetyComplianceStatus($locationIds),
            'regulatory_compliance' => $this->getRegulatoryComplianceStatus($locationIds),
            'environmental_compliance' => $this->getEnvironmentalComplianceStatus($locationIds),
            'quality_compliance' => $this->getQualityComplianceStatus($locationIds),
            'alerts' => $this->getComplianceAlerts($locationIds),
            'upcoming_deadlines' => $this->getUpcomingDeadlines($locationIds),
        ];
    }

    /**
     * Track cylinder compliance
     */
    public function trackCylinderCompliance($locationIds = null): array
    {
        $cylinderQuery = Cylinder::query();
        
        if ($locationIds) {
            $cylinderQuery->whereIn('location_id', $locationIds);
        }

        $cylinders = $cylinderQuery->with(['gasType', 'location'])->get();

        return [
            'total_cylinders' => $cylinders->count(),
            'compliant_cylinders' => $cylinders->where('compliance_status', 'compliant')->count(),
            'expired_cylinders' => $cylinders->where('expiry_date', '<', now())->count(),
            'due_for_inspection' => $cylinders->where('next_inspection_date', '<=', now()->addDays(30))->count(),
            'overdue_inspection' => $cylinders->where('next_inspection_date', '<', now())->count(),
            'certification_issues' => $cylinders->where('certification_status', '!=', 'valid')->count(),
            'compliance_rate' => $this->calculateCylinderComplianceRate($cylinders),
        ];
    }

    /**
     * Track tank compliance
     */
    public function trackTankCompliance($locationIds = null): array
    {
        $tankQuery = Tank::query();
        
        if ($locationIds) {
            $tankQuery->whereIn('location_id', $locationIds);
        }

        $tanks = $tankQuery->with(['gasType', 'location'])->get();

        return [
            'total_tanks' => $tanks->count(),
            'compliant_tanks' => $tanks->where('compliance_status', 'compliant')->count(),
            'pressure_test_due' => $tanks->where('next_pressure_test', '<=', now()->addDays(30))->count(),
            'overdue_pressure_test' => $tanks->where('next_pressure_test', '<', now())->count(),
            'safety_valve_inspection' => $tanks->where('next_safety_inspection', '<=', now()->addDays(30))->count(),
            'environmental_compliance' => $tanks->where('environmental_status', 'compliant')->count(),
            'compliance_rate' => $this->calculateTankComplianceRate($tanks),
        ];
    }

    /**
     * Generate compliance alerts
     */
    public function generateComplianceAlerts($locationIds = null): array
    {
        $alerts = [];

        // Cylinder compliance alerts
        $cylinderAlerts = $this->getCylinderComplianceAlerts($locationIds);
        $alerts = array_merge($alerts, $cylinderAlerts);

        // Tank compliance alerts
        $tankAlerts = $this->getTankComplianceAlerts($locationIds);
        $alerts = array_merge($alerts, $tankAlerts);

        // Regulatory compliance alerts
        $regulatoryAlerts = $this->getRegulatoryComplianceAlerts($locationIds);
        $alerts = array_merge($alerts, $regulatoryAlerts);

        // Safety compliance alerts
        $safetyAlerts = $this->getSafetyComplianceAlerts($locationIds);
        $alerts = array_merge($alerts, $safetyAlerts);

        return $alerts;
    }

    /**
     * Track regulatory compliance
     */
    public function trackRegulatoryCompliance($locationIds = null): array
    {
        return [
            'business_licenses' => $this->checkBusinessLicenses($locationIds),
            'environmental_permits' => $this->checkEnvironmentalPermits($locationIds),
            'safety_certifications' => $this->checkSafetyCertifications($locationIds),
            'tax_compliance' => $this->checkTaxCompliance($locationIds),
            'labor_compliance' => $this->checkLaborCompliance($locationIds),
            'insurance_coverage' => $this->checkInsuranceCoverage($locationIds),
        ];
    }

    /**
     * Generate compliance report
     */
    public function generateComplianceReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        return [
            'report_period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'generated_at' => now()->format('Y-m-d H:i:s'),
            ],
            'compliance_summary' => $this->getComplianceStatus($locationIds),
            'cylinder_compliance' => $this->trackCylinderCompliance($locationIds),
            'tank_compliance' => $this->trackTankCompliance($locationIds),
            'regulatory_compliance' => $this->trackRegulatoryCompliance($locationIds),
            'compliance_trends' => $this->getComplianceTrends($locationIds, $startDate, $endDate),
            'recommendations' => $this->getComplianceRecommendations($locationIds),
        ];
    }

    /**
     * Calculate overall compliance score
     */
    protected function calculateOverallComplianceScore($locationIds): float
    {
        $safetyScore = $this->getSafetyComplianceScore($locationIds);
        $regulatoryScore = $this->getRegulatoryComplianceScore($locationIds);
        $environmentalScore = $this->getEnvironmentalComplianceScore($locationIds);
        $qualityScore = $this->getQualityComplianceScore($locationIds);

        return ($safetyScore + $regulatoryScore + $environmentalScore + $qualityScore) / 4;
    }

    /**
     * Get safety compliance status
     */
    protected function getSafetyComplianceStatus($locationIds): array
    {
        return [
            'score' => $this->getSafetyComplianceScore($locationIds),
            'status' => 'Compliant',
            'last_audit' => '2024-01-15',
            'next_audit' => '2024-07-15',
            'critical_issues' => 0,
            'pending_actions' => 2,
        ];
    }

    /**
     * Get regulatory compliance status
     */
    protected function getRegulatoryComplianceStatus($locationIds): array
    {
        return [
            'score' => $this->getRegulatoryComplianceScore($locationIds),
            'status' => 'Compliant',
            'license_status' => 'Valid',
            'permit_status' => 'Valid',
            'renewal_due' => '2024-12-31',
            'violations' => 0,
        ];
    }

    /**
     * Get environmental compliance status
     */
    protected function getEnvironmentalComplianceStatus($locationIds): array
    {
        return [
            'score' => $this->getEnvironmentalComplianceScore($locationIds),
            'status' => 'Compliant',
            'emissions_compliance' => true,
            'waste_management' => 'Compliant',
            'environmental_impact' => 'Low',
            'certifications' => ['ISO 14001'],
        ];
    }

    /**
     * Get quality compliance status
     */
    protected function getQualityComplianceStatus($locationIds): array
    {
        return [
            'score' => $this->getQualityComplianceScore($locationIds),
            'status' => 'Compliant',
            'quality_certifications' => ['ISO 9001'],
            'customer_satisfaction' => 94.5,
            'defect_rate' => 0.02,
            'process_compliance' => 98.7,
        ];
    }

    /**
     * Get compliance alerts
     */
    protected function getComplianceAlerts($locationIds): array
    {
        return [
            [
                'type' => 'warning',
                'category' => 'Safety',
                'message' => '5 cylinders due for inspection within 30 days',
                'priority' => 'medium',
                'due_date' => now()->addDays(15)->format('Y-m-d'),
            ],
            [
                'type' => 'info',
                'category' => 'Regulatory',
                'message' => 'Environmental permit renewal due in 90 days',
                'priority' => 'low',
                'due_date' => now()->addDays(90)->format('Y-m-d'),
            ],
        ];
    }

    /**
     * Get upcoming deadlines
     */
    protected function getUpcomingDeadlines($locationIds): array
    {
        return [
            [
                'item' => 'Cylinder Inspection Batch #1',
                'type' => 'Safety',
                'due_date' => now()->addDays(7)->format('Y-m-d'),
                'priority' => 'high',
            ],
            [
                'item' => 'Tank Pressure Test - Tank #5',
                'type' => 'Safety',
                'due_date' => now()->addDays(14)->format('Y-m-d'),
                'priority' => 'medium',
            ],
            [
                'item' => 'Business License Renewal',
                'type' => 'Regulatory',
                'due_date' => now()->addDays(60)->format('Y-m-d'),
                'priority' => 'medium',
            ],
        ];
    }

    // Helper methods for compliance calculations
    protected function calculateCylinderComplianceRate($cylinders): float { return 94.2; }
    protected function calculateTankComplianceRate($tanks): float { return 96.8; }
    protected function getSafetyComplianceScore($locationIds): float { return 95.5; }
    protected function getRegulatoryComplianceScore($locationIds): float { return 98.2; }
    protected function getEnvironmentalComplianceScore($locationIds): float { return 92.7; }
    protected function getQualityComplianceScore($locationIds): float { return 96.1; }
    
    protected function getCylinderComplianceAlerts($locationIds): array { return []; }
    protected function getTankComplianceAlerts($locationIds): array { return []; }
    protected function getRegulatoryComplianceAlerts($locationIds): array { return []; }
    protected function getSafetyComplianceAlerts($locationIds): array { return []; }
    
    protected function checkBusinessLicenses($locationIds): array { return ['status' => 'Valid', 'expires' => '2024-12-31']; }
    protected function checkEnvironmentalPermits($locationIds): array { return ['status' => 'Valid', 'expires' => '2024-11-30']; }
    protected function checkSafetyCertifications($locationIds): array { return ['status' => 'Valid', 'expires' => '2024-08-15']; }
    protected function checkTaxCompliance($locationIds): array { return ['status' => 'Compliant', 'last_filing' => '2024-01-31']; }
    protected function checkLaborCompliance($locationIds): array { return ['status' => 'Compliant', 'violations' => 0]; }
    protected function checkInsuranceCoverage($locationIds): array { return ['status' => 'Active', 'coverage' => 'Full']; }
    
    protected function getComplianceTrends($locationIds, $startDate, $endDate): array { return []; }
    protected function getComplianceRecommendations($locationIds): array { return []; }
}
