<?php
/**
 * Check Admin User Credentials
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "🔍 Checking Admin User Credentials\n";
echo "==================================\n\n";

try {
    // Find admin user
    $user = User::where('email', '<EMAIL>')->first();
    
    if ($user) {
        echo "✅ Admin user found!\n";
        echo "   Email: {$user->email}\n";
        echo "   Name: {$user->name}\n";
        echo "   Active: " . ($user->is_active ? 'Yes' : 'No') . "\n";
        echo "   Email Verified: " . ($user->email_verified_at ? 'Yes' : 'No') . "\n";
        echo "   Created: {$user->created_at}\n";
        
        // Check roles
        $roles = $user->getRoleNames();
        echo "   Roles: " . $roles->implode(', ') . "\n";
        
        // Test password
        echo "\n🔑 Testing Password:\n";
        $testPasswords = ['password123', 'password', 'admin123', 'gcms123'];
        
        foreach ($testPasswords as $testPassword) {
            if (Hash::check($testPassword, $user->password)) {
                echo "   ✅ Correct password: '$testPassword'\n";
                break;
            } else {
                echo "   ❌ Wrong password: '$testPassword'\n";
            }
        }
        
        // Check if user can login
        echo "\n🔐 Login Test:\n";
        if ($user->is_active && $user->email_verified_at) {
            echo "   ✅ User can login (active and verified)\n";
        } else {
            echo "   ❌ User cannot login:\n";
            if (!$user->is_active) echo "     - Account not active\n";
            if (!$user->email_verified_at) echo "     - Email not verified\n";
        }
        
    } else {
        echo "❌ Admin user not found!\n";
        echo "💡 Creating admin user...\n";
        
        $newUser = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        
        // Assign super_admin role
        $newUser->assignRole('super_admin');
        
        echo "✅ Admin user created successfully!\n";
        echo "   Email: <EMAIL>\n";
        echo "   Password: password123\n";
    }
    
    echo "\n📊 User Statistics:\n";
    echo "   Total Users: " . User::count() . "\n";
    echo "   Active Users: " . User::where('is_active', true)->count() . "\n";
    echo "   Verified Users: " . User::whereNotNull('email_verified_at')->count() . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🌐 Login Instructions:\n";
echo "======================\n";
echo "1. Go to: http://localhost:8000/login\n";
echo "2. Enter email: <EMAIL>\n";
echo "3. Enter password: password123\n";
echo "4. Click 'Login' button\n";
echo "\n💡 Note: Don't put credentials in URL - use the login form!\n";
?>
