<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RentalExtension extends Model
{
    use HasFactory;

    protected $fillable = [
        'rental_id',
        'old_end_date',
        'new_end_date',
        'extension_days',
        'additional_amount',
        'reason',
        'extended_by',
        'approved_by',
        'status',
        'notes',
    ];

    protected $casts = [
        'old_end_date' => 'date',
        'new_end_date' => 'date',
        'additional_amount' => 'decimal:2',
    ];

    /**
     * Extension statuses
     */
    const STATUSES = [
        'pending' => 'Pending',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
    ];

    /**
     * Get the rental for this extension
     */
    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class);
    }

    /**
     * Get the user who requested the extension
     */
    public function extendedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'extended_by');
    }

    /**
     * Get the user who approved the extension
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Calculate extension days
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($extension) {
            if ($extension->old_end_date && $extension->new_end_date) {
                $extension->extension_days = $extension->old_end_date->diffInDays($extension->new_end_date);
            }
        });
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'approved' => ['label' => 'Approved', 'color' => 'green'],
            'rejected' => ['label' => 'Rejected', 'color' => 'red'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Approve extension
     */
    public function approve($userId = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $userId ?? auth()->id(),
        ]);

        return true;
    }

    /**
     * Reject extension
     */
    public function reject($reason = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'notes' => $reason,
        ]);

        return true;
    }

    /**
     * Scope for pending extensions
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
