@extends('installation.layout')

@section('title', 'Welcome')

@section('content')
<div class="px-8 py-12 text-center">
    <!-- Welcome Header -->
    <div class="mb-8">
        <div class="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-6">
            <i class="fas fa-gas-pump text-4xl text-blue-600"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
            Welcome to GCMS Installation
        </h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Gas Cylinder Management System - Complete solution for industrial gas cylinder tracking, rental management, and business operations.
        </p>
    </div>

    <!-- Features Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-qrcode text-blue-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">QR Code Tracking</h3>
            <p class="text-gray-600 text-sm">Real-time cylinder tracking with QR code scanning and mobile interface.</p>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-chart-line text-green-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Analytics Dashboard</h3>
            <p class="text-gray-600 text-sm">Comprehensive analytics with real-time monitoring and reporting.</p>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-6">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <i class="fab fa-whatsapp text-purple-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">WhatsApp Integration</h3>
            <p class="text-gray-600 text-sm">Automated customer notifications and communication via WhatsApp.</p>
        </div>
    </div>

    <!-- System Requirements Preview -->
    <div class="bg-blue-50 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-blue-900 mb-4">
            <i class="fas fa-server mr-2"></i>
            What You'll Need
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div>
                <h4 class="font-medium text-blue-800 mb-2">Server Requirements:</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><i class="fas fa-check text-green-500 mr-2"></i>PHP 8.1 or higher</li>
                    <li><i class="fas fa-check text-green-500 mr-2"></i>MySQL 8.0 or higher</li>
                    <li><i class="fas fa-check text-green-500 mr-2"></i>Redis (recommended)</li>
                    <li><i class="fas fa-check text-green-500 mr-2"></i>Web server (Apache/Nginx)</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-800 mb-2">Installation Process:</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><i class="fas fa-cog text-blue-500 mr-2"></i>System requirements check</li>
                    <li><i class="fas fa-database text-blue-500 mr-2"></i>Database configuration</li>
                    <li><i class="fas fa-user-shield text-blue-500 mr-2"></i>Admin account setup</li>
                    <li><i class="fas fa-building text-blue-500 mr-2"></i>Company & location setup</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Installation Steps Overview -->
    <div class="bg-gray-50 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-list-ol mr-2"></i>
            Installation Steps
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-left">
            <div class="space-y-2">
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">1</span>
                    <span>Welcome & Overview</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">2</span>
                    <span>Requirements Check</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">3</span>
                    <span>File Permissions</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">4</span>
                    <span>Database Setup</span>
                </div>
            </div>
            <div class="space-y-2">
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">5</span>
                    <span>Database Migration</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">6</span>
                    <span>Admin Account</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">7</span>
                    <span>Company Information</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">8</span>
                    <span>Location Setup</span>
                </div>
            </div>
            <div class="space-y-2">
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">9</span>
                    <span>Gas Types Configuration</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">10</span>
                    <span>Tank Setup</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">11</span>
                    <span>Final Configuration</span>
                </div>
                <div class="flex items-center text-sm">
                    <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">✓</span>
                    <span>Installation Complete</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Important Notes -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
            </div>
            <div class="ml-3 text-left">
                <h3 class="text-lg font-medium text-yellow-800 mb-2">Important Notes</h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• Ensure you have database credentials ready before starting</li>
                    <li>• The installation process will create all necessary database tables</li>
                    <li>• Make sure your web server has write permissions to storage and cache directories</li>
                    <li>• Keep your admin credentials secure - they will have full system access</li>
                    <li>• The installation process typically takes 5-10 minutes to complete</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Start Installation Button -->
    <div class="text-center">
        <a href="{{ route('install.requirements') }}" 
           class="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-lg transition duration-200 transform hover:scale-105">
            <i class="fas fa-rocket mr-3"></i>
            Start Installation
            <i class="fas fa-arrow-right ml-3"></i>
        </a>
        <p class="text-sm text-gray-500 mt-4">
            Ready to set up your Gas Cylinder Management System
        </p>
    </div>
</div>
@endsection
