@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-map-marker-alt text-cyan-500"></i> Location Setup
    </h2>
    <form id="location-setup-form" class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
        @csrf
        <div>
            <label for="name" class="block text-sm font-semibold text-gray-700 mb-1">Location Name</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="name" name="name" required>
        </div>
        <div>
            <label for="address" class="block text-sm font-semibold text-gray-700 mb-1">Address</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="address" name="address" required>
        </div>
        <div>
            <label for="city" class="block text-sm font-semibold text-gray-700 mb-1">City</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="city" name="city" required>
        </div>
        <div>
            <label for="state" class="block text-sm font-semibold text-gray-700 mb-1">State</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="state" name="state" required>
        </div>
        <div>
            <label for="postal_code" class="block text-sm font-semibold text-gray-700 mb-1">Postal Code</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="postal_code" name="postal_code" required>
        </div>
        <div>
            <label for="phone" class="block text-sm font-semibold text-gray-700 mb-1">Phone (Optional)</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="phone" name="phone">
        </div>
        <div>
            <label for="email" class="block text-sm font-semibold text-gray-700 mb-1">Email (Optional)</label>
            <input type="email" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="email" name="email">
        </div>
        <div class="flex items-center mt-6">
            <input type="checkbox" class="h-5 w-5 text-cyan-500 focus:ring-cyan-400 border-gray-300 rounded mr-2" id="is_main" name="is_main" value="1" checked>
            <label class="text-sm font-semibold text-gray-700" for="is_main">Is Main Location</label>
        </div>
        <div class="md:col-span-2 flex flex-col gap-3 mt-2">
            <button type="submit" class="w-full px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center justify-center gap-2">
                <i class="fas fa-save"></i> Save Location
            </button>
        </div>
    </form>

    <div id="location-status" class="mt-3"></div>

    <a href="{{ route('install.gas.types') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    document.getElementById('location-setup-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('location-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Saving location...</div>';

        const formData = new FormData(this);

        fetch('{{ route('install.save.location') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('location-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                let errors = '';
                if (data.errors) {
                    for (const error in data.errors) {
                        errors += '<p>' + data.errors[error][0] + '</p>';
                    }
                }
                document.getElementById('location-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + errors + '</div>';
            }
        });
    });
</script>
@endpush
