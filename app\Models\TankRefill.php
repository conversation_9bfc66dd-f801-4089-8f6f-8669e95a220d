<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TankRefill extends Model
{
    use HasFactory;

    protected $fillable = [
        'refill_number',
        'tank_id',
        'supplier_id',
        'scheduled_date',
        'completed_date',
        'requested_quantity',
        'delivered_quantity',
        'unit_price',
        'total_cost',
        'status',
        'priority',
        'delivery_method',
        'driver_name',
        'vehicle_number',
        'invoice_number',
        'quality_check',
        'temperature_at_delivery',
        'pressure_at_delivery',
        'notes',
        'requested_by',
        'approved_by',
        'received_by',
    ];

    protected $casts = [
        'scheduled_date' => 'datetime',
        'completed_date' => 'datetime',
        'requested_quantity' => 'decimal:2',
        'delivered_quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'temperature_at_delivery' => 'decimal:2',
        'pressure_at_delivery' => 'decimal:2',
        'quality_check' => 'array',
    ];

    /**
     * Refill statuses
     */
    const STATUSES = [
        'scheduled' => 'Scheduled',
        'confirmed' => 'Confirmed',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'delayed' => 'Delayed',
        'failed' => 'Failed',
    ];

    /**
     * Priority levels
     */
    const PRIORITIES = [
        'low' => 'Low',
        'normal' => 'Normal',
        'high' => 'High',
        'urgent' => 'Urgent',
        'emergency' => 'Emergency',
    ];

    /**
     * Delivery methods
     */
    const DELIVERY_METHODS = [
        'truck' => 'Truck Delivery',
        'pipeline' => 'Pipeline',
        'rail' => 'Rail Transport',
        'barge' => 'Barge',
        'pickup' => 'Customer Pickup',
    ];

    /**
     * Get the tank for this refill
     */
    public function tank(): BelongsTo
    {
        return $this->belongsTo(Tank::class);
    }

    /**
     * Get the supplier for this refill
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who requested this refill
     */
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the user who approved this refill
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who received this refill
     */
    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Generate unique refill number
     */
    public static function generateRefillNumber(): string
    {
        $prefix = 'REF-' . date('Ymd') . '-';
        $lastRefill = static::where('refill_number', 'like', $prefix . '%')
                           ->orderBy('id', 'desc')
                           ->first();

        if ($lastRefill) {
            $lastNumber = (int) substr($lastRefill->refill_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'scheduled' => ['label' => 'Scheduled', 'color' => 'blue'],
            'confirmed' => ['label' => 'Confirmed', 'color' => 'green'],
            'in_progress' => ['label' => 'In Progress', 'color' => 'yellow'],
            'completed' => ['label' => 'Completed', 'color' => 'green'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'gray'],
            'delayed' => ['label' => 'Delayed', 'color' => 'orange'],
            'failed' => ['label' => 'Failed', 'color' => 'red'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get priority label with color
     */
    public function getPriorityLabel(): array
    {
        return match($this->priority) {
            'low' => ['label' => 'Low', 'color' => 'gray'],
            'normal' => ['label' => 'Normal', 'color' => 'blue'],
            'high' => ['label' => 'High', 'color' => 'yellow'],
            'urgent' => ['label' => 'Urgent', 'color' => 'orange'],
            'emergency' => ['label' => 'Emergency', 'color' => 'red'],
            default => ['label' => ucfirst($this->priority), 'color' => 'gray']
        };
    }

    /**
     * Check if refill is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_date->isPast();
    }

    /**
     * Calculate delivery variance
     */
    public function getDeliveryVariance(): float
    {
        if (!$this->delivered_quantity || !$this->requested_quantity) {
            return 0;
        }

        return $this->delivered_quantity - $this->requested_quantity;
    }

    /**
     * Calculate delivery variance percentage
     */
    public function getDeliveryVariancePercentage(): float
    {
        if (!$this->requested_quantity) {
            return 0;
        }

        return ($this->getDeliveryVariance() / $this->requested_quantity) * 100;
    }

    /**
     * Mark as completed
     */
    public function markAsCompleted(array $completionData): bool
    {
        if ($this->status !== 'in_progress') {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'completed_date' => now(),
            'delivered_quantity' => $completionData['delivered_quantity'],
            'temperature_at_delivery' => $completionData['temperature_at_delivery'] ?? null,
            'pressure_at_delivery' => $completionData['pressure_at_delivery'] ?? null,
            'quality_check' => $completionData['quality_check'] ?? null,
            'received_by' => auth()->id(),
            'notes' => $completionData['notes'] ?? $this->notes,
        ]);

        // Update tank level
        $this->tank->updateLevel(
            $this->tank->current_level + $this->delivered_quantity,
            'refill'
        );

        // Update last refill date
        $this->tank->update(['last_refill_date' => now()]);

        return true;
    }

    /**
     * Auto-generate refill number before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($refill) {
            if (!$refill->refill_number) {
                $refill->refill_number = static::generateRefillNumber();
            }
        });
    }
}
