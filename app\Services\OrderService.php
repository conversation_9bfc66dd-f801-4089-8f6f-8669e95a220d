<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use App\Models\Cylinder;
use App\Models\Inventory;
use App\Models\StockMovement;
use App\Models\Invoice;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class OrderService
{
    /**
     * Create a new order
     */
    public function createOrder(array $orderData, array $items): Order
    {
        DB::beginTransaction();
        
        try {
            // Create the order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'customer_id' => $orderData['customer_id'],
                'location_id' => $orderData['location_id'],
                'type' => $orderData['type'],
                'priority' => $orderData['priority'] ?? 'normal',
                'status' => 'pending',
                'delivery_address' => $orderData['delivery_address'] ?? null,
                'special_instructions' => $orderData['special_instructions'] ?? null,
                'scheduled_at' => $orderData['scheduled_at'] ?? null,
                'payment_method' => $orderData['payment_method'] ?? 'cash',
                'payment_status' => 'pending',
            ]);

            $totalAmount = 0;
            $totalTax = 0;
            $totalDiscount = 0;

            // Create order items
            foreach ($items as $itemData) {
                $orderItem = OrderItem::create([
                    'order_id' => $order->id,
                    'gas_type_id' => $itemData['gas_type_id'],
                    'quantity' => $itemData['quantity'],
                    'rate' => $itemData['rate'],
                    'rental_days' => $itemData['rental_days'] ?? null,
                    'discount_amount' => $itemData['discount_amount'] ?? 0,
                    'tax_amount' => $itemData['tax_amount'] ?? 0,
                    'status' => 'pending',
                    'notes' => $itemData['notes'] ?? null,
                ]);

                $totalAmount += $orderItem->subtotal;
                $totalTax += $orderItem->tax_amount;
                $totalDiscount += $orderItem->discount_amount;
            }

            // Update order totals
            $order->update([
                'total_amount' => $totalAmount,
                'tax_amount' => $totalTax,
                'discount_amount' => $totalDiscount,
                'final_amount' => $totalAmount - $totalDiscount + $totalTax,
            ]);

            DB::commit();
            return $order->load(['items.gasType', 'customer', 'location']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Allocate inventory to order
     */
    public function allocateInventory(Order $order): bool
    {
        DB::beginTransaction();
        
        try {
            foreach ($order->items as $item) {
                if ($item->isFullyAllocated()) {
                    continue;
                }

                $remainingQuantity = $item->getRemainingQuantity();
                
                // Find available cylinders
                $availableCylinders = Cylinder::where('location_id', $order->location_id)
                                            ->where('gas_type_id', $item->gas_type_id)
                                            ->where('status', 'full')
                                            ->limit($remainingQuantity)
                                            ->get();

                if ($availableCylinders->count() < $remainingQuantity) {
                    throw new \Exception("Insufficient inventory for {$item->gasType->name}");
                }

                // Allocate cylinders
                $item->allocateCylinders($availableCylinders->pluck('id')->toArray());

                // Create stock movement
                StockMovement::createMovement([
                    'location_id' => $order->location_id,
                    'gas_type_id' => $item->gas_type_id,
                    'movement_type' => 'out',
                    'quantity' => $availableCylinders->count(),
                    'reference_type' => 'Order',
                    'reference_id' => $order->id,
                    'notes' => "Allocated for order {$order->order_number}",
                ]);
            }

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Release allocated inventory
     */
    public function releaseInventory(Order $order): bool
    {
        DB::beginTransaction();
        
        try {
            foreach ($order->items as $item) {
                if ($item->allocated_cylinders) {
                    // Create stock movement for return
                    StockMovement::createMovement([
                        'location_id' => $order->location_id,
                        'gas_type_id' => $item->gas_type_id,
                        'movement_type' => 'in',
                        'quantity' => count($item->allocated_cylinders),
                        'reference_type' => 'Order',
                        'reference_id' => $order->id,
                        'notes' => "Released from cancelled order {$order->order_number}",
                    ]);

                    $item->releaseAllocatedCylinders();
                }
            }

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Assign order to staff member
     */
    public function assignOrder(Order $order, $userId, $scheduledAt = null): bool
    {
        if (!$order->canBeAssigned()) {
            throw new \Exception('Order cannot be assigned in its current status');
        }

        // Try to allocate inventory first
        $this->allocateInventory($order);

        return $order->assignTo($userId, $scheduledAt);
    }

    /**
     * Complete order delivery
     */
    public function completeDelivery(Order $order, array $deliveryData): bool
    {
        if (!$order->canBeDelivered()) {
            throw new \Exception('Order cannot be delivered in its current status');
        }

        DB::beginTransaction();
        
        try {
            // Complete the delivery
            $order->completeDelivery(
                $deliveryData['delivery_proof'] ?? null,
                $deliveryData['notes'] ?? null
            );

            // Update payment status if cash payment
            if ($order->payment_method === 'cash' && $deliveryData['payment_received']) {
                $order->update([
                    'payment_status' => 'paid',
                    'payment_reference' => $deliveryData['payment_reference'] ?? null,
                ]);
            }

            // Generate invoice if not exists
            if (!$order->invoice) {
                $this->generateInvoice($order);
            }

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Cancel order
     */
    public function cancelOrder(Order $order, $reason = null): bool
    {
        DB::beginTransaction();
        
        try {
            // Release allocated inventory
            $this->releaseInventory($order);

            // Cancel the order
            $order->cancel($reason);

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Generate invoice for order
     */
    public function generateInvoice(Order $order): Invoice
    {
        if ($order->invoice) {
            return $order->invoice;
        }

        $invoice = Invoice::create([
            'invoice_number' => $this->generateInvoiceNumber(),
            'order_id' => $order->id,
            'customer_id' => $order->customer_id,
            'location_id' => $order->location_id,
            'subtotal' => $order->total_amount,
            'tax_amount' => $order->tax_amount,
            'discount_amount' => $order->discount_amount,
            'total_amount' => $order->final_amount,
            'status' => $order->payment_status === 'paid' ? 'paid' : 'pending',
            'due_date' => now()->addDays(30),
            'issued_at' => now(),
        ]);

        return $invoice;
    }

    /**
     * Get order statistics
     */
    public function getOrderStatistics($locationIds = null, $days = 30)
    {
        $query = Order::where('created_at', '>=', now()->subDays($days));
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $orders = $query->get();

        return [
            'total_orders' => $orders->count(),
            'pending_orders' => $orders->where('status', 'pending')->count(),
            'assigned_orders' => $orders->where('status', 'assigned')->count(),
            'in_progress_orders' => $orders->where('status', 'in_progress')->count(),
            'delivered_orders' => $orders->where('status', 'delivered')->count(),
            'cancelled_orders' => $orders->where('status', 'cancelled')->count(),
            'total_revenue' => $orders->where('payment_status', 'paid')->sum('final_amount'),
            'pending_revenue' => $orders->where('payment_status', 'pending')->sum('final_amount'),
            'average_order_value' => $orders->avg('final_amount'),
            'overdue_orders' => $orders->filter(fn($order) => $order->isOverdue())->count(),
        ];
    }

    /**
     * Get orders requiring attention
     */
    public function getOrdersRequiringAttention($locationIds = null)
    {
        $query = Order::with(['customer', 'location', 'assignedTo']);
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return [
            'overdue' => $query->clone()->overdue()->get(),
            'high_priority' => $query->clone()->highPriority()->whereNotIn('status', ['delivered', 'completed', 'cancelled'])->get(),
            'pending_assignment' => $query->clone()->pending()->get(),
            'payment_overdue' => $query->clone()->where('payment_status', 'overdue')->get(),
        ];
    }

    /**
     * Generate invoice number
     */
    private function generateInvoiceNumber(): string
    {
        $prefix = 'INV-' . date('Ymd') . '-';
        $lastInvoice = Invoice::where('invoice_number', 'like', $prefix . '%')
                             ->orderBy('id', 'desc')
                             ->first();
        
        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
