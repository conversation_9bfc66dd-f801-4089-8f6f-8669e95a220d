@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-flask text-cyan-500"></i> Gas Types Setup
    </h2>
    <form id="gas-types-form" class="space-y-6 mt-4">
        @csrf
        <div id="gas-types-container" class="space-y-6">
            <div class="gas-type-group grid grid-cols-1 md:grid-cols-4 gap-4 items-end bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Gas Name</label>
                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="gas_types[0][name]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Code</label>
                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="gas_types[0][code]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Description</label>
                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="gas_types[0][description]">
                </div>
                <div class="flex items-center gap-2 mt-6">
                    <input type="checkbox" class="h-5 w-5 text-cyan-500 focus:ring-cyan-400 border-gray-300 rounded" name="gas_types[0][is_hazardous]" value="1">
                    <label class="text-sm font-semibold text-gray-700">Is Hazardous</label>
                </div>
            </div>
        </div>
        <div class="flex flex-col md:flex-row gap-3 mt-2">
            <button type="button" class="w-full md:w-auto px-6 py-2 rounded-lg bg-gradient-to-tr from-gray-200 to-gray-300 text-gray-700 font-semibold shadow hover:bg-gray-300 transition flex items-center gap-2" id="add-gas-type">
                <i class="fas fa-plus"></i> Add Another Gas Type
            </button>
            <button type="submit" class="w-full md:w-auto px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center gap-2">
                <i class="fas fa-save"></i> Save Gas Types
            </button>
        </div>
    </form>

    <div id="gas-types-status" class="mt-3"></div>

    <a href="{{ route('install.tank.setup') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    let gasTypeIndex = 1;
    document.getElementById('add-gas-type').addEventListener('click', function () {
        const container = document.getElementById('gas-types-container');
        const newGasType = document.createElement('div');
        newGasType.className = 'gas-type-group grid grid-cols-1 md:grid-cols-4 gap-4 items-end bg-gray-50 p-4 rounded-lg border border-gray-200';
        newGasType.innerHTML = `
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Gas Name</label>
                <input type='text' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='gas_types[${gasTypeIndex}][name]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Code</label>
                <input type='text' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='gas_types[${gasTypeIndex}][code]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Description</label>
                <input type='text' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='gas_types[${gasTypeIndex}][description]'>
            </div>
            <div class='flex items-center gap-2 mt-6'>
                <input type='checkbox' class='h-5 w-5 text-cyan-500 focus:ring-cyan-400 border-gray-300 rounded' name='gas_types[${gasTypeIndex}][is_hazardous]' value='1'>
                <label class='text-sm font-semibold text-gray-700'>Is Hazardous</label>
            </div>
        `;
        container.appendChild(newGasType);
        gasTypeIndex++;
    });

    document.getElementById('gas-types-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('gas-types-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Saving gas types...</div>';

        const formData = new FormData(this);

        fetch('{{ route('install.save.gas.types') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('gas-types-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                let errors = '';
                if (data.errors) {
                    for (const error in data.errors) {
                        errors += '<p>' + data.errors[error][0] + '</p>';
                    }
                }
                document.getElementById('gas-types-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + errors + '</div>';
            }
        });
    });
</script>
@endpush
