<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\Location;
use App\Models\GasType;
use App\Services\InventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InventoryController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_inventory', ['only' => ['index', 'show']]);
        $this->middleware('permission:manage_inventory', ['only' => ['create', 'store', 'edit', 'update', 'destroy']]);
        $this->inventoryService = $inventoryService;
    }

    /**
     * Display inventory overview
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        // Get inventory overview
        $inventoryOverview = $this->inventoryService->getInventoryOverview($accessibleLocationIds);

        // Get statistics
        $statistics = $this->inventoryService->getInventoryStatistics($accessibleLocationIds);

        // Get alerts
        $lowStockAlerts = $this->inventoryService->getLowStockAlerts($accessibleLocationIds);
        $criticalStockAlerts = $this->inventoryService->getCriticalStockAlerts($accessibleLocationIds);

        // Get pending transfers
        $pendingTransfers = $this->inventoryService->getPendingTransfers($accessibleLocationIds);

        $locations = $this->getAccessibleLocations($user);
        $gasTypes = GasType::active()->get();

        return view('inventory.index', compact(
            'inventoryOverview',
            'statistics',
            'lowStockAlerts',
            'criticalStockAlerts',
            'pendingTransfers',
            'locations',
            'gasTypes'
        ));
    }

    /**
     * Show inventory for specific location
     */
    public function show(Request $request, $locationId)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($locationId)) {
            abort(403, 'You do not have access to this location.');
        }

        $location = Location::findOrFail($locationId);

        // Get inventory for this location
        $inventories = Inventory::with(['gasType'])
                               ->where('location_id', $locationId)
                               ->get();

        // Get stock movement history
        $stockMovements = $this->inventoryService->getStockMovementHistory($locationId, null, 30);

        // Get transfer history
        $transfers = $this->inventoryService->getTransferHistory([$locationId], 30);

        return view('inventory.show', compact('location', 'inventories', 'stockMovements', 'transfers'));
    }

    /**
     * Sync inventory with actual cylinder counts
     */
    public function sync(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationId = $request->location_id;

        if ($locationId && !in_array($locationId, $accessibleLocationIds)) {
            abort(403, 'You do not have access to this location.');
        }

        $syncedCount = $this->inventoryService->syncAllInventory(
            $locationId ? [$locationId] : $accessibleLocationIds
        );

        return response()->json([
            'success' => true,
            'message' => "Synced {$syncedCount} inventory items successfully.",
        ]);
    }

    /**
     * Create stock adjustment
     */
    public function adjust(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'location_id' => 'required|exists:locations,id',
            'gas_type_id' => 'required|exists:gas_types,id',
            'adjustments' => 'required|array',
            'reason' => 'nullable|string|max:1000',
        ]);

        // Check location access
        if (!$user->hasLocationAccess($request->location_id)) {
            abort(403, 'You do not have access to this location.');
        }

        try {
            $inventory = $this->inventoryService->createStockAdjustment(
                $request->location_id,
                $request->gas_type_id,
                $request->adjustments,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'Stock adjustment created successfully.',
                'inventory' => $inventory->fresh(['location', 'gasType']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Update stock levels
     */
    public function updateLevels(Request $request, Inventory $inventory)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($inventory->location_id)) {
            abort(403, 'You do not have access to this inventory item.');
        }

        $request->validate([
            'min_stock_level' => 'required|integer|min:0',
            'max_stock_level' => 'required|integer|min:1',
            'reorder_level' => 'required|integer|min:0',
        ]);

        try {
            $updatedInventory = $this->inventoryService->updateStockLevels(
                $inventory->id,
                $request->min_stock_level,
                $request->max_stock_level,
                $request->reorder_level
            );

            return response()->json([
                'success' => true,
                'message' => 'Stock levels updated successfully.',
                'inventory' => $updatedInventory,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get reorder suggestions
     */
    public function reorderSuggestions(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $suggestions = $this->inventoryService->getReorderSuggestions($accessibleLocationIds);

        return response()->json([
            'suggestions' => $suggestions,
        ]);
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
