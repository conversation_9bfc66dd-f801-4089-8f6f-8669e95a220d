<?php

namespace App\Services;

use App\Models\Rental;
use App\Models\RentalBilling;
use App\Models\RentalExtension;
use App\Models\Order;
use App\Models\Cylinder;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class RentalService
{
    /**
     * Create a new rental from order
     */
    public function createRentalFromOrder(Order $order, array $rentalData): Rental
    {
        DB::beginTransaction();
        
        try {
            // Get the first order item (assuming single item for rental)
            $orderItem = $order->items->first();
            
            if (!$orderItem) {
                throw new \Exception('Order has no items');
            }

            // Get allocated cylinder
            $cylinderId = $orderItem->allocated_cylinders ? $orderItem->allocated_cylinders[0] : null;
            
            if (!$cylinderId) {
                throw new \Exception('No cylinder allocated for rental');
            }

            $rental = Rental::create([
                'rental_number' => Rental::generateRentalNumber(),
                'order_id' => $order->id,
                'customer_id' => $order->customer_id,
                'location_id' => $order->location_id,
                'cylinder_id' => $cylinderId,
                'gas_type_id' => $orderItem->gas_type_id,
                'rental_type' => $rentalData['rental_type'],
                'status' => 'pending',
                'start_date' => $rentalData['start_date'],
                'end_date' => $rentalData['end_date'],
                'daily_rate' => $rentalData['daily_rate'],
                'weekly_rate' => $rentalData['weekly_rate'] ?? null,
                'monthly_rate' => $rentalData['monthly_rate'] ?? null,
                'deposit_amount' => $rentalData['deposit_amount'],
                'billing_cycle' => $rentalData['billing_cycle'],
                'auto_renew' => $rentalData['auto_renew'] ?? false,
                'renewal_period' => $rentalData['renewal_period'] ?? null,
                'terms_conditions' => $rentalData['terms_conditions'] ?? null,
                'special_instructions' => $rentalData['special_instructions'] ?? null,
                'delivery_address' => $rentalData['delivery_address'] ?? null,
                'assigned_to' => $rentalData['assigned_to'] ?? null,
            ]);

            // Calculate initial amounts
            $rental->total_amount = $rental->calculateCurrentAmount();
            $rental->outstanding_amount = $rental->total_amount;
            $rental->save();

            DB::commit();
            return $rental->load(['customer', 'location', 'cylinder', 'gasType']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Activate rental
     */
    public function activateRental(Rental $rental): bool
    {
        DB::beginTransaction();
        
        try {
            if (!$rental->activate()) {
                throw new \Exception('Cannot activate rental in current status');
            }

            // Create initial billing if not upfront
            if ($rental->billing_cycle !== 'upfront') {
                $this->createBillingRecord($rental);
            }

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process rental return
     */
    public function processReturn(Rental $rental, array $returnData): bool
    {
        DB::beginTransaction();
        
        try {
            $returnDate = $returnData['return_date'] ?? now();
            $condition = $returnData['condition'] ?? 'good';
            $notes = $returnData['notes'] ?? null;

            if (!$rental->processReturn($returnDate, $condition, $notes)) {
                throw new \Exception('Cannot process return for rental in current status');
            }

            // Create final billing if needed
            if ($rental->outstanding_amount > 0) {
                $this->createFinalBilling($rental);
            }

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Extend rental period
     */
    public function extendRental(Rental $rental, $newEndDate, $reason = null): RentalExtension
    {
        DB::beginTransaction();
        
        try {
            $extension = RentalExtension::create([
                'rental_id' => $rental->id,
                'old_end_date' => $rental->end_date,
                'new_end_date' => $newEndDate,
                'reason' => $reason,
                'extended_by' => Auth::id(),
                'status' => 'pending',
            ]);

            // Auto-approve if user has permission
            if (Auth::user()->can('approve_rental_extensions')) {
                $extension->approve();
                $rental->extend($newEndDate, $reason);
            }

            DB::commit();
            return $extension;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process rental billing
     */
    public function processBilling(Rental $rental): ?RentalBilling
    {
        if (!$rental->needsBilling()) {
            return null;
        }

        return $this->createBillingRecord($rental);
    }

    /**
     * Process payment for billing
     */
    public function processPayment(RentalBilling $billing, array $paymentData): bool
    {
        return $billing->markAsPaid(
            $paymentData['payment_method'] ?? null,
            $paymentData['payment_reference'] ?? null
        );
    }

    /**
     * Get rental statistics
     */
    public function getRentalStatistics($locationIds = null, $days = 30)
    {
        $query = Rental::where('created_at', '>=', now()->subDays($days));
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $rentals = $query->get();

        return [
            'total_rentals' => $rentals->count(),
            'active_rentals' => $rentals->where('status', 'active')->count(),
            'overdue_rentals' => $rentals->filter(fn($r) => $r->isOverdue())->count(),
            'returned_rentals' => $rentals->where('status', 'returned')->count(),
            'cancelled_rentals' => $rentals->where('status', 'cancelled')->count(),
            'total_revenue' => $rentals->sum('paid_amount'),
            'outstanding_amount' => $rentals->sum('outstanding_amount'),
            'average_rental_duration' => $rentals->avg(fn($r) => $r->getDurationInDays()),
            'deposit_collected' => $rentals->sum('deposit_amount'),
        ];
    }

    /**
     * Get rentals requiring attention
     */
    public function getRentalsRequiringAttention($locationIds = null)
    {
        $query = Rental::with(['customer', 'location', 'cylinder', 'gasType']);
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return [
            'overdue' => $query->clone()->overdue()->get(),
            'needs_billing' => $query->clone()->needsBilling()->get(),
            'pending_extensions' => RentalExtension::with(['rental.customer'])->pending()->get(),
            'ending_soon' => $query->clone()->active()
                                  ->whereBetween('end_date', [now(), now()->addDays(7)])
                                  ->get(),
        ];
    }

    /**
     * Auto-renew eligible rentals
     */
    public function processAutoRenewals(): int
    {
        $renewableRentals = Rental::where('auto_renew', true)
                                 ->where('status', 'active')
                                 ->where('end_date', '<=', now()->addDays(1))
                                 ->get();

        $renewed = 0;
        
        foreach ($renewableRentals as $rental) {
            try {
                $newEndDate = match($rental->renewal_period) {
                    'weekly' => $rental->end_date->addWeek(),
                    'monthly' => $rental->end_date->addMonth(),
                    default => $rental->end_date->addDays(30)
                };

                $rental->extend($newEndDate, 'Auto-renewal');
                $renewed++;
                
            } catch (\Exception $e) {
                // Log error but continue with other renewals
                \Log::error("Failed to auto-renew rental {$rental->id}: " . $e->getMessage());
            }
        }

        return $renewed;
    }

    /**
     * Create billing record
     */
    private function createBillingRecord(Rental $rental): RentalBilling
    {
        $periodStart = $rental->start_date;
        $periodEnd = match($rental->billing_cycle) {
            'daily' => $periodStart->copy()->addDay(),
            'weekly' => $periodStart->copy()->addWeek(),
            'monthly' => $periodStart->copy()->addMonth(),
            default => $periodStart->copy()->addDay()
        };

        $amount = match($rental->billing_cycle) {
            'daily' => $rental->daily_rate,
            'weekly' => $rental->weekly_rate ?? ($rental->daily_rate * 7),
            'monthly' => $rental->monthly_rate ?? ($rental->daily_rate * 30),
            default => $rental->daily_rate
        };

        return RentalBilling::create([
            'rental_id' => $rental->id,
            'billing_number' => RentalBilling::generateBillingNumber(),
            'billing_period_start' => $periodStart,
            'billing_period_end' => $periodEnd,
            'amount' => $amount,
            'total_amount' => $amount,
            'status' => 'pending',
            'due_date' => now()->addDays(7), // 7 days to pay
        ]);
    }

    /**
     * Create final billing for rental completion
     */
    private function createFinalBilling(Rental $rental): RentalBilling
    {
        $amount = $rental->outstanding_amount;
        
        return RentalBilling::create([
            'rental_id' => $rental->id,
            'billing_number' => RentalBilling::generateBillingNumber(),
            'billing_period_start' => $rental->start_date,
            'billing_period_end' => $rental->actual_return_date ?? $rental->end_date,
            'amount' => $amount - $rental->late_fee - $rental->damage_fee,
            'late_fee' => $rental->late_fee,
            'total_amount' => $amount,
            'status' => 'pending',
            'due_date' => now()->addDays(7),
        ]);
    }
}
