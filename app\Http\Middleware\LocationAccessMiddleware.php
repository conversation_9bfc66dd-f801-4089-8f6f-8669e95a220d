<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LocationAccessMiddleware
{
    /**
     * Handle an incoming request.
     * Check if user has access to the specified location
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $parameterName = 'location'): Response
    {
        $user = $request->user();

        // Allow if user is not authenticated (will be handled by auth middleware)
        if (!$user) {
            return $next($request);
        }

        // Super Admin and Admin have access to all locations
        if ($user->hasAnyRole(['super_admin', 'admin'])) {
            return $next($request);
        }

        // Get location ID from route parameter
        $locationId = $request->route($parameterName);

        // If no location specified, allow (general access)
        if (!$locationId) {
            return $next($request);
        }

        // Check if user has access to this location
        if (!$user->hasLocationAccess($locationId)) {
            abort(403, 'You do not have access to this location.');
        }

        return $next($request);
    }
}
