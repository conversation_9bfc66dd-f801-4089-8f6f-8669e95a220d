<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CompanySetting;

class CompanySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        CompanySetting::create([
            'name' => 'Industrial Gas Solutions Ltd.',
            'address' => '123 Industrial Avenue, Gas City, GC 12345',
            'phone' => '******-GAS-TANK',
            'email' => '<EMAIL>',
            'gst_number' => 'GST123456789',
            'website' => 'https://www.industrialgas.com',
            'settings' => [
                'currency' => 'USD',
                'timezone' => 'America/New_York',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
                'default_rental_days' => 30,
                'late_fee_per_day' => 5.00,
                'grace_period_days' => 3,
                'auto_generate_invoices' => true,
                'send_whatsapp_notifications' => true,
                'require_delivery_otp' => true,
                'cylinder_inspection_interval_months' => 12,
                'backup_frequency' => 'daily',
                'maintenance_mode' => false,
                'features' => [
                    'whatsapp_integration' => true,
                    'qr_code_scanning' => true,
                    'mobile_app' => true,
                    'analytics_dashboard' => true,
                    'automated_billing' => true,
                    'inventory_alerts' => true,
                    'delivery_tracking' => true,
                    'customer_portal' => true,
                ],
                'notification_settings' => [
                    'low_stock_threshold' => 10,
                    'expiry_alert_days' => 30,
                    'inspection_due_days' => 7,
                    'overdue_rental_days' => 1,
                ],
                'security_settings' => [
                    'session_timeout_minutes' => 120,
                    'password_expiry_days' => 90,
                    'max_login_attempts' => 5,
                    'require_2fa' => false,
                ],
            ],
            'is_active' => true,
        ]);

        $this->command->info('Company settings created successfully!');
    }
}
