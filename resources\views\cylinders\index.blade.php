<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Cylinder Management') }}
            </h2>
            <div class="flex space-x-2">
                @can('scan_cylinders')
                    <a href="{{ route('scan.index') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        📱 Scan Cylinder
                    </a>
                @endcan
                @can('create_cylinders')
                    <a href="{{ route('cylinders.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add New Cylinder
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Status Overview Cards -->
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
                @php
                    $statusCounts = $cylinders->groupBy('status')->map->count();
                    $statusColors = [
                        'empty' => 'bg-gray-100 text-gray-800',
                        'full' => 'bg-green-100 text-green-800',
                        'in_use' => 'bg-blue-100 text-blue-800',
                        'damaged' => 'bg-red-100 text-red-800',
                        'expired' => 'bg-orange-100 text-orange-800',
                        'maintenance' => 'bg-yellow-100 text-yellow-800',
                        'in_transit' => 'bg-purple-100 text-purple-800',
                    ];
                @endphp
                
                @foreach(['empty', 'full', 'in_use', 'damaged', 'expired', 'maintenance', 'in_transit'] as $status)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-4 text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                {{ $statusCounts->get($status, 0) }}
                            </div>
                            <div class="text-sm {{ $statusColors[$status] }} px-2 py-1 rounded-full inline-block mt-1">
                                {{ ucfirst(str_replace('_', ' ', $status)) }}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Filters -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('cylinders.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Cylinder ID or QR Code"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <select name="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Statuses</option>
                                @foreach(['empty', 'full', 'in_use', 'damaged', 'expired', 'maintenance', 'in_transit'] as $status)
                                    <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $status)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Gas Type</label>
                            <select name="gas_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Gas Types</option>
                                @foreach($gasTypes as $gasType)
                                    <option value="{{ $gasType->id }}" {{ request('gas_type') == $gasType->id ? 'selected' : '' }}>
                                        {{ $gasType->name }} ({{ $gasType->code }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
                            <select name="location" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Locations</option>
                                @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ request('location') == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                            <a href="{{ route('cylinders.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('cylinders.index', ['filter' => 'expired']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                        🚨 Expired Cylinders
                    </a>
                    <a href="{{ route('cylinders.index', ['filter' => 'inspection_due']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                        🔍 Inspection Due
                    </a>
                    <a href="{{ route('cylinders.index', ['status' => 'damaged']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-500 hover:bg-red-600">
                        🔧 Damaged
                    </a>
                    <a href="{{ route('cylinders.index', ['status' => 'maintenance']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-500 hover:bg-orange-600">
                        🛠️ Maintenance
                    </a>
                </div>
            </div>

            <!-- Cylinders Table -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Cylinder
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Gas Type
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Location
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Last Activity
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @forelse($cylinders as $cylinder)
                                    @php
                                        $statusLabel = $cylinder->getStatusLabel();
                                    @endphp
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full flex items-center justify-center text-white text-xs font-bold"
                                                         style="background-color: {{ $cylinder->gasType->getDisplayColor() }}">
                                                        {{ $cylinder->gasType->code }}
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ $cylinder->unique_id }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        QR: {{ $cylinder->qr_code }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ $cylinder->capacity }}L
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->gasType->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $cylinder->gasType->code }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                                {{ $statusLabel['label'] }}
                                            </span>
                                            @if($cylinder->isExpired())
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 ml-1">
                                                    Expired
                                                </span>
                                            @endif
                                            @if($cylinder->needsInspection())
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 ml-1">
                                                    Inspection Due
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->location->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $cylinder->location->getTypeLabel() }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ $cylinder->updated_at->diffForHumans() }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex justify-end space-x-2">
                                                @can('view_cylinders')
                                                    <a href="{{ route('cylinders.show', $cylinder) }}" class="text-indigo-600 hover:text-indigo-900">
                                                        View
                                                    </a>
                                                @endcan
                                                @can('generate_qr_codes')
                                                    <a href="{{ route('cylinders.qr', $cylinder) }}" target="_blank" class="text-green-600 hover:text-green-900">
                                                        QR
                                                    </a>
                                                @endcan
                                                @can('edit_cylinders')
                                                    <a href="{{ route('cylinders.edit', $cylinder) }}" class="text-indigo-600 hover:text-indigo-900">
                                                        Edit
                                                    </a>
                                                @endcan
                                                @can('delete_cylinders')
                                                    @if($cylinder->status !== 'in_use')
                                                        <form method="POST" action="{{ route('cylinders.destroy', $cylinder) }}" class="inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-red-600 hover:text-red-900" 
                                                                    onclick="return confirm('Are you sure you want to delete this cylinder?')">
                                                                Delete
                                                            </button>
                                                        </form>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                            No cylinders found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $cylinders->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
