<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="theme-color" content="#3b82f6">

    <title>Offline - {{ config('app.name', 'GCMS') }}</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        
        .offline-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .offline-message {
            color: #6b7280;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 30px;
        }
        
        .offline-features {
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .offline-features h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #4b5563;
            font-size: 14px;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
            transition: background-color 0.2s;
        }
        
        .retry-button:hover {
            background: #2563eb;
        }
        
        .retry-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .home-button {
            background: transparent;
            color: #6b7280;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 10px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: all 0.2s;
        }
        
        .home-button:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .connection-status.offline {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .connection-status.online {
            background: #f0fdf4;
            color: #16a34a;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-dot.offline {
            background: #dc2626;
        }
        
        .status-dot.online {
            background: #16a34a;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .cached-data {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .cached-data h4 {
            color: #92400e;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .cached-data p {
            color: #b45309;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>

<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            No internet connection detected. Don't worry, you can still access some features while offline.
        </p>
        
        <div class="offline-features">
            <h3>Available Offline</h3>
            <ul class="feature-list">
                <li>View cached dashboard data</li>
                <li>Access recent customer information</li>
                <li>View previously loaded tank levels</li>
                <li>Browse cached order history</li>
                <li>Use QR scanner (limited functionality)</li>
            </ul>
        </div>
        
        <button id="retryButton" class="retry-button" onclick="checkConnection()">
            <span id="retryText">Check Connection</span>
        </button>
        
        <button class="home-button" onclick="goHome()">
            Go to Offline Dashboard
        </button>
        
        <div id="connectionStatus" class="connection-status offline">
            <div class="status-dot offline pulse"></div>
            <span>No Internet Connection</span>
        </div>
        
        <div class="cached-data">
            <h4>📱 Offline Mode Active</h4>
            <p>
                Your actions will be saved locally and synchronized when you're back online. 
                Some features may have limited functionality.
            </p>
        </div>
    </div>

    <script>
        let retryAttempts = 0;
        const maxRetryAttempts = 3;
        
        function checkConnection() {
            const retryButton = document.getElementById('retryButton');
            const retryText = document.getElementById('retryText');
            const connectionStatus = document.getElementById('connectionStatus');
            
            retryButton.disabled = true;
            retryText.textContent = 'Checking...';
            
            // Simulate connection check
            fetch('/mobile/dashboard', {
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    // Connection restored
                    updateConnectionStatus(true);
                    setTimeout(() => {
                        window.location.href = '/mobile/dashboard';
                    }, 1000);
                } else {
                    throw new Error('Connection failed');
                }
            })
            .catch(error => {
                console.log('Still offline:', error);
                retryAttempts++;
                
                if (retryAttempts >= maxRetryAttempts) {
                    retryText.textContent = 'Still Offline';
                    setTimeout(() => {
                        retryText.textContent = 'Try Again';
                        retryButton.disabled = false;
                        retryAttempts = 0;
                    }, 3000);
                } else {
                    retryText.textContent = 'Try Again';
                    retryButton.disabled = false;
                }
                
                updateConnectionStatus(false);
            });
        }
        
        function updateConnectionStatus(isOnline) {
            const connectionStatus = document.getElementById('connectionStatus');
            const statusDot = connectionStatus.querySelector('.status-dot');
            const statusText = connectionStatus.querySelector('span');
            
            if (isOnline) {
                connectionStatus.className = 'connection-status online';
                statusDot.className = 'status-dot online';
                statusText.textContent = 'Connection Restored!';
            } else {
                connectionStatus.className = 'connection-status offline';
                statusDot.className = 'status-dot offline pulse';
                statusText.textContent = 'No Internet Connection';
            }
        }
        
        function goHome() {
            // Try to load cached dashboard
            if ('caches' in window) {
                caches.match('/mobile/dashboard')
                    .then(response => {
                        if (response) {
                            window.location.href = '/mobile/dashboard';
                        } else {
                            // Show offline dashboard with cached data
                            showOfflineDashboard();
                        }
                    })
                    .catch(() => {
                        showOfflineDashboard();
                    });
            } else {
                showOfflineDashboard();
            }
        }
        
        function showOfflineDashboard() {
            // Create a simple offline dashboard
            document.body.innerHTML = `
                <div style="padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <div style="background: #3b82f6; color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h1 style="margin: 0; font-size: 24px;">GCMS - Offline Mode</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">Limited functionality available</p>
                    </div>
                    
                    <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <p style="color: #dc2626; margin: 0; font-weight: 500;">⚠️ You are currently offline</p>
                        <p style="color: #b91c1c; margin: 5px 0 0 0; font-size: 14px;">Some features may not work properly</p>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-size: 32px; margin-bottom: 10px;">📱</div>
                            <h3 style="margin: 0; font-size: 16px; color: #1f2937;">QR Scanner</h3>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #6b7280;">Limited offline mode</p>
                        </div>
                        
                        <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="font-size: 32px; margin-bottom: 10px;">💾</div>
                            <h3 style="margin: 0; font-size: 16px; color: #1f2937;">Cached Data</h3>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #6b7280;">View saved information</p>
                        </div>
                    </div>
                    
                    <button onclick="location.reload()" style="width: 100%; background: #3b82f6; color: white; border: none; border-radius: 8px; padding: 15px; font-size: 16px; font-weight: 600; cursor: pointer;">
                        🔄 Check Connection Again
                    </button>
                </div>
            `;
        }
        
        // Auto-check connection every 30 seconds
        setInterval(() => {
            if (navigator.onLine) {
                checkConnection();
            }
        }, 30000);
        
        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus(true);
            setTimeout(() => {
                window.location.href = '/mobile/dashboard';
            }, 1000);
        });
        
        window.addEventListener('offline', () => {
            updateConnectionStatus(false);
        });
        
        // Initial connection check
        if (navigator.onLine) {
            setTimeout(checkConnection, 1000);
        }
    </script>
</body>
</html>
