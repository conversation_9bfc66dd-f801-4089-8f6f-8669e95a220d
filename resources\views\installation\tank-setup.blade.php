@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-oil-can text-cyan-500"></i> Tank Setup
    </h2>
    <form id="tank-setup-form" class="space-y-6 mt-4">
        @csrf
        <div id="tanks-container" class="space-y-6">
            <div class="tank-group grid grid-cols-1 md:grid-cols-4 gap-4 items-end bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Tank Name</label>
                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][name]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Location</label>
                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][location_id]" required>
                        @foreach($locations as $location)
                            <option value="{{ $location->id }}">{{ $location->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Gas Type</label>
                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][gas_type_id]" required>
                        @foreach($gasTypes as $gasType)
                            <option value="{{ $gasType->id }}">{{ $gasType->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Capacity</label>
                    <input type="number" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][capacity]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Current Level</label>
                    <input type="number" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][current_level]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Min Level</label>
                    <input type="number" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][min_level]" required>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-1">Max Level</label>
                    <input type="number" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" name="tanks[0][max_level]" required>
                </div>
            </div>
        </div>
        <div class="flex flex-col md:flex-row gap-3 mt-2">
            <button type="button" class="w-full md:w-auto px-6 py-2 rounded-lg bg-gradient-to-tr from-gray-200 to-gray-300 text-gray-700 font-semibold shadow hover:bg-gray-300 transition flex items-center gap-2" id="add-tank">
                <i class="fas fa-plus"></i> Add Another Tank
            </button>
            <button type="submit" class="w-full md:w-auto px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center gap-2">
                <i class="fas fa-save"></i> Save Tanks
            </button>
        </div>
    </form>

    <div id="tanks-status" class="mt-3"></div>

    <a href="{{ route('install.final.setup') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    let tankIndex = 1;
    document.getElementById('add-tank').addEventListener('click', function () {
        const container = document.getElementById('tanks-container');
        const newTank = document.createElement('div');
        newTank.className = 'tank-group grid grid-cols-1 md:grid-cols-4 gap-4 items-end bg-gray-50 p-4 rounded-lg border border-gray-200';
        newTank.innerHTML = `
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Tank Name</label>
                <input type='text' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][name]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Location</label>
                <select class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][location_id]' required>
                    @foreach($locations as $location)
                        <option value='{{ $location->id }}'>{{ $location->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Gas Type</label>
                <select class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][gas_type_id]' required>
                    @foreach($gasTypes as $gasType)
                        <option value='{{ $gasType->id }}'>{{ $gasType->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Capacity</label>
                <input type='number' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][capacity]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Current Level</label>
                <input type='number' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][current_level]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Min Level</label>
                <input type='number' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][min_level]' required>
            </div>
            <div>
                <label class='block text-sm font-semibold text-gray-700 mb-1'>Max Level</label>
                <input type='number' class='w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800' name='tanks[${tankIndex}][max_level]' required>
            </div>
        `;
        container.appendChild(newTank);
        tankIndex++;
    });

    document.getElementById('tank-setup-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('tanks-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Saving tanks...</div>';

        const formData = new FormData(this);

        fetch('{{ route('install.save.tanks') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('tanks-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                let errors = '';
                if (data.errors) {
                    for (const error in data.errors) {
                        errors += '<p>' + data.errors[error][0] + '</p>';
                    }
                }
                document.getElementById('tanks-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + errors + '</div>';
            }
        });
    });
</script>
@endpush
