@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-building text-cyan-500"></i> Company Information
    </h2>
    <form id="company-info-form" class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
        @csrf
        <div>
            <label for="company_name" class="block text-sm font-semibold text-gray-700 mb-1">Company Name</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_name" name="company_name" required>
        </div>
        <div>
            <label for="company_email" class="block text-sm font-semibold text-gray-700 mb-1">Company Email</label>
            <input type="email" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_email" name="company_email" required>
        </div>
        <div>
            <label for="company_phone" class="block text-sm font-semibold text-gray-700 mb-1">Company Phone</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_phone" name="company_phone" required>
        </div>
        <div>
            <label for="company_address" class="block text-sm font-semibold text-gray-700 mb-1">Address</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_address" name="company_address" required>
        </div>
        <div>
            <label for="company_city" class="block text-sm font-semibold text-gray-700 mb-1">City</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_city" name="company_city" required>
        </div>
        <div>
            <label for="company_state" class="block text-sm font-semibold text-gray-700 mb-1">State</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_state" name="company_state" required>
        </div>
        <div>
            <label for="company_country" class="block text-sm font-semibold text-gray-700 mb-1">Country</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_country" name="company_country" required>
        </div>
        <div>
            <label for="company_postal_code" class="block text-sm font-semibold text-gray-700 mb-1">Postal Code</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="company_postal_code" name="company_postal_code" required>
        </div>
        <div>
            <label for="tax_number" class="block text-sm font-semibold text-gray-700 mb-1">Tax Number (Optional)</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="tax_number" name="tax_number">
        </div>
        <div>
            <label for="license_number" class="block text-sm font-semibold text-gray-700 mb-1">License Number (Optional)</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="license_number" name="license_number">
        </div>
        <div class="md:col-span-2 flex flex-col gap-3 mt-2">
            <button type="submit" class="w-full px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center justify-center gap-2">
                <i class="fas fa-save"></i> Save Company Info
            </button>
        </div>
    </form>

    <div id="company-info-status" class="mt-3"></div>

    <a href="{{ route('install.location.setup') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    document.getElementById('company-info-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('company-info-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Saving company info...</div>';

        const formData = new FormData(this);

        fetch('{{ route('install.save.company') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('company-info-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                let errors = '';
                if (data.errors) {
                    for (const error in data.errors) {
                        errors += '<p>' + data.errors[error][0] + '</p>';
                    }
                }
                document.getElementById('company-info-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + errors + '</div>';
            }
        });
    });
</script>
@endpush
