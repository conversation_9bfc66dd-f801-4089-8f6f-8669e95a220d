<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rentals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained();
            $table->foreignId('cylinder_id')->constrained();
            $table->foreignId('customer_id')->constrained();
            $table->date('start_date');
            $table->date('expected_return_date');
            $table->date('actual_return_date')->nullable();
            $table->decimal('daily_rate', 8, 2);
            $table->decimal('late_fee', 8, 2)->default(0);
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->enum('status', ['active', 'returned', 'overdue', 'cancelled']);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['status', 'expected_return_date']);
            $table->index(['customer_id', 'status']);
            $table->index(['cylinder_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rentals');
    }
};
