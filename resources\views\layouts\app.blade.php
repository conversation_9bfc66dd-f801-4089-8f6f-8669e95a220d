<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="#3b82f6">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="GCMS">
        <meta name="msapplication-TileColor" content="#3b82f6">

        <!-- PWA Manifest -->
        <link rel="manifest" href="/manifest.json">

        <!-- Icons -->
        <link rel="icon" type="image/png" sizes="192x192" href="/images/icons/icon-192x192.png">
        <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Dashboard Styles -->
        <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">

        <!-- Chart.js for Dashboard Analytics -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <!-- PWA Service Worker -->
        <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/sw.js')
                        .then((registration) => {
                            console.log('SW registered: ', registration);
                        })
                        .catch((registrationError) => {
                            console.log('SW registration failed: ', registrationError);
                        });
                });
            }
        </script>
        <style>
            .main-bg {
                background: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
                min-height: 100vh;
            }
            .glass {
                background: rgba(255,255,255,0.85);
                backdrop-filter: blur(8px);
            }
        </style>
    </head>
    <body class="font-sans antialiased main-bg">
        <div class="min-h-screen flex flex-col">
            <livewire:layout.navigation />
            <!-- Volt: layout.navigation -->

            <!-- Page Heading -->
            @if (isset($header))
                <header class="glass shadow-lg rounded-b-2xl mx-auto w-full max-w-7xl mt-4">
                    <div class="py-6 px-4 sm:px-6 lg:px-8 flex items-center gap-3">
                        <span class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-tr from-indigo-500 to-cyan-400 shadow-lg">
                            <i class="fas fa-gas-pump text-white text-2xl"></i>
                        </span>
                        <span class="text-2xl font-extrabold text-gray-800 tracking-tight">{{ config('app.name', 'GCMS') }}</span>
                        <span class="ml-auto text-sm text-gray-500 font-medium">{{ date('Y') }}</span>
                    </div>
                    <div class="px-4 pb-4">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main class="flex-1 flex flex-col items-center justify-start py-8">
                <div class="w-full max-w-7xl px-4 glass rounded-2xl shadow-2xl border border-gray-200">
                    {{ $slot }}
                </div>
            </main>
        </div>
    </body>
</html>
