<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'template_id',
        'contact_id',
        'customer_id',
        'phone_number',
        'message_type',
        'content',
        'template_data',
        'status',
        'sent_at',
        'delivered_at',
        'read_at',
        'failed_at',
        'error_message',
        'webhook_data',
        'priority',
        'scheduled_at',
        'retry_count',
        'cost',
        'gateway_message_id',
        'gateway_response',
        'triggered_by_event',
        'related_model_type',
        'related_model_id',
        'sent_by',
    ];

    protected $casts = [
        'content' => 'array',
        'template_data' => 'array',
        'webhook_data' => 'array',
        'gateway_response' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'failed_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'cost' => 'decimal:4',
    ];

    /**
     * Message types
     */
    const MESSAGE_TYPES = [
        'template' => 'Template Message',
        'text' => 'Text Message',
        'image' => 'Image Message',
        'document' => 'Document Message',
        'video' => 'Video Message',
        'audio' => 'Audio Message',
        'location' => 'Location Message',
        'contact' => 'Contact Message',
    ];

    /**
     * Message statuses
     */
    const STATUSES = [
        'pending' => 'Pending',
        'queued' => 'Queued',
        'sent' => 'Sent',
        'delivered' => 'Delivered',
        'read' => 'Read',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
        'scheduled' => 'Scheduled',
    ];

    /**
     * Priority levels
     */
    const PRIORITIES = [
        'low' => 'Low',
        'normal' => 'Normal',
        'high' => 'High',
        'urgent' => 'Urgent',
        'emergency' => 'Emergency',
    ];

    /**
     * Get the template used for this message
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(WhatsAppTemplate::class, 'template_id');
    }

    /**
     * Get the contact for this message
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(WhatsAppContact::class, 'contact_id');
    }

    /**
     * Get the customer for this message
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * Get the user who sent this message
     */
    public function sentBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sent_by');
    }

    /**
     * Get the related model (polymorphic)
     */
    public function relatedModel()
    {
        if ($this->related_model_type && $this->related_model_id) {
            return $this->morphTo('related', 'related_model_type', 'related_model_id');
        }
        return null;
    }

    /**
     * Generate unique message ID
     */
    public static function generateMessageId(): string
    {
        return 'WA-' . date('YmdHis') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get message type label
     */
    public function getMessageTypeLabel(): string
    {
        return self::MESSAGE_TYPES[$this->message_type] ?? ucfirst(str_replace('_', ' ', $this->message_type));
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'queued' => ['label' => 'Queued', 'color' => 'blue'],
            'sent' => ['label' => 'Sent', 'color' => 'green'],
            'delivered' => ['label' => 'Delivered', 'color' => 'green'],
            'read' => ['label' => 'Read', 'color' => 'green'],
            'failed' => ['label' => 'Failed', 'color' => 'red'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'gray'],
            'scheduled' => ['label' => 'Scheduled', 'color' => 'blue'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get priority label with color
     */
    public function getPriorityLabel(): array
    {
        return match($this->priority) {
            'low' => ['label' => 'Low', 'color' => 'gray'],
            'normal' => ['label' => 'Normal', 'color' => 'blue'],
            'high' => ['label' => 'High', 'color' => 'yellow'],
            'urgent' => ['label' => 'Urgent', 'color' => 'orange'],
            'emergency' => ['label' => 'Emergency', 'color' => 'red'],
            default => ['label' => ucfirst($this->priority), 'color' => 'gray']
        };
    }

    /**
     * Check if message is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at && $this->scheduled_at->isFuture();
    }

    /**
     * Check if message is overdue for sending
     */
    public function isOverdue(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at && $this->scheduled_at->isPast();
    }

    /**
     * Mark message as sent
     */
    public function markAsSent(string $gatewayMessageId = null, array $gatewayResponse = null): bool
    {
        return $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'gateway_message_id' => $gatewayMessageId,
            'gateway_response' => $gatewayResponse,
        ]);
    }

    /**
     * Mark message as delivered
     */
    public function markAsDelivered(): bool
    {
        if ($this->status !== 'sent') {
            return false;
        }

        return $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark message as read
     */
    public function markAsRead(): bool
    {
        if (!in_array($this->status, ['sent', 'delivered'])) {
            return false;
        }

        return $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Mark message as failed
     */
    public function markAsFailed(string $errorMessage = null, array $gatewayResponse = null): bool
    {
        return $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'error_message' => $errorMessage,
            'gateway_response' => $gatewayResponse,
        ]);
    }

    /**
     * Increment retry count
     */
    public function incrementRetryCount(): bool
    {
        return $this->increment('retry_count');
    }

    /**
     * Scope for pending messages
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for scheduled messages
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for overdue scheduled messages
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_at', '<=', now());
    }

    /**
     * Scope for failed messages
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for specific priority
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Auto-generate message ID before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($message) {
            if (!$message->message_id) {
                $message->message_id = static::generateMessageId();
            }
        });
    }
}
