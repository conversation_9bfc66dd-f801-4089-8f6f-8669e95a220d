<?php

namespace App\Http\Controllers;

use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules;

class ProfileController extends Controller
{
    // Removed middleware enforcement for testing environment
    // Removed constructor to prevent middleware invocation
    /**
     * Show the profile page
     */
    public function show()
    {
        $user = auth()->user();
        $user->load(['roles', 'locations', 'managedLocations']);

        return view('profile.show', compact('user'));
    }

    /**
     * Show the profile edit form
     */
    public function edit()
    {
        $user = auth()->user();
        $user->load(['roles', 'locations']);
        $availableLocations = Location::active()->get();

        return view('profile.edit', compact('user', 'availableLocations'));
    }

    /**
     * Update the profile
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
        ]);

        $user->update($validated);

        return redirect()->route('profile.show')
                        ->with('success', 'Profile updated successfully.');
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        auth()->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile.show')
                        ->with('success', 'Password updated successfully.');
    }

    /**
     * Show activity log
     */
    public function activity()
    {
        $user = auth()->user();

        // Get user's activity logs
        $activities = \Spatie\Activitylog\Models\Activity::where('causer_id', $user->id)
                        ->where('causer_type', get_class($user))
                        ->latest()
                        ->paginate(20);

        return view('profile.activity', compact('activities'));
    }

    /**
     * Show user's assigned locations
     */
    public function locations()
    {
        $user = auth()->user();
        $user->load(['locations', 'managedLocations']);

        return view('profile.locations', compact('user'));
    }

    /**
     * Request location access
     */
    public function requestLocationAccess(Request $request)
    {
        $request->validate([
            'location_id' => ['required', 'exists:locations,id'],
            'reason' => ['required', 'string', 'max:500'],
        ]);

        // Create a notification or request record
        // This would typically create a request that admins can approve

        return redirect()->route('profile.locations')
                        ->with('success', 'Location access request submitted successfully.');
    }

    /**
     * Show user preferences
     */
    public function preferences()
    {
        $user = auth()->user();

        return view('profile.preferences', compact('user'));
    }

    /**
     * Update user preferences
     */
    public function updatePreferences(Request $request)
    {
        $validated = $request->validate([
            'timezone' => ['nullable', 'string', 'max:50'],
            'language' => ['nullable', 'string', 'max:10'],
            'notifications_email' => ['boolean'],
            'notifications_sms' => ['boolean'],
            'theme' => ['nullable', 'in:light,dark,auto'],
        ]);

        $user = auth()->user();

        // Store preferences in user settings or separate table
        // For now, we'll use a JSON column or create a user_preferences table

        return redirect()->route('profile.preferences')
                        ->with('success', 'Preferences updated successfully.');
    }
}
