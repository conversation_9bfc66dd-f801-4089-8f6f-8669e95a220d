<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RentalBilling extends Model
{
    use HasFactory;

    protected $fillable = [
        'rental_id',
        'billing_number',
        'billing_period_start',
        'billing_period_end',
        'amount',
        'late_fee',
        'total_amount',
        'status',
        'due_date',
        'paid_date',
        'payment_method',
        'payment_reference',
        'notes',
    ];

    protected $casts = [
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'due_date' => 'date',
        'paid_date' => 'date',
        'amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Billing statuses
     */
    const STATUSES = [
        'pending' => 'Pending',
        'paid' => 'Paid',
        'overdue' => 'Overdue',
        'cancelled' => 'Cancelled',
    ];

    /**
     * Get the rental for this billing
     */
    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class);
    }

    /**
     * Generate unique billing number
     */
    public static function generateBillingNumber(): string
    {
        $prefix = 'RB-' . date('Ymd') . '-';
        $lastBilling = static::where('billing_number', 'like', $prefix . '%')
                            ->orderBy('id', 'desc')
                            ->first();

        if ($lastBilling) {
            $lastNumber = (int) substr($lastBilling->billing_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'paid' => ['label' => 'Paid', 'color' => 'green'],
            'overdue' => ['label' => 'Overdue', 'color' => 'red'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'gray'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Check if billing is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'pending' && $this->due_date && $this->due_date->isPast();
    }

    /**
     * Mark as paid
     */
    public function markAsPaid($paymentMethod = null, $paymentReference = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'paid',
            'paid_date' => now(),
            'payment_method' => $paymentMethod,
            'payment_reference' => $paymentReference,
        ]);

        // Update rental paid amount
        $this->rental->increment('paid_amount', $this->total_amount);
        $this->rental->decrement('outstanding_amount', $this->total_amount);

        return true;
    }

    /**
     * Scope for overdue billings
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', now());
    }

    /**
     * Scope for pending billings
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
