<?php
/**
 * GCMS Quick Fix Script
 * Run this script to fix common issues with the GCMS application
 */

echo "🔧 GCMS Quick Fix Script\n";
echo "========================\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "❌ Error: Please run this script from the GCMS root directory\n";
    exit(1);
}

echo "1. Clearing all caches...\n";
exec('php artisan config:clear', $output1);
exec('php artisan cache:clear', $output2);
exec('php artisan route:clear', $output3);
exec('php artisan view:clear', $output4);
echo "✅ Caches cleared\n\n";

echo "2. Checking database connection...\n";
try {
    $pdo = new PDO(
        'mysql:host=' . getenv('DB_HOST') ?: '127.0.0.1' . ';dbname=' . getenv('DB_DATABASE') ?: 'gcms_database',
        getenv('DB_USERNAME') ?: 'root',
        getenv('DB_PASSWORD') ?: ''
    );
    echo "✅ Database connection successful\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "💡 Please check your .env database settings\n\n";
}

echo "3. Checking file permissions...\n";
$directories = [
    'storage',
    'bootstrap/cache',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $dir is writable\n";
        } else {
            echo "❌ $dir is not writable\n";
            // Try to fix permissions
            chmod($dir, 0755);
            if (is_writable($dir)) {
                echo "✅ Fixed permissions for $dir\n";
            } else {
                echo "❌ Could not fix permissions for $dir\n";
            }
        }
    } else {
        echo "❌ Directory $dir does not exist\n";
        mkdir($dir, 0755, true);
        echo "✅ Created directory $dir\n";
    }
}
echo "\n";

echo "4. Checking Laravel requirements...\n";
$extensions = ['openssl', 'pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext extension loaded\n";
    } else {
        echo "❌ $ext extension missing\n";
    }
}
echo "\n";

echo "5. Checking .env configuration...\n";
if (file_exists('.env')) {
    echo "✅ .env file exists\n";
    
    $envContent = file_get_contents('.env');
    
    // Check for Redis configuration
    if (strpos($envContent, 'CACHE_STORE=redis') !== false || 
        strpos($envContent, 'SESSION_DRIVER=redis') !== false ||
        strpos($envContent, 'QUEUE_CONNECTION=redis') !== false) {
        echo "⚠️  Redis configuration detected\n";
        echo "💡 Consider changing to file-based caching if Redis is not installed\n";
    }
    
    // Check for application key
    if (strpos($envContent, 'APP_KEY=') === false || strpos($envContent, 'APP_KEY=base64:') === false) {
        echo "❌ Application key missing or invalid\n";
        echo "🔧 Generating new application key...\n";
        exec('php artisan key:generate', $keyOutput);
        echo "✅ Application key generated\n";
    } else {
        echo "✅ Application key configured\n";
    }
} else {
    echo "❌ .env file missing\n";
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env from .env.example\n";
        exec('php artisan key:generate', $keyOutput);
        echo "✅ Generated application key\n";
    }
}
echo "\n";

echo "6. Testing basic Laravel functionality...\n";
try {
    exec('php artisan --version', $versionOutput);
    echo "✅ Laravel is working: " . implode(' ', $versionOutput) . "\n";
} catch (Exception $e) {
    echo "❌ Laravel not working: " . $e->getMessage() . "\n";
}
echo "\n";

echo "7. Checking for installation status...\n";
if (file_exists('storage/installed')) {
    echo "✅ GCMS is installed\n";
    echo "🌐 Access your application at: http://localhost:8000\n";
} else {
    echo "⚠️  GCMS not installed yet\n";
    echo "🌐 Run installation wizard at: http://localhost:8000/install\n";
}
echo "\n";

echo "🎉 Quick fix completed!\n";
echo "========================\n";
echo "Next steps:\n";
echo "1. Start the server: php -S localhost:8000 -t public\n";
echo "2. Open browser: http://localhost:8000\n";
echo "3. If not installed, go to: http://localhost:8000/install\n";
echo "\nIf you still have issues, check the Laravel logs in storage/logs/\n";
?>
