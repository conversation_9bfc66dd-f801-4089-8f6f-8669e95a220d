<?php

namespace App\Http\Controllers;

use App\Models\Rental;
use App\Models\RentalBilling;
use App\Models\RentalExtension;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Location;
use App\Models\GasType;
use App\Models\User;
use App\Services\RentalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RentalController extends Controller
{
    protected $rentalService;

    public function __construct(RentalService $rentalService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_rentals', ['only' => ['index', 'show']];
        $this->middleware('permission:create_rentals', ['only' => ['create', 'store']];
        $this->middleware('permission:edit_rentals', ['only' => ['edit', 'update']];
        $this->middleware('permission:manage_rentals', ['only' => ['activate', 'return', 'extend', 'cancel']];
        $this->rentalService = $rentalService;
    }

    /**
     * Display rentals listing
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = Rental::with(['customer', 'location', 'cylinder', 'gasType', 'assignedTo']);

        // Apply location filtering
        $query->whereIn('location_id', $accessibleLocationIds);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('rental_type')) {
            $query->where('rental_type', $request->rental_type);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('rental_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'overdue') {
            $query->overdue();
        } elseif ($request->filter === 'needs_billing') {
            $query->needsBilling();
        } elseif ($request->filter === 'my_rentals') {
            $query->assignedTo($user->id);
        } elseif ($request->filter === 'ending_soon') {
            $query->active()->whereBetween('expected_return_date', [now(), now()->addDays(7)]);
        }

        $rentals = $query->orderBy('created_at', 'desc')->paginate(20);

        $locations = $this->getAccessibleLocations($user);
        $staff = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['staff', 'location_manager']);
        })->get();

        // Get statistics
        $statistics = $this->rentalService->getRentalStatistics($accessibleLocationIds);

        return view('rentals.index', compact('rentals', 'locations', 'staff', 'statistics'));
    }

    /**
     * Show rental creation form
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $customers = Customer::active()->get();
        $locations = $this->getAccessibleLocations($user);
        $gasTypes = GasType::active()->get();

        // If creating from order
        $order = null;
        if ($request->filled('order_id')) {
            $order = Order::with(['customer', 'items.gasType'])->findOrFail($request->order_id);
        }

        return view('rentals.create', compact('customers', 'locations', 'gasTypes', 'order'));
    }

    /**
     * Store new rental
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'order_id' => 'nullable|exists:orders,id',
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'rental_type' => 'required|in:daily,weekly,monthly,long_term,event',
            'start_date' => 'required|date|after_or_equal:today',
            'expected_return_date' => 'required|date|after:start_date',
            'daily_rate' => 'required|numeric|min:0',
            'weekly_rate' => 'nullable|numeric|min:0',
            'monthly_rate' => 'nullable|numeric|min:0',
            'deposit_amount' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:daily,weekly,monthly,upfront',
            'auto_renew' => 'boolean',
            'renewal_period' => 'nullable|in:weekly,monthly',
            'terms_conditions' => 'nullable|string|max:2000',
            'special_instructions' => 'nullable|string|max:1000',
            'delivery_address' => 'nullable|string|max:500',
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        // Check location access
        if (!$user->hasLocationAccess($request->location_id)) {
            abort(403, 'You do not have access to this location.');
        }

        try {
            if ($request->order_id) {
                $order = Order::findOrFail($request->order_id);
                $rental = $this->rentalService->createRentalFromOrder($order, $request->all());
            } else {
                // Create standalone rental (would need additional logic for cylinder allocation)
                throw new \Exception('Standalone rental creation not yet implemented');
            }

            return redirect()->route('rentals.show', $rental)
                           ->with('success', 'Rental created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', $e->getMessage());
        }
    }

    /**
     * Display rental details
     */
    public function show(Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        $rental->load(['customer', 'location', 'cylinder', 'gasType', 'assignedTo', 'billings', 'extensions']);

        return view('rentals.show', compact('rental'));
    }

    /**
     * Activate rental
     */
    public function activate(Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        try {
            $this->rentalService->activateRental($rental);

            return response()->json([
                'success' => true,
                'message' => 'Rental activated successfully.',
                'rental' => $rental->fresh(['customer', 'location', 'cylinder']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Process rental return
     */
    public function return(Request $request, Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        $request->validate([
            'return_date' => 'nullable|date',
            'condition' => 'required|in:good,damaged,lost',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $this->rentalService->processReturn($rental, [
                'return_date' => $request->return_date,
                'condition' => $request->condition,
                'notes' => $request->notes,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Rental return processed successfully.',
                'rental' => $rental->fresh(['customer', 'location', 'cylinder']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Extend rental period
     */
    public function extend(Request $request, Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        $request->validate([
            'new_end_date' => 'required|date|after:' . $rental->expected_return_date,
            'reason' => 'nullable|string|max:1000',
        ]);

        try {
            $extension = $this->rentalService->extendRental(
                $rental,
                $request->new_end_date,
                $request->reason
            );

            return response()->json([
                'success' => true,
                'message' => 'Rental extension requested successfully.',
                'extension' => $extension,
                'rental' => $rental->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Cancel rental
     */
    public function cancel(Request $request, Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        if ($rental->cancel($request->reason)) {
            return response()->json([
                'success' => true,
                'message' => 'Rental cancelled successfully.',
                'rental' => $rental->fresh(),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Rental cannot be cancelled in its current status.',
        ], 400);
    }

    /**
     * Get rental statistics
     */
    public function statistics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $days = $request->get('days', 30);
        $statistics = $this->rentalService->getRentalStatistics($accessibleLocationIds, $days);
        $attention = $this->rentalService->getRentalsRequiringAttention($accessibleLocationIds);

        return response()->json([
            'statistics' => $statistics,
            'requiring_attention' => [
                'overdue' => $attention['overdue']->count(),
                'needs_billing' => $attention['needs_billing']->count(),
                'pending_extensions' => $attention['pending_extensions']->count(),
                'ending_soon' => $attention['ending_soon']->count(),
            ],
        ]);
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
