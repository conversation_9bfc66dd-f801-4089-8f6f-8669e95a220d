<?php

namespace App\Services;

use App\Models\Cylinder;
use App\Models\CylinderLog;
use Illuminate\Support\Facades\Auth;

class CylinderStatusService
{
    /**
     * Available cylinder statuses
     */
    const STATUSES = [
        'empty' => 'Empty',
        'full' => 'Full',
        'in_use' => 'In Use',
        'damaged' => 'Damaged',
        'expired' => 'Expired',
        'maintenance' => 'Maintenance',
        'in_transit' => 'In Transit',
    ];

    /**
     * Status transitions that are allowed
     */
    const ALLOWED_TRANSITIONS = [
        'empty' => ['full', 'damaged', 'maintenance', 'expired'],
        'full' => ['in_use', 'empty', 'damaged', 'maintenance', 'in_transit'],
        'in_use' => ['empty', 'damaged', 'maintenance'],
        'damaged' => ['maintenance', 'expired'],
        'expired' => ['maintenance'],
        'maintenance' => ['empty', 'full', 'expired'],
        'in_transit' => ['full', 'empty', 'damaged'],
    ];

    /**
     * Change cylinder status with validation and logging
     */
    public function changeStatus(Cylinder $cylinder, $newStatus, $notes = null, $metadata = [])
    {
        // Validate new status
        if (!array_key_exists($newStatus, self::STATUSES)) {
            throw new \InvalidArgumentException("Invalid status: {$newStatus}");
        }

        // Check if transition is allowed
        if (!$this->isTransitionAllowed($cylinder->status, $newStatus)) {
            throw new \InvalidArgumentException(
                "Status transition from {$cylinder->status} to {$newStatus} is not allowed"
            );
        }

        $oldStatus = $cylinder->status;
        
        // Update cylinder status
        $cylinder->update(['status' => $newStatus]);

        // Log the status change
        CylinderLog::createLog($cylinder->id, 'status_changed', [
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'location_id' => $cylinder->location_id,
            'notes' => $notes ?: "Status changed from {$oldStatus} to {$newStatus}",
            'metadata' => $metadata,
        ]);

        return $cylinder;
    }

    /**
     * Check if status transition is allowed
     */
    public function isTransitionAllowed($currentStatus, $newStatus)
    {
        if ($currentStatus === $newStatus) {
            return true; // Same status is always allowed
        }

        return in_array($newStatus, self::ALLOWED_TRANSITIONS[$currentStatus] ?? []);
    }

    /**
     * Get allowed next statuses for a cylinder
     */
    public function getAllowedNextStatuses($currentStatus)
    {
        $allowedStatuses = self::ALLOWED_TRANSITIONS[$currentStatus] ?? [];
        
        $result = [];
        foreach ($allowedStatuses as $status) {
            $result[$status] = self::STATUSES[$status];
        }
        
        return $result;
    }

    /**
     * Fill a cylinder (empty -> full)
     */
    public function fillCylinder(Cylinder $cylinder, $filledWeight = null, $tankId = null, $notes = null)
    {
        if ($cylinder->status !== 'empty') {
            throw new \InvalidArgumentException('Only empty cylinders can be filled');
        }

        $metadata = [];
        if ($filledWeight) {
            $metadata['filled_weight'] = $filledWeight;
            $cylinder->current_weight = $filledWeight;
        }
        if ($tankId) {
            $metadata['tank_id'] = $tankId;
        }

        $cylinder->last_filled_at = now();
        $cylinder->save();

        return $this->changeStatus($cylinder, 'full', $notes ?: 'Cylinder filled', $metadata);
    }

    /**
     * Empty a cylinder (full/in_use -> empty)
     */
    public function emptyCylinder(Cylinder $cylinder, $notes = null)
    {
        if (!in_array($cylinder->status, ['full', 'in_use'])) {
            throw new \InvalidArgumentException('Only full or in-use cylinders can be emptied');
        }

        $cylinder->current_weight = $cylinder->tare_weight;
        $cylinder->save();

        return $this->changeStatus($cylinder, 'empty', $notes ?: 'Cylinder emptied');
    }

    /**
     * Mark cylinder as in use
     */
    public function markInUse(Cylinder $cylinder, $customerId = null, $orderId = null, $notes = null)
    {
        if ($cylinder->status !== 'full') {
            throw new \InvalidArgumentException('Only full cylinders can be marked as in use');
        }

        $metadata = [];
        if ($customerId) {
            $metadata['customer_id'] = $customerId;
        }
        if ($orderId) {
            $metadata['order_id'] = $orderId;
        }

        return $this->changeStatus($cylinder, 'in_use', $notes ?: 'Cylinder deployed to customer', $metadata);
    }

    /**
     * Mark cylinder as damaged
     */
    public function markDamaged(Cylinder $cylinder, $damageDescription, $severity = 'medium')
    {
        $metadata = [
            'damage_description' => $damageDescription,
            'severity' => $severity,
            'reported_by' => Auth::id(),
            'reported_at' => now(),
        ];

        return $this->changeStatus($cylinder, 'damaged', "Cylinder damaged: {$damageDescription}", $metadata);
    }

    /**
     * Send cylinder for maintenance
     */
    public function sendForMaintenance(Cylinder $cylinder, $maintenanceType, $notes = null)
    {
        $metadata = [
            'maintenance_type' => $maintenanceType,
            'scheduled_by' => Auth::id(),
            'scheduled_at' => now(),
        ];

        return $this->changeStatus(
            $cylinder, 
            'maintenance', 
            $notes ?: "Sent for {$maintenanceType} maintenance", 
            $metadata
        );
    }

    /**
     * Move cylinder to different location
     */
    public function moveCylinder(Cylinder $cylinder, $newLocationId, $notes = null)
    {
        $oldLocationId = $cylinder->location_id;
        
        $cylinder->update(['location_id' => $newLocationId]);

        CylinderLog::createLog($cylinder->id, 'moved', [
            'old_status' => $cylinder->status,
            'new_status' => $cylinder->status,
            'location_id' => $newLocationId,
            'notes' => $notes ?: "Moved from location {$oldLocationId} to {$newLocationId}",
            'metadata' => [
                'old_location_id' => $oldLocationId,
                'new_location_id' => $newLocationId,
            ],
        ]);

        return $cylinder;
    }

    /**
     * Get cylinder status statistics
     */
    public function getStatusStatistics($locationIds = null)
    {
        $query = Cylinder::query();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $stats = $query->selectRaw('status, count(*) as count')
                      ->groupBy('status')
                      ->pluck('count', 'status')
                      ->toArray();

        // Ensure all statuses are represented
        foreach (self::STATUSES as $status => $label) {
            if (!isset($stats[$status])) {
                $stats[$status] = 0;
            }
        }

        return $stats;
    }

    /**
     * Get cylinders requiring attention
     */
    public function getCylindersRequiringAttention($locationIds = null)
    {
        $query = Cylinder::with(['gasType', 'location']);
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return [
            'expired' => $query->clone()->expired()->get(),
            'inspection_due' => $query->clone()->dueForInspection()->get(),
            'damaged' => $query->clone()->where('status', 'damaged')->get(),
            'maintenance' => $query->clone()->where('status', 'maintenance')->get(),
        ];
    }
}
