<?php

/**
 * MySQL Database Verification Script for GCMS
 * Run this script to verify MySQL connection and database setup
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = new Application(realpath(__DIR__));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 GCMS MySQL Database Verification\n";
echo "=====================================\n\n";

try {
    // Test 1: Database Connection
    echo "1. Testing MySQL Connection...\n";
    $pdo = DB::connection()->getPdo();
    echo "   ✅ MySQL connection successful!\n";
    
    // Test 2: Database Information
    echo "\n2. Database Information:\n";
    $dbName = DB::connection()->getDatabaseName();
    echo "   📊 Database Name: {$dbName}\n";
    
    $version = DB::select("SELECT VERSION() as version")[0]->version;
    echo "   🔢 MySQL Version: {$version}\n";
    
    $charset = DB::select("SELECT @@character_set_database as charset")[0]->charset;
    echo "   🔤 Character Set: {$charset}\n";
    
    $collation = DB::select("SELECT @@collation_database as collation")[0]->collation;
    echo "   📝 Collation: {$collation}\n";
    
    // Test 3: Check Tables
    echo "\n3. Checking Database Tables:\n";
    $tables = DB::select("SHOW TABLES");
    $tableCount = count($tables);
    echo "   📋 Total Tables: {$tableCount}\n";
    
    if ($tableCount > 0) {
        echo "   📝 Tables found:\n";
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            $rowCount = DB::table($tableName)->count();
            echo "      - {$tableName} ({$rowCount} rows)\n";
        }
    } else {
        echo "   ⚠️  No tables found. Run 'php artisan migrate' to create tables.\n";
    }
    
    // Test 4: Check Core Tables
    echo "\n4. Verifying Core GCMS Tables:\n";
    $coreTables = [
        'users', 'customers', 'cylinders', 'orders', 
        'rentals', 'invoices', 'tanks', 'locations'
    ];
    
    foreach ($coreTables as $table) {
        if (Schema::hasTable($table)) {
            $count = DB::table($table)->count();
            echo "   ✅ {$table} table exists ({$count} records)\n";
        } else {
            echo "   ❌ {$table} table missing\n";
        }
    }
    
    // Test 5: MySQL Performance Status
    echo "\n5. MySQL Performance Status:\n";
    $status = DB::select("SHOW STATUS WHERE Variable_name IN (
        'Connections', 'Threads_connected', 'Queries', 'Uptime'
    )");
    
    foreach ($status as $stat) {
        echo "   📊 {$stat->Variable_name}: {$stat->Value}\n";
    }
    
    // Test 6: Storage Engine
    echo "\n6. Storage Engine Information:\n";
    $engines = DB::select("SHOW ENGINES WHERE Support = 'YES'");
    foreach ($engines as $engine) {
        if ($engine->Engine === 'InnoDB') {
            echo "   ✅ InnoDB engine available (recommended)\n";
        }
    }
    
    // Test 7: Database Size
    echo "\n7. Database Size Information:\n";
    $sizeQuery = DB::select("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    
    if (!empty($sizeQuery)) {
        $sizeMB = $sizeQuery[0]->size_mb;
        echo "   💾 Database Size: {$sizeMB} MB\n";
    }
    
    echo "\n🎉 MySQL Database Verification Complete!\n";
    echo "=====================================\n";
    echo "✅ All checks passed successfully!\n";
    echo "🚀 GCMS is ready to use with MySQL!\n\n";
    
} catch (Exception $e) {
    echo "\n❌ Database Verification Failed!\n";
    echo "=====================================\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting Steps:\n";
    echo "1. Ensure MySQL server is running\n";
    echo "2. Check database credentials in .env file\n";
    echo "3. Verify database 'gcms_database' exists\n";
    echo "4. Run 'php artisan migrate' if tables are missing\n";
    echo "5. Check MySQL user permissions\n\n";
    
    exit(1);
}
