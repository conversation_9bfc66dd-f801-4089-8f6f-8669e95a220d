<?php

namespace App\Http\Controllers;

use App\Models\InventoryTransfer;
use App\Models\Location;
use App\Models\GasType;
use App\Models\Cylinder;
use App\Services\InventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InventoryTransferController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_transfers')->only(['index', 'show']);
        $this->middleware('permission:create_transfers')->only(['create', 'store']);
        $this->middleware('permission:approve_transfers')->only(['approve']);
        $this->middleware('permission:manage_transfers')->only(['ship', 'receive', 'cancel']);
        $this->inventoryService = $inventoryService;
    }

    /**
     * Display transfer listing
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = InventoryTransfer::with(['fromLocation', 'toLocation', 'gasType', 'requestedBy', 'approvedBy']);

        // Apply location filtering
        $query->where(function ($q) use ($accessibleLocationIds) {
            $q->whereIn('from_location_id', $accessibleLocationIds)
              ->orWhereIn('to_location_id', $accessibleLocationIds);
        });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('from_location')) {
            $query->where('from_location_id', $request->from_location);
        }

        if ($request->filled('to_location')) {
            $query->where('to_location_id', $request->to_location);
        }

        if ($request->filled('gas_type')) {
            $query->where('gas_type_id', $request->gas_type);
        }

        $transfers = $query->orderBy('created_at', 'desc')->paginate(20);

        $locations = $this->getAccessibleLocations($user);
        $gasTypes = GasType::active()->get();

        return view('inventory.transfers.index', compact('transfers', 'locations', 'gasTypes'));
    }

    /**
     * Show transfer creation form
     */
    public function create()
    {
        $user = Auth::user();
        $locations = $this->getAccessibleLocations($user);
        $gasTypes = GasType::active()->get();

        return view('inventory.transfers.create', compact('locations', 'gasTypes'));
    }

    /**
     * Store new transfer request
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'from_location_id' => 'required|exists:locations,id',
            'to_location_id' => 'required|exists:locations,id|different:from_location_id',
            'gas_type_id' => 'required|exists:gas_types,id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'nullable|string|max:1000',
            'cylinder_ids' => 'nullable|array',
            'cylinder_ids.*' => 'exists:cylinders,id',
        ]);

        // Check location access
        if (!$user->hasLocationAccess($request->from_location_id) ||
            !$user->hasLocationAccess($request->to_location_id)) {
            abort(403, 'You do not have access to one or both locations.');
        }

        try {
            $transfer = $this->inventoryService->requestTransfer(
                $request->from_location_id,
                $request->to_location_id,
                $request->gas_type_id,
                $request->quantity,
                $request->reason,
                $request->cylinder_ids
            );

            return redirect()->route('inventory.transfers.show', $transfer)
                           ->with('success', 'Transfer request created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', $e->getMessage());
        }
    }

    /**
     * Display transfer details
     */
    public function show(InventoryTransfer $transfer)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($transfer->from_location_id) &&
            !$user->hasLocationAccess($transfer->to_location_id)) {
            abort(403, 'You do not have access to this transfer.');
        }

        $transfer->load(['fromLocation', 'toLocation', 'gasType', 'requestedBy', 'approvedBy', 'shippedBy', 'receivedBy']);

        // Get cylinders if specific cylinders are being transferred
        $cylinders = null;
        if ($transfer->cylinder_ids) {
            $cylinders = Cylinder::with(['gasType'])->whereIn('id', $transfer->cylinder_ids)->get();
        }

        return view('inventory.transfers.show', compact('transfer', 'cylinders'));
    }

    /**
     * Approve transfer
     */
    public function approve(Request $request, InventoryTransfer $transfer)
    {
        $user = Auth::user();

        // Check permissions and location access
        if (!$user->can('approve_transfers') ||
            (!$user->hasLocationAccess($transfer->from_location_id) && !$user->hasLocationAccess($transfer->to_location_id))) {
            abort(403, 'You do not have permission to approve this transfer.');
        }

        if ($transfer->approve()) {
            return response()->json([
                'success' => true,
                'message' => 'Transfer approved successfully.',
                'transfer' => $transfer->fresh(['fromLocation', 'toLocation', 'gasType', 'approvedBy']),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Transfer cannot be approved in its current status.',
        ], 400);
    }

    /**
     * Ship transfer
     */
    public function ship(Request $request, InventoryTransfer $transfer)
    {
        $user = Auth::user();

        // Check location access (must have access to source location)
        if (!$user->hasLocationAccess($transfer->from_location_id)) {
            abort(403, 'You do not have access to ship from this location.');
        }

        if ($transfer->ship()) {
            return response()->json([
                'success' => true,
                'message' => 'Transfer shipped successfully.',
                'transfer' => $transfer->fresh(['fromLocation', 'toLocation', 'gasType', 'shippedBy']),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Transfer cannot be shipped in its current status.',
        ], 400);
    }

    /**
     * Receive transfer
     */
    public function receive(Request $request, InventoryTransfer $transfer)
    {
        $user = Auth::user();

        // Check location access (must have access to destination location)
        if (!$user->hasLocationAccess($transfer->to_location_id)) {
            abort(403, 'You do not have access to receive at this location.');
        }

        if ($transfer->complete()) {
            return response()->json([
                'success' => true,
                'message' => 'Transfer received successfully.',
                'transfer' => $transfer->fresh(['fromLocation', 'toLocation', 'gasType', 'receivedBy']),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Transfer cannot be received in its current status.',
        ], 400);
    }

    /**
     * Cancel transfer
     */
    public function cancel(Request $request, InventoryTransfer $transfer)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($transfer->from_location_id) &&
            !$user->hasLocationAccess($transfer->to_location_id)) {
            abort(403, 'You do not have access to cancel this transfer.');
        }

        if ($transfer->cancel()) {
            return response()->json([
                'success' => true,
                'message' => 'Transfer cancelled successfully.',
                'transfer' => $transfer->fresh(['fromLocation', 'toLocation', 'gasType']),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Transfer cannot be cancelled in its current status.',
        ], 400);
    }

    /**
     * Get available cylinders for transfer
     */
    public function getAvailableCylinders(Request $request)
    {
        $request->validate([
            'location_id' => 'required|exists:locations,id',
            'gas_type_id' => 'required|exists:gas_types,id',
        ]);

        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($request->location_id)) {
            abort(403, 'You do not have access to this location.');
        }

        $cylinders = Cylinder::with(['gasType'])
                            ->where('location_id', $request->location_id)
                            ->where('gas_type_id', $request->gas_type_id)
                            ->whereIn('status', ['full', 'empty'])
                            ->get();

        return response()->json([
            'cylinders' => $cylinders,
        ]);
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
