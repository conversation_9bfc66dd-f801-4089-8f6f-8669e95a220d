<?php

namespace App\Http\Controllers;

use App\Models\RentalBilling;
use App\Models\Rental;
use App\Services\RentalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RentalBillingController extends Controller
{
    protected $rentalService;

    public function __construct(RentalService $rentalService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_rental_billing')->only(['index', 'show']);
        $this->middleware('permission:manage_rental_billing')->only(['pay', 'cancel']);
        $this->rentalService = $rentalService;
    }

    /**
     * Display billing listing
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = RentalBilling::with(['rental.customer', 'rental.location']);

        // Apply location filtering
        $query->whereHas('rental', function ($q) use ($accessibleLocationIds) {
            $q->whereIn('location_id', $accessibleLocationIds);
        });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('rental_id')) {
            $query->where('rental_id', $request->rental_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('billing_number', 'like', "%{$search}%")
                  ->orWhereHas('rental', function ($rentalQuery) use ($search) {
                      $rentalQuery->where('rental_number', 'like', "%{$search}%")
                                 ->orWhereHas('customer', function ($customerQuery) use ($search) {
                                     $customerQuery->where('name', 'like', "%{$search}%");
                                 });
                  });
            });
        }

        // Special filters
        if ($request->filter === 'overdue') {
            $query->overdue();
        } elseif ($request->filter === 'pending') {
            $query->pending();
        }

        $billings = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('rentals.billing.index', compact('billings'));
    }

    /**
     * Display billing details
     */
    public function show(RentalBilling $billing)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($billing->rental->location_id)) {
            abort(403, 'You do not have access to this billing record.');
        }

        $billing->load(['rental.customer', 'rental.location', 'rental.cylinder', 'rental.gasType']);

        return view('rentals.billing.show', compact('billing'));
    }

    /**
     * Process payment for billing
     */
    public function pay(Request $request, RentalBilling $billing)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($billing->rental->location_id)) {
            abort(403, 'You do not have access to this billing record.');
        }

        $request->validate([
            'payment_method' => 'required|in:cash,card,bank_transfer,check',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        try {
            $this->rentalService->processPayment($billing, [
                'payment_method' => $request->payment_method,
                'payment_reference' => $request->payment_reference,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully.',
                'billing' => $billing->fresh(['rental']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Cancel billing
     */
    public function cancel(RentalBilling $billing)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($billing->rental->location_id)) {
            abort(403, 'You do not have access to this billing record.');
        }

        if ($billing->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Only pending billings can be cancelled.',
            ], 400);
        }

        $billing->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'Billing cancelled successfully.',
            'billing' => $billing->fresh(),
        ]);
    }

    /**
     * Generate billing for rental
     */
    public function generate(Rental $rental)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($rental->location_id)) {
            abort(403, 'You do not have access to this rental.');
        }

        try {
            $billing = $this->rentalService->processBilling($rental);

            if (!$billing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Rental does not need billing at this time.',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Billing generated successfully.',
                'billing' => $billing,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return \App\Models\Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }
}
