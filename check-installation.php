<?php
/**
 * GCMS Installation Status Checker
 * This script checks if GCMS is properly installed and configured
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

echo "🔍 GCMS Installation Status Checker\n";
echo "===================================\n\n";

try {
    echo "1. Database Connection Test:\n";
    echo "============================\n";
    
    // Test database connection
    try {
        $pdo = DB::connection()->getPdo();
        echo "✅ Database connection: SUCCESS\n";
        echo "   Database: " . DB::connection()->getDatabaseName() . "\n";
        echo "   Driver: " . DB::connection()->getDriverName() . "\n\n";
    } catch (Exception $e) {
        echo "❌ Database connection: FAILED\n";
        echo "   Error: " . $e->getMessage() . "\n\n";
        exit(1);
    }

    echo "2. Migration Status:\n";
    echo "===================\n";
    
    // Check if migrations table exists
    $tables = DB::select("SHOW TABLES");
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    $requiredTables = [
        'users', 'roles', 'permissions', 'role_has_permissions', 
        'model_has_roles', 'customers', 'locations', 'gas_types', 
        'cylinders', 'orders', 'rentals', 'invoices'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        if (in_array($table, $tableNames)) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' missing\n";
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        echo "✅ All required tables exist\n\n";
    } else {
        echo "❌ Missing tables: " . implode(', ', $missingTables) . "\n\n";
    }

    echo "3. Permission System Status:\n";
    echo "============================\n";
    
    $permissionCount = Permission::count();
    $roleCount = Role::count();
    $userCount = User::count();
    
    echo "   Permissions: $permissionCount\n";
    echo "   Roles: $roleCount\n";
    echo "   Users: $userCount\n";
    
    if ($permissionCount >= 30 && $roleCount >= 5) {
        echo "✅ Permission system: PROPERLY CONFIGURED\n\n";
    } else {
        echo "⚠️  Permission system: NEEDS SETUP\n\n";
    }

    echo "4. Admin User Status:\n";
    echo "====================\n";
    
    $adminUser = User::where('email', '<EMAIL>')->first();
    if ($adminUser) {
        echo "✅ Admin user exists: {$adminUser->email}\n";
        echo "   Name: {$adminUser->name}\n";
        echo "   Active: " . ($adminUser->is_active ? 'Yes' : 'No') . "\n";
        
        $roles = $adminUser->getRoleNames();
        echo "   Roles: " . $roles->implode(', ') . "\n";
        
        if ($adminUser->hasRole('super_admin')) {
            echo "✅ Admin has super_admin role\n\n";
        } else {
            echo "⚠️  Admin missing super_admin role\n\n";
        }
    } else {
        echo "❌ Admin user not found\n";
        echo "💡 Default credentials should be: <EMAIL> / password123\n\n";
    }

    echo "5. Installation Status:\n";
    echo "======================\n";
    
    // Check if installation marker exists
    $installationMarker = storage_path('installed');
    if (file_exists($installationMarker)) {
        echo "✅ Installation marker exists\n";
        echo "   Installed at: " . date('Y-m-d H:i:s', filemtime($installationMarker)) . "\n";
    } else {
        echo "⚠️  Installation marker missing\n";
        echo "💡 Creating installation marker...\n";
        file_put_contents($installationMarker, json_encode([
            'installed_at' => now()->toISOString(),
            'version' => '1.0.0',
            'database_seeded' => true
        ]));
        echo "✅ Installation marker created\n";
    }

    echo "\n6. System Health Check:\n";
    echo "======================\n";
    
    // Check file permissions
    $directories = ['storage', 'bootstrap/cache'];
    foreach ($directories as $dir) {
        if (is_writable($dir)) {
            echo "✅ $dir is writable\n";
        } else {
            echo "❌ $dir is not writable\n";
        }
    }
    
    // Check .env file
    if (file_exists('.env')) {
        echo "✅ .env file exists\n";
    } else {
        echo "❌ .env file missing\n";
    }
    
    // Check application key
    $appKey = env('APP_KEY');
    if ($appKey && strlen($appKey) > 10) {
        echo "✅ Application key configured\n";
    } else {
        echo "❌ Application key missing\n";
    }

    echo "\n🎯 OVERALL STATUS:\n";
    echo "==================\n";
    
    if (empty($missingTables) && $permissionCount >= 30 && $roleCount >= 5 && $adminUser) {
        echo "🟢 GCMS IS FULLY INSTALLED AND READY!\n\n";
        
        echo "🚀 Access Information:\n";
        echo "=====================\n";
        echo "🌐 Application URL: http://localhost:8000\n";
        echo "🔐 Admin Login: <EMAIL>\n";
        echo "🔑 Admin Password: password123\n";
        echo "📊 Dashboard: http://localhost:8000/dashboard\n";
        echo "📱 Mobile: http://localhost:8000/mobile\n";
        echo "🔧 Status: http://localhost:8000/status.php\n\n";
        
        echo "✨ Features Available:\n";
        echo "- User Management with Role-Based Access\n";
        echo "- Customer Management\n";
        echo "- Cylinder Inventory with QR Codes\n";
        echo "- Order Management\n";
        echo "- Rental System\n";
        echo "- Financial Management & Invoicing\n";
        echo "- Tank Monitoring\n";
        echo "- WhatsApp Integration\n";
        echo "- Mobile PWA Interface\n";
        echo "- Analytics & Reporting\n";
        echo "- Compliance Management\n\n";
        
        echo "🎉 Your GCMS system is ready for production use!\n";
        
    } else {
        echo "🟡 GCMS NEEDS ADDITIONAL SETUP\n\n";
        
        if (!empty($missingTables)) {
            echo "❌ Run migrations: php artisan migrate\n";
        }
        
        if ($permissionCount < 30 || $roleCount < 5) {
            echo "❌ Run seeder: php seed-database.php\n";
        }
        
        if (!$adminUser) {
            echo "❌ Create admin user: php seed-database.php\n";
        }
        
        echo "\n💡 Or use the installation wizard: http://localhost:8000/install\n";
    }

} catch (Exception $e) {
    echo "❌ Error during status check: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
