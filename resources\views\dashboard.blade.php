<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ __('Dashboard') }}
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Welcome back, {{ auth()->user()->name }}! Here's what's happening with your gas cylinder operations.
                </p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ now()->format('l, F j, Y') }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400" id="current-time">{{ now()->format('g:i A') }}</p>
                </div>
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-gas-pump text-white"></i>
                </div>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Quick Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <x-dashboard.metric-card
                    title="Total Cylinders"
                    :value="$dashboardData['total_cylinders'] ?? 0"
                    icon="fas fa-gas-pump"
                    trend="+12% from last month"
                    trend-direction="up"
                    color="blue"
                    href="{{ route('cylinders.index') }}"
                />

                <x-dashboard.metric-card
                    title="Active Rentals"
                    :value="$dashboardData['active_rentals'] ?? 0"
                    icon="fas fa-handshake"
                    trend="+8% from last week"
                    trend-direction="up"
                    color="green"
                    href="{{ route('rentals.index') }}"
                />

                <x-dashboard.metric-card
                    title="Pending Orders"
                    :value="$dashboardData['pending_orders'] ?? 0"
                    icon="fas fa-shopping-cart"
                    trend="Needs attention"
                    color="yellow"
                    href="{{ route('orders.index') }}"
                />

                <x-dashboard.metric-card
                    title="Monthly Revenue"
                    :value="'$' . number_format($dashboardData['monthly_revenue'] ?? 0, 0)"
                    icon="fas fa-dollar-sign"
                    trend="+15% from last month"
                    trend-direction="up"
                    color="purple"
                    href="{{ route('reports.index') }}"
                />
            </div>

            <!-- Main Dashboard Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column - Charts and Analytics -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Revenue Chart -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                                Revenue Analytics
                            </h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">7D</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">30D</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">90D</button>
                            </div>
                        </div>
                        <div class="h-80">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>

                    <!-- Cylinder Status Overview -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
                            <i class="fas fa-gas-pump text-green-500 mr-2"></i>
                            Cylinder Status Overview
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $dashboardData['cylinders_available'] ?? 0 }}</div>
                                <div class="text-sm text-green-700 dark:text-green-300">Available</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $dashboardData['cylinders_rented'] ?? 0 }}</div>
                                <div class="text-sm text-blue-700 dark:text-blue-300">Rented</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                                <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $dashboardData['cylinders_maintenance'] ?? 0 }}</div>
                                <div class="text-sm text-yellow-700 dark:text-yellow-300">Maintenance</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                                <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $dashboardData['cylinders_overdue'] ?? 0 }}</div>
                                <div class="text-sm text-red-700 dark:text-red-300">Overdue</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Recent Activity and Alerts -->
                <div class="space-y-8">
                    <!-- Tank Levels Alert -->
                    <x-dashboard.tank-level :tanks="$dashboardData['tank_levels'] ?? []" />

                    <!-- Recent Orders -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                <i class="fas fa-shopping-cart text-blue-500 mr-2"></i>
                                Recent Orders
                            </h3>
                            <a href="{{ route('orders.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                        </div>
                        <div class="space-y-3">
                            @foreach($dashboardData['recent_orders'] ?? [] as $order)
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">#{{ $order['order_number'] }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ $order['customer_name'] }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900 dark:text-gray-100">${{ number_format($order['total'], 2) }}</div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $order['status'] === 'completed' ? 'bg-green-100 text-green-800' :
                                           ($order['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800') }}">
                                        {{ ucfirst($order['status']) }}
                                    </span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                            Quick Actions
                        </h3>
                        <div class="grid grid-cols-2 gap-3">
                            <a href="{{ route('orders.create') }}"
                               class="flex items-center justify-center p-3 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900 dark:hover:bg-blue-800 rounded-lg transition-colors">
                                <i class="fas fa-plus text-blue-600 dark:text-blue-400 mr-2"></i>
                                <span class="text-sm font-medium text-blue-700 dark:text-blue-300">New Order</span>
                            </a>
                            <a href="{{ route('customers.create') }}"
                               class="flex items-center justify-center p-3 bg-green-50 hover:bg-green-100 dark:bg-green-900 dark:hover:bg-green-800 rounded-lg transition-colors">
                                <i class="fas fa-user-plus text-green-600 dark:text-green-400 mr-2"></i>
                                <span class="text-sm font-medium text-green-700 dark:text-green-300">Add Customer</span>
                            </a>
                            <a href="{{ route('cylinders.scan') }}"
                               class="flex items-center justify-center p-3 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900 dark:hover:bg-purple-800 rounded-lg transition-colors">
                                <i class="fas fa-qrcode text-purple-600 dark:text-purple-400 mr-2"></i>
                                <span class="text-sm font-medium text-purple-700 dark:text-purple-300">Scan QR</span>
                            </a>
                            <a href="{{ route('reports.index') }}"
                               class="flex items-center justify-center p-3 bg-orange-50 hover:bg-orange-100 dark:bg-orange-900 dark:hover:bg-orange-800 rounded-lg transition-colors">
                                <i class="fas fa-chart-bar text-orange-600 dark:text-orange-400 mr-2"></i>
                                <span class="text-sm font-medium text-orange-700 dark:text-orange-300">Reports</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }
        setInterval(updateTime, 1000);

        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                datasets: [{
                    label: 'Revenue',
                    data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Auto-refresh dashboard data every 30 seconds
        setInterval(function() {
            fetch('/dashboard/data')
                .then(response => response.json())
                .then(data => {
                    // Update stats
                    document.getElementById('total-cylinders').textContent = data.total_cylinders;
                    document.getElementById('active-rentals').textContent = data.active_rentals;
                    document.getElementById('pending-orders').textContent = data.pending_orders;
                    document.getElementById('monthly-revenue').textContent = '$' + data.monthly_revenue.toLocaleString();
                })
                .catch(error => console.log('Dashboard refresh error:', error));
        }, 30000);
    </script>
    @endpush
</x-app-layout>
