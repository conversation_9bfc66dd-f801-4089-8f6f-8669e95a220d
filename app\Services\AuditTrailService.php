<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AuditTrailService
{
    /**
     * Log user activity
     */
    public function logActivity(string $action, string $model = null, int $modelId = null, array $changes = [], string $description = null): void
    {
        $user = Auth::user();
        
        DB::table('audit_trails')->insert([
            'user_id' => $user ? $user->id : null,
            'user_name' => $user ? $user->name : 'System',
            'action' => $action,
            'model_type' => $model,
            'model_id' => $modelId,
            'changes' => json_encode($changes),
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now(),
        ]);
    }

    /**
     * Log login activity
     */
    public function logLogin(User $user, bool $successful = true, string $reason = null): void
    {
        DB::table('login_activities')->insert([
            'user_id' => $user->id,
            'user_name' => $user->name,
            'email' => $user->email,
            'successful' => $successful,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'reason' => $reason,
            'created_at' => now(),
        ]);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, string $severity = 'medium', array $details = []): void
    {
        $user = Auth::user();
        
        DB::table('security_events')->insert([
            'user_id' => $user ? $user->id : null,
            'event_type' => $event,
            'severity' => $severity,
            'details' => json_encode($details),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now(),
        ]);
    }

    /**
     * Get audit trail for specific model
     */
    public function getModelAuditTrail(string $modelType, int $modelId, int $limit = 50): array
    {
        return DB::table('audit_trails')
            ->where('model_type', $modelType)
            ->where('model_id', $modelId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($record) {
                $record->changes = json_decode($record->changes, true);
                return $record;
            })
            ->toArray();
    }

    /**
     * Get user activity history
     */
    public function getUserActivityHistory(int $userId, int $limit = 100): array
    {
        return DB::table('audit_trails')
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($record) {
                $record->changes = json_decode($record->changes, true);
                return $record;
            })
            ->toArray();
    }

    /**
     * Get login history
     */
    public function getLoginHistory(int $userId = null, int $limit = 100): array
    {
        $query = DB::table('login_activities');
        
        if ($userId) {
            $query->where('user_id', $userId);
        }
        
        return $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get security events
     */
    public function getSecurityEvents(string $severity = null, int $limit = 100): array
    {
        $query = DB::table('security_events');
        
        if ($severity) {
            $query->where('severity', $severity);
        }
        
        return $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($record) {
                $record->details = json_decode($record->details, true);
                return $record;
            })
            ->toArray();
    }

    /**
     * Generate audit report
     */
    public function generateAuditReport($startDate = null, $endDate = null, $userId = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $auditQuery = DB::table('audit_trails')
            ->whereBetween('created_at', [$startDate, $endDate]);
            
        if ($userId) {
            $auditQuery->where('user_id', $userId);
        }

        $loginQuery = DB::table('login_activities')
            ->whereBetween('created_at', [$startDate, $endDate]);
            
        if ($userId) {
            $loginQuery->where('user_id', $userId);
        }

        $securityQuery = DB::table('security_events')
            ->whereBetween('created_at', [$startDate, $endDate]);

        return [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'generated_at' => now()->format('Y-m-d H:i:s'),
            ],
            'summary' => [
                'total_activities' => $auditQuery->count(),
                'total_logins' => $loginQuery->count(),
                'successful_logins' => $loginQuery->where('successful', true)->count(),
                'failed_logins' => $loginQuery->where('successful', false)->count(),
                'security_events' => $securityQuery->count(),
                'unique_users' => $auditQuery->distinct('user_id')->count(),
            ],
            'activity_breakdown' => $this->getActivityBreakdown($startDate, $endDate, $userId),
            'user_activity' => $this->getUserActivitySummary($startDate, $endDate),
            'security_summary' => $this->getSecuritySummary($startDate, $endDate),
            'top_activities' => $this->getTopActivities($startDate, $endDate, $userId),
        ];
    }

    /**
     * Get activity breakdown by action type
     */
    protected function getActivityBreakdown($startDate, $endDate, $userId = null): array
    {
        $query = DB::table('audit_trails')
            ->select('action', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('action')
            ->orderBy('count', 'desc');
            
        if ($userId) {
            $query->where('user_id', $userId);
        }

        return $query->get()->toArray();
    }

    /**
     * Get user activity summary
     */
    protected function getUserActivitySummary($startDate, $endDate): array
    {
        return DB::table('audit_trails')
            ->select('user_name', DB::raw('COUNT(*) as activity_count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->groupBy('user_name')
            ->orderBy('activity_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get security summary
     */
    protected function getSecuritySummary($startDate, $endDate): array
    {
        $securityEvents = DB::table('security_events')
            ->select('event_type', 'severity', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('event_type', 'severity')
            ->get();

        return [
            'total_events' => $securityEvents->sum('count'),
            'critical_events' => $securityEvents->where('severity', 'critical')->sum('count'),
            'high_events' => $securityEvents->where('severity', 'high')->sum('count'),
            'medium_events' => $securityEvents->where('severity', 'medium')->sum('count'),
            'low_events' => $securityEvents->where('severity', 'low')->sum('count'),
            'event_breakdown' => $securityEvents->toArray(),
        ];
    }

    /**
     * Get top activities
     */
    protected function getTopActivities($startDate, $endDate, $userId = null): array
    {
        $query = DB::table('audit_trails')
            ->select('action', 'model_type', 'user_name', 'created_at', 'description')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->limit(20);
            
        if ($userId) {
            $query->where('user_id', $userId);
        }

        return $query->get()->toArray();
    }

    /**
     * Clean old audit records
     */
    public function cleanOldRecords(int $daysToKeep = 365): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deletedAudit = DB::table('audit_trails')
            ->where('created_at', '<', $cutoffDate)
            ->delete();
            
        $deletedLogin = DB::table('login_activities')
            ->where('created_at', '<', $cutoffDate)
            ->delete();
            
        $deletedSecurity = DB::table('security_events')
            ->where('created_at', '<', $cutoffDate)
            ->delete();

        return $deletedAudit + $deletedLogin + $deletedSecurity;
    }

    /**
     * Export audit data
     */
    public function exportAuditData($startDate, $endDate, $format = 'csv'): string
    {
        $auditData = $this->generateAuditReport($startDate, $endDate);
        $filename = 'audit_report_' . now()->format('Y-m-d_H-i-s');
        
        switch ($format) {
            case 'json':
                $content = json_encode($auditData, JSON_PRETTY_PRINT);
                $filename .= '.json';
                break;
            case 'csv':
            default:
                $content = $this->convertAuditToCSV($auditData);
                $filename .= '.csv';
                break;
        }
        
        $filepath = storage_path('app/exports/' . $filename);
        
        // Create directory if it doesn't exist
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        file_put_contents($filepath, $content);
        
        return $filename;
    }

    /**
     * Convert audit data to CSV
     */
    protected function convertAuditToCSV($auditData): string
    {
        $csv = "Audit Report\n";
        $csv .= "Generated: " . $auditData['period']['generated_at'] . "\n";
        $csv .= "Period: " . $auditData['period']['start_date'] . " to " . $auditData['period']['end_date'] . "\n\n";
        
        $csv .= "SUMMARY\n";
        foreach ($auditData['summary'] as $key => $value) {
            $csv .= ucfirst(str_replace('_', ' ', $key)) . "," . $value . "\n";
        }
        
        $csv .= "\nACTIVITY BREAKDOWN\n";
        $csv .= "Action,Count\n";
        foreach ($auditData['activity_breakdown'] as $activity) {
            $csv .= $activity->action . "," . $activity->count . "\n";
        }
        
        $csv .= "\nUSER ACTIVITY\n";
        $csv .= "User,Activity Count\n";
        foreach ($auditData['user_activity'] as $user) {
            $csv .= $user->user_name . "," . $user->activity_count . "\n";
        }
        
        return $csv;
    }
}
