<?php $__env->startSection('content'); ?>
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-folder-open text-cyan-500"></i> File Permissions
    </h2>
    <p class="text-gray-600 mb-6">The following directories and files need to be writable for the application to function properly.</p>

    <ul class="space-y-3">
        <?php $__currentLoopData = $permissions['permissions']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="flex items-center justify-between px-4 py-3 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <span class="font-mono text-sm text-gray-800"><?php echo e($permission['name']); ?></span>
                <?php if($permission['check']): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-700">
                        <i class="fas fa-check-circle mr-1"></i> Writable
                    </span>
                <?php else: ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-700 animate-pulse">
                        <i class="fas fa-times-circle mr-1"></i> Not Writable
                    </span>
                <?php endif; ?>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

    <?php if($permissions['canProceed']): ?>
        <a href="<?php echo e(route('install.database')); ?>" class="mt-8 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform">Next <i class="fas fa-arrow-right ml-2"></i></a>
    <?php else: ?>
        <div class="mt-8 bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2">
            <i class="fas fa-exclamation-triangle"></i>
            Please make sure all directories and files are writable.
        </div>
        <a href="<?php echo e(route('install.permissions')); ?>" class="mt-4 inline-block px-6 py-2 rounded-lg bg-gray-200 text-gray-700 font-semibold shadow hover:bg-gray-300 transition">Refresh <i class="fas fa-sync-alt ml-2"></i></a>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installation.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\GCMS\resources\views/installation/permissions.blade.php ENDPATH**/ ?>