<?php

namespace App\Http\Controllers;

use App\Models\Cylinder;
use App\Services\CylinderStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CylinderStatusController extends Controller
{
    protected $statusService;

    public function __construct(CylinderStatusService $statusService)
    {
        $this->middleware('auth');
        $this->middleware('permission:edit_cylinders');
        $this->statusService = $statusService;
    }

    /**
     * Change cylinder status
     */
    public function changeStatus(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'status' => 'required|string',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $this->statusService->changeStatus(
                $cylinder,
                $request->status,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Cylinder status updated successfully.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
                'status_label' => $cylinder->getStatusLabel(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Fill cylinder
     */
    public function fill(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'filled_weight' => 'nullable|numeric|min:0',
            'tank_id' => 'nullable|exists:tanks,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $this->statusService->fillCylinder(
                $cylinder,
                $request->filled_weight,
                $request->tank_id,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Cylinder filled successfully.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Empty cylinder
     */
    public function empty(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $this->statusService->emptyCylinder($cylinder, $request->notes);

            return response()->json([
                'success' => true,
                'message' => 'Cylinder emptied successfully.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mark cylinder as damaged
     */
    public function markDamaged(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'damage_description' => 'required|string|max:1000',
            'severity' => 'required|in:low,medium,high,critical',
        ]);

        try {
            $this->statusService->markDamaged(
                $cylinder,
                $request->damage_description,
                $request->severity
            );

            return response()->json([
                'success' => true,
                'message' => 'Cylinder marked as damaged.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Send cylinder for maintenance
     */
    public function sendMaintenance(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'maintenance_type' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $this->statusService->sendForMaintenance(
                $cylinder,
                $request->maintenance_type,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Cylinder sent for maintenance.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Move cylinder to different location
     */
    public function move(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $request->validate([
            'location_id' => 'required|exists:locations,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check access to target location
        if (!$user->hasLocationAccess($request->location_id)) {
            abort(403, 'You do not have access to the target location.');
        }

        try {
            $this->statusService->moveCylinder(
                $cylinder,
                $request->location_id,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Cylinder moved successfully.',
                'cylinder' => $cylinder->fresh(['gasType', 'location']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get status statistics
     */
    public function getStatistics(Request $request)
    {
        $user = Auth::user();
        $locationIds = null;

        // Apply location restrictions for non-admin users
        if (!$user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            $accessibleLocationIds = $user->locations->pluck('id')->toArray();
            if ($user->hasRole('location_manager')) {
                $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
                $accessibleLocationIds = array_merge($accessibleLocationIds, $managedLocationIds);
            }
            $locationIds = $accessibleLocationIds;
        }

        $stats = $this->statusService->getStatusStatistics($locationIds);
        $attention = $this->statusService->getCylindersRequiringAttention($locationIds);

        return response()->json([
            'status_statistics' => $stats,
            'requiring_attention' => [
                'expired' => $attention['expired']->count(),
                'inspection_due' => $attention['inspection_due']->count(),
                'damaged' => $attention['damaged']->count(),
                'maintenance' => $attention['maintenance']->count(),
            ],
        ]);
    }

    /**
     * Get allowed next statuses for a cylinder
     */
    public function getAllowedStatuses(Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $allowedStatuses = $this->statusService->getAllowedNextStatuses($cylinder->status);

        return response()->json([
            'current_status' => $cylinder->status,
            'allowed_statuses' => $allowedStatuses,
        ]);
    }
}
