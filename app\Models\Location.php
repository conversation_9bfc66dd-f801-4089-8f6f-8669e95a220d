<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Location extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'address',
        'phone',
        'manager_id',
        'latitude',
        'longitude',
        'is_active',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_active' => 'boolean',
    ];

    /**
     * Get the manager of this location
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get all users assigned to this location
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'location_users')
                    ->withTimestamps()
                    ->withPivot('assigned_at');
    }

    /**
     * Get all cylinders at this location
     */
    public function cylinders(): HasMany
    {
        return $this->hasMany(Cylinder::class);
    }

    /**
     * Get all orders for this location
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Scope for active locations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific location type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get location type label
     */
    public function getTypeLabel()
    {
        return match($this->type) {
            'branch' => 'Branch Office',
            'warehouse' => 'Warehouse',
            'distribution_center' => 'Distribution Center',
            default => ucfirst($this->type)
        };
    }
}
