<?php

namespace App\Http\Controllers;

use App\Services\ReportService;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_reports')->only(['index', 'show']);
        $this->middleware('permission:generate_reports')->only(['generate', 'export']);
        $this->reportService = $reportService;
    }

    /**
     * Reports dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocations = $this->getAccessibleLocations($user);

        return view('reports.index', compact('accessibleLocations'));
    }

    /**
     * Generate business report
     */
    public function generateBusinessReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        // Filter requested locations by accessible ones
        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateBusinessReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        return response()->json($report);
    }

    /**
     * Generate sales report
     */
    public function generateSalesReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateSalesReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        return response()->json($report);
    }

    /**
     * Generate inventory report
     */
    public function generateInventoryReport(Request $request)
    {
        $request->validate([
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateInventoryReport($locationIds);

        return response()->json($report);
    }

    /**
     * Generate financial report
     */
    public function generateFinancialReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateFinancialReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        return response()->json($report);
    }

    /**
     * Generate customer report
     */
    public function generateCustomerReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        $report = $this->reportService->generateCustomerReport(
            $locationIds,
            $request->start_date,
            $request->end_date
        );

        return response()->json($report);
    }

    /**
     * Export report
     */
    public function exportReport(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:business,sales,inventory,financial,customer',
            'format' => 'required|in:pdf,csv,excel',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'location_ids' => 'nullable|array',
            'location_ids.*' => 'exists:locations,id',
        ]);

        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $locationIds = $request->location_ids
            ? array_intersect($request->location_ids, $accessibleLocationIds)
            : $accessibleLocationIds;

        // Generate report based on type
        switch ($request->report_type) {
            case 'business':
                $report = $this->reportService->generateBusinessReport(
                    $locationIds,
                    $request->start_date,
                    $request->end_date
                );
                break;
            case 'sales':
                $report = $this->reportService->generateSalesReport(
                    $locationIds,
                    $request->start_date,
                    $request->end_date
                );
                break;
            case 'inventory':
                $report = $this->reportService->generateInventoryReport($locationIds);
                break;
            case 'financial':
                $report = $this->reportService->generateFinancialReport(
                    $locationIds,
                    $request->start_date,
                    $request->end_date
                );
                break;
            case 'customer':
                $report = $this->reportService->generateCustomerReport(
                    $locationIds,
                    $request->start_date,
                    $request->end_date
                );
                break;
        }

        // Export based on format
        switch ($request->format) {
            case 'csv':
                return $this->exportToCsv($report, $request->report_type);
            case 'pdf':
                return $this->exportToPdf($report, $request->report_type);
            case 'excel':
                return $this->exportToExcel($report, $request->report_type);
            default:
                return response()->json($report);
        }
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }

    /**
     * Export to CSV
     */
    protected function exportToCsv($report, $reportType)
    {
        $filename = $reportType . '_report_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($report, $reportType) {
            $file = fopen('php://output', 'w');

            // Write CSV content based on report type
            $this->writeCsvContent($file, $report, $reportType);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to PDF (placeholder)
     */
    protected function exportToPdf($report, $reportType)
    {
        // This would use a PDF library like DomPDF
        return response()->json([
            'message' => 'PDF export functionality would be implemented here',
            'report_type' => $reportType,
            'data' => $report
        ]);
    }

    /**
     * Export to Excel (placeholder)
     */
    protected function exportToExcel($report, $reportType)
    {
        // This would use a library like PhpSpreadsheet
        return response()->json([
            'message' => 'Excel export functionality would be implemented here',
            'report_type' => $reportType,
            'data' => $report
        ]);
    }

    /**
     * Write CSV content
     */
    protected function writeCsvContent($file, $report, $reportType)
    {
        // Write headers
        fputcsv($file, ['Report Type', $reportType]);
        fputcsv($file, ['Generated At', now()->format('Y-m-d H:i:s')]);
        fputcsv($file, []); // Empty row

        // Write report content based on type
        foreach ($report as $section => $data) {
            fputcsv($file, [strtoupper(str_replace('_', ' ', $section))]);

            if (is_array($data)) {
                foreach ($data as $key => $value) {
                    if (is_array($value)) {
                        fputcsv($file, [$key, json_encode($value)]);
                    } else {
                        fputcsv($file, [$key, $value]);
                    }
                }
            }

            fputcsv($file, []); // Empty row
        }
    }
}
