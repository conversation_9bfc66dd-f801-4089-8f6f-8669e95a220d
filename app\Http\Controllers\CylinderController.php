<?php

namespace App\Http\Controllers;

use App\Models\Cylinder;
use App\Models\CylinderLog;
use App\Models\GasType;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class CylinderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view_cylinders', ['only' => ['index', 'show']]);
        $this->middleware('permission:create_cylinders', ['only' => ['create', 'store']]);
        $this->middleware('permission:edit_cylinders', ['only' => ['edit', 'update']]);
        $this->middleware('permission:delete_cylinders', ['only' => ['destroy']]);
        $this->middleware('permission:scan_cylinders', ['only' => ['scan', 'scanResult']]);
    }

    /**
     * Display a listing of cylinders
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Cylinder::with(['gasType', 'location']);

        // Apply location-based filtering for non-admin users
        if (!$user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            $accessibleLocationIds = $user->locations->pluck('id')->toArray();
            if ($user->hasRole('location_manager')) {
                $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
                $accessibleLocationIds = array_merge($accessibleLocationIds, $managedLocationIds);
            }
            $query->whereIn('location_id', $accessibleLocationIds);
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('gas_type')) {
            $query->where('gas_type_id', $request->gas_type);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unique_id', 'like', "%{$search}%")
                  ->orWhere('qr_code', 'like', "%{$search}%");
            });
        }

        // Special filters
        if ($request->filter === 'expired') {
            $query->expired();
        } elseif ($request->filter === 'inspection_due') {
            $query->dueForInspection();
        }

        $cylinders = $query->paginate(20);
        $gasTypes = GasType::active()->get();
        $locations = $this->getAccessibleLocations($user);

        return view('cylinders.index', compact('cylinders', 'gasTypes', 'locations'));
    }

    /**
     * Show the form for creating a new cylinder
     */
    public function create()
    {
        $gasTypes = GasType::active()->get();
        $locations = $this->getAccessibleLocations(Auth::user());

        return view('cylinders.create', compact('gasTypes', 'locations'));
    }

    /**
     * Store a newly created cylinder
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'unique_id' => 'required|string|max:255|unique:cylinders',
            'gas_type_id' => 'required|exists:gas_types,id',
            'location_id' => 'required|exists:locations,id',
            'capacity' => 'required|numeric|min:0',
            'tare_weight' => 'required|numeric|min:0',
            'expiry_date' => 'nullable|date|after:today',
            'last_inspection_date' => 'nullable|date|before_or_equal:today',
            'next_inspection_date' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($validated['location_id'])) {
            abort(403, 'You do not have access to this location.');
        }

        $cylinder = Cylinder::create([
            'unique_id' => $validated['unique_id'],
            'gas_type_id' => $validated['gas_type_id'],
            'location_id' => $validated['location_id'],
            'status' => 'empty', // Default status
            'capacity' => $validated['capacity'],
            'tare_weight' => $validated['tare_weight'],
            'expiry_date' => $validated['expiry_date'],
            'last_inspection_date' => $validated['last_inspection_date'],
            'next_inspection_date' => $validated['next_inspection_date'],
            'notes' => $validated['notes'],
        ]);

        // Generate QR code
        $cylinder->generateQrCode();

        // Log the creation
        CylinderLog::createLog($cylinder->id, 'created', [
            'new_status' => 'empty',
            'location_id' => $cylinder->location_id,
            'notes' => 'Cylinder created and added to inventory',
        ]);

        return redirect()->route('cylinders.index')
                        ->with('success', 'Cylinder created successfully.');
    }

    /**
     * Display the specified cylinder
     */
    public function show(Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $cylinder->load(['gasType', 'location', 'logs.user', 'logs.location']);

        return view('cylinders.show', compact('cylinder'));
    }

    /**
     * Show the form for editing the cylinder
     */
    public function edit(Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $gasTypes = GasType::active()->get();
        $locations = $this->getAccessibleLocations($user);

        return view('cylinders.edit', compact('cylinder', 'gasTypes', 'locations'));
    }

    /**
     * Update the specified cylinder
     */
    public function update(Request $request, Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $validated = $request->validate([
            'unique_id' => ['required', 'string', 'max:255', Rule::unique('cylinders')->ignore($cylinder->id)],
            'gas_type_id' => 'required|exists:gas_types,id',
            'location_id' => 'required|exists:locations,id',
            'status' => 'required|in:empty,full,in_use,damaged,expired,maintenance,in_transit',
            'capacity' => 'required|numeric|min:0',
            'tare_weight' => 'required|numeric|min:0',
            'current_weight' => 'nullable|numeric|min:0',
            'expiry_date' => 'nullable|date|after:today',
            'last_inspection_date' => 'nullable|date|before_or_equal:today',
            'next_inspection_date' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check new location access
        if (!$user->hasLocationAccess($validated['location_id'])) {
            abort(403, 'You do not have access to the target location.');
        }

        $oldStatus = $cylinder->status;
        $oldLocationId = $cylinder->location_id;

        $cylinder->update($validated);

        // Log status change
        if ($oldStatus !== $validated['status']) {
            CylinderLog::createLog($cylinder->id, 'status_changed', [
                'old_status' => $oldStatus,
                'new_status' => $validated['status'],
                'location_id' => $cylinder->location_id,
                'notes' => 'Status updated via edit form',
            ]);
        }

        // Log location change
        if ($oldLocationId !== $validated['location_id']) {
            CylinderLog::createLog($cylinder->id, 'moved', [
                'old_status' => $cylinder->status,
                'new_status' => $cylinder->status,
                'location_id' => $validated['location_id'],
                'notes' => "Moved from location ID {$oldLocationId} to {$validated['location_id']}",
                'metadata' => [
                    'old_location_id' => $oldLocationId,
                    'new_location_id' => $validated['location_id'],
                ],
            ]);
        }

        return redirect()->route('cylinders.show', $cylinder)
                        ->with('success', 'Cylinder updated successfully.');
    }

    /**
     * Remove the specified cylinder
     */
    public function destroy(Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        // Prevent deletion if cylinder is in use
        if ($cylinder->status === 'in_use') {
            return redirect()->route('cylinders.index')
                            ->with('error', 'Cannot delete cylinder that is currently in use.');
        }

        // Log deletion
        CylinderLog::createLog($cylinder->id, 'deleted', [
            'old_status' => $cylinder->status,
            'new_status' => 'deleted',
            'location_id' => $cylinder->location_id,
            'notes' => 'Cylinder deleted from system',
        ]);

        $cylinder->delete();

        return redirect()->route('cylinders.index')
                        ->with('success', 'Cylinder deleted successfully.');
    }

    /**
     * Generate QR code for cylinder
     */
    public function generateQr(Cylinder $cylinder)
    {
        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            abort(403, 'You do not have access to this cylinder.');
        }

        $qrCode = QrCode::format('svg')
                        ->size(200)
                        ->generate($cylinder->qr_code);

        return response($qrCode, 200)
               ->header('Content-Type', 'image/svg+xml');
    }

    /**
     * Scan cylinder by QR code
     */
    public function scan(Request $request)
    {
        $request->validate([
            'qr_code' => 'required|string',
        ]);

        $cylinder = Cylinder::where('qr_code', $request->qr_code)->first();

        if (!$cylinder) {
            return response()->json([
                'success' => false,
                'message' => 'Cylinder not found.',
            ], 404);
        }

        // Check location access
        $user = Auth::user();
        if (!$user->hasLocationAccess($cylinder->location_id)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this cylinder.',
            ], 403);
        }

        // Log the scan
        CylinderLog::createLog($cylinder->id, 'scanned', [
            'old_status' => $cylinder->status,
            'new_status' => $cylinder->status,
            'location_id' => $cylinder->location_id,
            'notes' => 'Cylinder scanned via mobile interface',
        ]);

        $cylinder->load(['gasType', 'location']);

        return response()->json([
            'success' => true,
            'cylinder' => $cylinder,
            'status_label' => $cylinder->getStatusLabel(),
        ]);
    }

    /**
     * Get accessible locations for the user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
