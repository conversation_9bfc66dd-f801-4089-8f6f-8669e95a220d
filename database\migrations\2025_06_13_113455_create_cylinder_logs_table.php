<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cylinder_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cylinder_id')->constrained()->onDelete('cascade');
            $table->string('action'); // filled, emptied, moved, damaged, repaired, etc.
            $table->string('old_status')->nullable();
            $table->string('new_status');
            $table->foreignId('location_id')->nullable()->constrained();
            $table->foreignId('user_id')->constrained();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional data like weight, pressure, etc.
            $table->timestamps();

            $table->index(['cylinder_id', 'created_at']);
            $table->index(['action', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cylinder_logs');
    }
};
