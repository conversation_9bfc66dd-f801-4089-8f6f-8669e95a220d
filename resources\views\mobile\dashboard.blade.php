<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="GCMS">

    <title>{{ config('app.name', 'GCMS') }} - Mobile</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        /* Mobile-specific styles */
        body {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: transparent;
            overscroll-behavior: none;
        }
        
        .mobile-container {
            max-width: 100vw;
            overflow-x: hidden;
        }
        
        .touch-action {
            touch-action: manipulation;
        }
        
        .safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }
        
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .slide-up {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>

<body class="bg-gray-50 mobile-container safe-area">
    <!-- Mobile Header -->
    <header class="bg-blue-600 text-white sticky top-0 z-50">
        <div class="flex items-center justify-between p-4">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-sm">G</span>
                </div>
                <div>
                    <h1 class="font-semibold text-lg">GCMS Mobile</h1>
                    <p class="text-blue-200 text-xs">{{ $user->name }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <button id="notificationBtn" class="relative p-2 rounded-full hover:bg-blue-700 touch-action">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19c-5 0-8-3-8-6s3-6 8-6 8 3 8 6-3 6-8 6z"></path>
                    </svg>
                    <span id="notificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                </button>
                <button id="menuBtn" class="p-2 rounded-full hover:bg-blue-700 touch-action">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Quick Stats -->
    <section class="p-4">
        <div class="grid grid-cols-2 gap-3">
            <div class="bg-white rounded-lg p-4 card-shadow slide-up">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Pending Orders</p>
                        <p class="text-2xl font-bold text-blue-600" id="pendingOrders">{{ $dashboardData['quick_stats']['pending_orders'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">📦</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-4 card-shadow slide-up">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Active Rentals</p>
                        <p class="text-2xl font-bold text-green-600" id="activeRentals">{{ $dashboardData['quick_stats']['active_rentals'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">🔄</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-4 card-shadow slide-up">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Critical Tanks</p>
                        <p class="text-2xl font-bold text-red-600" id="criticalTanks">{{ $dashboardData['quick_stats']['critical_tanks'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">🛢️</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-4 card-shadow slide-up">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Overdue</p>
                        <p class="text-2xl font-bold text-orange-600" id="overdueInvoices">{{ $dashboardData['quick_stats']['overdue_invoices'] }}</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">⏰</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="p-4">
        <h2 class="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div class="grid grid-cols-4 gap-3">
            <a href="/mobile/qr-scanner" class="bg-white rounded-lg p-4 text-center card-shadow touch-action">
                <div class="text-3xl mb-2">📱</div>
                <p class="text-xs font-medium text-gray-700">Scan QR</p>
            </a>
            
            <a href="/mobile/customers/search" class="bg-white rounded-lg p-4 text-center card-shadow touch-action">
                <div class="text-3xl mb-2">👥</div>
                <p class="text-xs font-medium text-gray-700">Customers</p>
            </a>
            
            <a href="/mobile/orders/create" class="bg-white rounded-lg p-4 text-center card-shadow touch-action">
                <div class="text-3xl mb-2">➕</div>
                <p class="text-xs font-medium text-gray-700">New Order</p>
            </a>
            
            <a href="/mobile/tanks" class="bg-white rounded-lg p-4 text-center card-shadow touch-action">
                <div class="text-3xl mb-2">🛢️</div>
                <p class="text-xs font-medium text-gray-700">Tanks</p>
            </a>
        </div>
    </section>

    <!-- Urgent Alerts -->
    @if(count($dashboardData['urgent_alerts']) > 0)
    <section class="p-4">
        <h2 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span class="w-3 h-3 bg-red-500 rounded-full mr-2 pulse-animation"></span>
            Urgent Alerts
        </h2>
        <div class="space-y-2">
            @foreach($dashboardData['urgent_alerts'] as $alert)
            <div class="bg-red-50 border-l-4 border-red-500 p-3 rounded-r-lg">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <p class="text-red-800 font-medium text-sm">{{ $alert['message'] }}</p>
                        <p class="text-red-600 text-xs mt-1">Tank: {{ $alert['tank'] }} • {{ $alert['time'] }}</p>
                    </div>
                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">{{ ucfirst($alert['severity']) }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif

    <!-- Today's Tasks -->
    @if(count($dashboardData['today_tasks']) > 0)
    <section class="p-4">
        <h2 class="text-lg font-semibold text-gray-900 mb-3">Today's Tasks</h2>
        <div class="space-y-2">
            @foreach($dashboardData['today_tasks'] as $task)
            <div class="bg-white rounded-lg p-3 card-shadow">
                <div class="flex justify-between items-center">
                    <div class="flex-1">
                        <p class="font-medium text-gray-900 text-sm">{{ $task['title'] }}</p>
                        <p class="text-gray-600 text-xs">{{ $task['time'] }}</p>
                    </div>
                    <div class="text-2xl">
                        @if($task['type'] === 'delivery')
                            🚚
                        @else
                            📋
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif

    <!-- Recent Activities -->
    <section class="p-4 pb-20">
        <h2 class="text-lg font-semibold text-gray-900 mb-3">Recent Activities</h2>
        <div class="space-y-2">
            @foreach($dashboardData['recent_activities'] as $activity)
            <div class="bg-white rounded-lg p-3 card-shadow">
                <div class="flex justify-between items-center">
                    <div class="flex-1">
                        <p class="font-medium text-gray-900 text-sm">{{ $activity['title'] }}</p>
                        <p class="text-gray-600 text-xs">{{ $activity['subtitle'] }} • {{ $activity['time'] }}</p>
                    </div>
                    <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">{{ ucfirst($activity['status']) }}</span>
                </div>
            </div>
            @endforeach
        </div>
    </section>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area">
        <div class="grid grid-cols-5 py-2">
            <a href="/mobile/dashboard" class="flex flex-col items-center py-2 text-blue-600">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
                <span class="text-xs mt-1">Dashboard</span>
            </a>
            
            <a href="/mobile/qr-scanner" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                </svg>
                <span class="text-xs mt-1">Scan</span>
            </a>
            
            <a href="/mobile/customers/search" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <span class="text-xs mt-1">Customers</span>
            </a>
            
            <a href="/mobile/tanks" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                <span class="text-xs mt-1">Tanks</span>
            </a>
            
            <a href="/mobile/menu" class="flex flex-col items-center py-2 text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
                <span class="text-xs mt-1">Menu</span>
            </a>
        </div>
    </nav>

    <!-- Offline Indicator -->
    <div id="offlineIndicator" class="fixed top-16 left-4 right-4 bg-orange-500 text-white p-3 rounded-lg shadow-lg hidden">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <span class="text-sm font-medium">You're offline. Some features may be limited.</span>
        </div>
    </div>

    <script>
        // PWA Installation
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });

        function showInstallPrompt() {
            // Show install prompt after 30 seconds
            setTimeout(() => {
                if (deferredPrompt && !window.matchMedia('(display-mode: standalone)').matches) {
                    const installBanner = document.createElement('div');
                    installBanner.className = 'fixed bottom-20 left-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50';
                    installBanner.innerHTML = `
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium">Install GCMS App</p>
                                <p class="text-sm text-blue-200">Get the full app experience</p>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="installApp()" class="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium">Install</button>
                                <button onclick="dismissInstall()" class="text-blue-200 px-2 py-1 text-sm">✕</button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(installBanner);
                    window.installBanner = installBanner;
                }
            }, 30000);
        }

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((result) => {
                    if (result.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    dismissInstall();
                });
            }
        }

        function dismissInstall() {
            if (window.installBanner) {
                window.installBanner.remove();
            }
        }

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Online/Offline Detection
        function updateOnlineStatus() {
            const offlineIndicator = document.getElementById('offlineIndicator');
            if (navigator.onLine) {
                offlineIndicator.classList.add('hidden');
                // Sync offline data when back online
                syncOfflineData();
            } else {
                offlineIndicator.classList.remove('hidden');
            }
        }

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);

        // Real-time updates
        function updateDashboardData() {
            if (navigator.onLine) {
                fetch('/mobile/dashboard', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Update quick stats
                    document.getElementById('pendingOrders').textContent = data.quick_stats.pending_orders;
                    document.getElementById('activeRentals').textContent = data.quick_stats.active_rentals;
                    document.getElementById('criticalTanks').textContent = data.quick_stats.critical_tanks;
                    document.getElementById('overdueInvoices').textContent = data.quick_stats.overdue_invoices;
                    
                    // Update notification badge
                    const totalAlerts = data.urgent_alerts.length;
                    const badge = document.getElementById('notificationBadge');
                    if (totalAlerts > 0) {
                        badge.textContent = totalAlerts;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Failed to update dashboard data:', error);
                });
            }
        }

        // Sync offline data
        function syncOfflineData() {
            // This would sync any offline actions when connection is restored
            console.log('Syncing offline data...');
        }

        // Touch feedback
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('.touch-action')) {
                e.target.closest('.touch-action').style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.closest('.touch-action')) {
                setTimeout(() => {
                    e.target.closest('.touch-action').style.transform = 'scale(1)';
                }, 100);
            }
        });

        // Initialize
        updateOnlineStatus();
        updateDashboardData();
        
        // Update every 30 seconds
        setInterval(updateDashboardData, 30000);
    </script>
</body>
</html>
