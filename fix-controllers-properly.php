<?php
/**
 * Fix Controller Middleware Syntax Properly
 */

echo "🔧 Fixing Controller Middleware Syntax Properly\n";
echo "===============================================\n\n";

$controllers = [
    'app/Http/Controllers/CustomerController.php',
    'app/Http/Controllers/OrderController.php',
    'app/Http/Controllers/LocationController.php',
    'app/Http/Controllers/RentalController.php',
    'app/Http/Controllers/TankController.php',
    'app/Http/Controllers/ReportController.php',
    'app/Http/Controllers/WhatsAppController.php',
    'app/Http/Controllers/ComplianceController.php',
    'app/Http/Controllers/SystemController.php',
];

foreach ($controllers as $controllerPath) {
    if (file_exists($controllerPath)) {
        echo "Fixing: $controllerPath\n";
        
        $content = file_get_contents($controllerPath);
        
        // Fix the broken syntax from previous attempt
        $content = str_replace('), [\'only\' =>', ', [\'only\' =>', $content);
        $content = str_replace('), [\'except\' =>', ', [\'except\' =>', $content);
        
        file_put_contents($controllerPath, $content);
        echo "✅ Fixed: $controllerPath\n";
    } else {
        echo "⚠️  Not found: $controllerPath\n";
    }
}

echo "\n🎉 Controller middleware syntax fixed properly!\n";
echo "Testing routes now...\n";
?>
