<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',

            // Role Management
            'view_roles',
            'create_roles',
            'edit_roles',
            'delete_roles',

            // Location Management
            'view_locations',
            'create_locations',
            'edit_locations',
            'delete_locations',
            'manage_location_users',

            // Cylinder Management
            'view_cylinders',
            'create_cylinders',
            'edit_cylinders',
            'delete_cylinders',
            'scan_cylinders',
            'generate_qr_codes',
            'bulk_import_cylinders',

            // Customer Management
            'view_customers',
            'create_customers',
            'edit_customers',
            'delete_customers',

            // Order Management
            'view_orders',
            'create_orders',
            'edit_orders',
            'delete_orders',
            'assign_orders',
            'deliver_orders',

            // Rental Management
            'view_rentals',
            'create_rentals',
            'edit_rentals',
            'delete_rentals',
            'calculate_late_fees',

            // Invoice Management
            'view_invoices',
            'create_invoices',
            'edit_invoices',
            'delete_invoices',
            'send_invoices',

            // Payment Management
            'view_payments',
            'create_payments',
            'edit_payments',
            'delete_payments',

            // Tank Management
            'view_tanks',
            'create_tanks',
            'edit_tanks',
            'delete_tanks',
            'manage_refills',

            // Reports & Analytics
            'view_reports',
            'export_reports',
            'view_analytics',
            'view_audit_logs',

            // WhatsApp Management
            'view_whatsapp_templates',
            'create_whatsapp_templates',
            'edit_whatsapp_templates',
            'send_whatsapp_messages',

            // System Settings
            'view_settings',
            'edit_settings',
            'manage_gas_types',

            // Compliance & Security
            'view_compliance',
            'generate_compliance_reports',
            'view_audit_trails',
            'manage_security_settings',

            // System Administration
            'manage_system',
            'view_system_metrics',
            'manage_backups',
            'optimize_system',

            // Dashboard Access
            'view_dashboard',
            'view_location_dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'super_admin', 'guard_name' => 'web']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - Most permissions except system settings
        $admin = Role::create(['name' => 'admin', 'guard_name' => 'web']);
        $adminPermissions = [
            'view_users', 'create_users', 'edit_users',
            'view_locations', 'create_locations', 'edit_locations', 'manage_location_users',
            'view_cylinders', 'create_cylinders', 'edit_cylinders', 'scan_cylinders', 'generate_qr_codes', 'bulk_import_cylinders',
            'view_customers', 'create_customers', 'edit_customers',
            'view_orders', 'create_orders', 'edit_orders', 'assign_orders', 'deliver_orders',
            'view_rentals', 'create_rentals', 'edit_rentals', 'calculate_late_fees',
            'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices',
            'view_payments', 'create_payments', 'edit_payments',
            'view_tanks', 'create_tanks', 'edit_tanks', 'manage_refills',
            'view_reports', 'export_reports', 'view_analytics',
            'view_whatsapp_templates', 'create_whatsapp_templates', 'edit_whatsapp_templates', 'send_whatsapp_messages',
            'view_dashboard',
        ];
        $admin->givePermissionTo($adminPermissions);

        // Auditor - Read-only access to reports and data
        $auditor = Role::create(['name' => 'auditor', 'guard_name' => 'web']);
        $auditorPermissions = [
            'view_users', 'view_locations', 'view_cylinders', 'view_customers',
            'view_orders', 'view_rentals', 'view_invoices', 'view_payments',
            'view_tanks', 'view_reports', 'export_reports', 'view_analytics', 'view_audit_logs',
            'view_dashboard',
        ];
        $auditor->givePermissionTo($auditorPermissions);

        // Location Manager - Location-specific management
        $locationManager = Role::create(['name' => 'location_manager', 'guard_name' => 'web']);
        $locationManagerPermissions = [
            'view_cylinders', 'edit_cylinders', 'scan_cylinders',
            'view_customers', 'create_customers', 'edit_customers',
            'view_orders', 'create_orders', 'edit_orders', 'assign_orders', 'deliver_orders',
            'view_rentals', 'create_rentals', 'edit_rentals',
            'view_invoices', 'view_payments',
            'view_tanks', 'manage_refills',
            'view_reports', 'export_reports',
            'send_whatsapp_messages',
            'view_location_dashboard',
        ];
        $locationManager->givePermissionTo($locationManagerPermissions);

        // Staff - Basic operations
        $staff = Role::create(['name' => 'staff', 'guard_name' => 'web']);
        $staffPermissions = [
            'view_cylinders', 'scan_cylinders',
            'view_customers',
            'view_orders', 'deliver_orders',
            'view_rentals',
            'view_location_dashboard',
        ];
        $staff->givePermissionTo($staffPermissions);

        // Customer - Limited access
        $customer = Role::create(['name' => 'customer', 'guard_name' => 'web']);
        $customerPermissions = [
            'view_orders', 'create_orders',
            'view_invoices', 'view_payments',
            'scan_cylinders', // To view their cylinder history
        ];
        $customer->givePermissionTo($customerPermissions);

        // Create default super admin user
        $superAdminUser = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => bcrypt('password'),
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $superAdminUser->assignRole('super_admin');

        $this->command->info('Roles and permissions created successfully!');
        $this->command->info('Super Admin created: <EMAIL> / password');
    }
}
