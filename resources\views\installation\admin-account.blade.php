@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-user-shield text-cyan-500"></i> Create Admin Account
    </h2>
    <form id="create-admin-form" class="space-y-5 mt-4">
        @csrf
        <div>
            <label for="name" class="block text-sm font-semibold text-gray-700 mb-1">Name</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="name" name="name" required>
        </div>
        <div>
            <label for="email" class="block text-sm font-semibold text-gray-700 mb-1">Email</label>
            <input type="email" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="email" name="email" required>
        </div>
        <div>
            <label for="password" class="block text-sm font-semibold text-gray-700 mb-1">Password</label>
            <input type="password" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="password" name="password" required>
        </div>
        <div>
            <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-1">Confirm Password</label>
            <input type="password" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="password_confirmation" name="password_confirmation" required>
        </div>
        <div>
            <label for="phone" class="block text-sm font-semibold text-gray-700 mb-1">Phone (Optional)</label>
            <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-cyan-400 focus:outline-none text-gray-800" id="phone" name="phone">
        </div>
        <button type="submit" class="w-full px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center justify-center gap-2">
            <i class="fas fa-user-plus"></i> Create Admin
        </button>
    </form>

    <div id="admin-status" class="mt-3"></div>

    <a href="{{ route('install.company.info') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    document.getElementById('create-admin-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('admin-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Creating admin account...</div>';

        const formData = new FormData(this);

        fetch('{{ route('install.create.admin') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('admin-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                let errors = '';
                if (data.errors) {
                    for (const error in data.errors) {
                        errors += '<p>' + data.errors[error][0] + '</p>';
                    }
                }
                document.getElementById('admin-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + errors + '</div>';
            }
        });
    });
</script>
@endpush
