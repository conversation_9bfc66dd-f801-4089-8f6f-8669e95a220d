<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'order_id',
        'rental_id',
        'customer_id',
        'location_id',
        'invoice_type',
        'status',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'outstanding_amount',
        'currency',
        'payment_terms',
        'due_date',
        'issued_at',
        'paid_at',
        'notes',
        'billing_address',
        'shipping_address',
        'payment_method',
        'payment_reference',
        'late_fee',
        'finance_charge',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:4',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'outstanding_amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'finance_charge' => 'decimal:2',
        'due_date' => 'date',
        'issued_at' => 'datetime',
        'paid_at' => 'datetime',
        'billing_address' => 'array',
        'shipping_address' => 'array',
    ];

    /**
     * Invoice types
     */
    const TYPES = [
        'order' => 'Order Invoice',
        'rental' => 'Rental Invoice',
        'service' => 'Service Invoice',
        'credit' => 'Credit Note',
        'debit' => 'Debit Note',
    ];

    /**
     * Invoice statuses
     */
    const STATUSES = [
        'draft' => 'Draft',
        'pending' => 'Pending',
        'sent' => 'Sent',
        'paid' => 'Paid',
        'partial' => 'Partially Paid',
        'overdue' => 'Overdue',
        'cancelled' => 'Cancelled',
        'refunded' => 'Refunded',
    ];

    /**
     * Payment terms
     */
    const PAYMENT_TERMS = [
        'immediate' => 'Immediate',
        'net_7' => 'Net 7 Days',
        'net_15' => 'Net 15 Days',
        'net_30' => 'Net 30 Days',
        'net_60' => 'Net 60 Days',
        'net_90' => 'Net 90 Days',
    ];

    /**
     * Get the order for this invoice
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the rental for this invoice
     */
    public function rental(): BelongsTo
    {
        return $this->belongsTo(Rental::class);
    }

    /**
     * Get the customer for this invoice
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the location for this invoice
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get invoice items
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get payments for this invoice
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Generate unique invoice number
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV-' . date('Ymd') . '-';
        $lastInvoice = static::where('invoice_number', 'like', $prefix . '%')
                            ->orderBy('id', 'desc')
                            ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get invoice type label
     */
    public function getTypeLabel(): string
    {
        return self::TYPES[$this->invoice_type] ?? ucfirst($this->invoice_type);
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'draft' => ['label' => 'Draft', 'color' => 'gray'],
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'sent' => ['label' => 'Sent', 'color' => 'blue'],
            'paid' => ['label' => 'Paid', 'color' => 'green'],
            'partial' => ['label' => 'Partially Paid', 'color' => 'orange'],
            'overdue' => ['label' => 'Overdue', 'color' => 'red'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'gray'],
            'refunded' => ['label' => 'Refunded', 'color' => 'purple'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get payment terms label
     */
    public function getPaymentTermsLabel(): string
    {
        return self::PAYMENT_TERMS[$this->payment_terms] ?? ucfirst(str_replace('_', ' ', $this->payment_terms));
    }

    /**
     * Check if invoice is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'pending' && $this->due_date && $this->due_date->isPast();
    }

    /**
     * Calculate days overdue
     */
    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->due_date->diffInDays(now());
    }

    /**
     * Calculate late fees
     */
    public function calculateLateFees(): float
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $daysOverdue = $this->getDaysOverdue();
        $lateFeeRate = 0.015; // 1.5% per month (0.05% per day)

        return $this->outstanding_amount * ($lateFeeRate / 30) * $daysOverdue;
    }

    /**
     * Apply payment to invoice
     */
    public function applyPayment(float $amount, string $method = 'cash', string $reference = null): bool
    {
        if ($amount <= 0 || $amount > $this->outstanding_amount) {
            return false;
        }

        $this->paid_amount += $amount;
        $this->outstanding_amount -= $amount;

        if ($this->outstanding_amount <= 0.01) { // Account for floating point precision
            $this->status = 'paid';
            $this->paid_at = now();
            $this->outstanding_amount = 0;
        } elseif ($this->paid_amount > 0) {
            $this->status = 'partial';
        }

        $this->save();

        // Create payment record
        Payment::create([
            'invoice_id' => $this->id,
            'customer_id' => $this->customer_id,
            'amount' => $amount,
            'payment_method' => $method,
            'payment_reference' => $reference,
            'payment_date' => now(),
            'status' => 'completed',
        ]);

        return true;
    }

    /**
     * Mark invoice as sent
     */
    public function markAsSent(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update(['status' => 'sent']);
        return true;
    }

    /**
     * Cancel invoice
     */
    public function cancel($reason = null): bool
    {
        if (in_array($this->status, ['paid', 'cancelled', 'refunded'])) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason,
        ]);

        return true;
    }

    /**
     * Process refund
     */
    public function processRefund(float $amount, string $reason = null): bool
    {
        if ($amount <= 0 || $amount > $this->paid_amount) {
            return false;
        }

        $this->paid_amount -= $amount;
        $this->outstanding_amount += $amount;

        if ($this->paid_amount <= 0.01) {
            $this->status = 'refunded';
        } else {
            $this->status = 'partial';
        }

        $this->save();

        // Create refund payment record
        Payment::create([
            'invoice_id' => $this->id,
            'customer_id' => $this->customer_id,
            'amount' => -$amount,
            'payment_method' => 'refund',
            'payment_reference' => $reason,
            'payment_date' => now(),
            'status' => 'completed',
        ]);

        return true;
    }

    /**
     * Update status based on due date
     */
    public function updateOverdueStatus(): void
    {
        if ($this->status === 'sent' && $this->isOverdue()) {
            $this->update(['status' => 'overdue']);
        }
    }

    /**
     * Scope for overdue invoices
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function ($q) {
                        $q->where('status', 'sent')
                          ->where('due_date', '<', now());
                    });
    }

    /**
     * Scope for pending invoices
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['pending', 'sent']);
    }

    /**
     * Scope for paid invoices
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for invoices at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for invoices by customer
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }
}
