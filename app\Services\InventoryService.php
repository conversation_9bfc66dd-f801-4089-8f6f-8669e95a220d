<?php

namespace App\Services;

use App\Models\Inventory;
use App\Models\StockMovement;
use App\Models\InventoryTransfer;
use App\Models\Location;
use App\Models\GasType;
use App\Models\Cylinder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InventoryService
{
    /**
     * Get inventory overview for locations
     */
    public function getInventoryOverview($locationIds = null)
    {
        $query = Inventory::with(['location', 'gasType']);
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return $query->get()->groupBy('location_id');
    }

    /**
     * Get low stock alerts
     */
    public function getLowStockAlerts($locationIds = null)
    {
        $query = Inventory::with(['location', 'gasType'])->lowStock();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return $query->get();
    }

    /**
     * Get critical stock alerts
     */
    public function getCriticalStockAlerts($locationIds = null)
    {
        $query = Inventory::with(['location', 'gasType'])->criticalStock();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        return $query->get();
    }

    /**
     * Sync all inventory with actual cylinder counts
     */
    public function syncAllInventory($locationIds = null)
    {
        $locations = $locationIds ? Location::whereIn('id', $locationIds)->get() : Location::all();
        $gasTypes = GasType::active()->get();
        
        $synced = 0;
        
        foreach ($locations as $location) {
            foreach ($gasTypes as $gasType) {
                $inventory = Inventory::firstOrCreate(
                    [
                        'location_id' => $location->id,
                        'gas_type_id' => $gasType->id,
                    ],
                    [
                        'full_count' => 0,
                        'empty_count' => 0,
                        'damaged_count' => 0,
                        'maintenance_count' => 0,
                        'in_use_count' => 0,
                        'in_transit_count' => 0,
                        'expired_count' => 0,
                        'min_stock_level' => 10,
                        'max_stock_level' => 100,
                        'reorder_level' => 20,
                    ]
                );
                
                $inventory->syncWithCylinders();
                $synced++;
            }
        }
        
        return $synced;
    }

    /**
     * Create stock adjustment
     */
    public function createStockAdjustment($locationId, $gasTypeId, $adjustments, $reason = null)
    {
        DB::beginTransaction();
        
        try {
            $inventory = Inventory::firstOrCreate(
                ['location_id' => $locationId, 'gas_type_id' => $gasTypeId],
                [
                    'full_count' => 0,
                    'empty_count' => 0,
                    'damaged_count' => 0,
                    'maintenance_count' => 0,
                    'in_use_count' => 0,
                    'in_transit_count' => 0,
                    'expired_count' => 0,
                    'min_stock_level' => 10,
                    'max_stock_level' => 100,
                    'reorder_level' => 20,
                ]
            );

            foreach ($adjustments as $status => $quantity) {
                if ($quantity != 0) {
                    StockMovement::createMovement([
                        'location_id' => $locationId,
                        'gas_type_id' => $gasTypeId,
                        'movement_type' => 'adjustment',
                        'quantity' => abs($quantity),
                        'reference_type' => 'adjustment',
                        'notes' => $reason ?: 'Stock adjustment',
                        'metadata' => [
                            'status' => $status,
                            'adjustment_type' => $quantity > 0 ? 'increase' : 'decrease',
                        ],
                    ]);
                }
            }

            // Sync inventory after adjustments
            $inventory->syncWithCylinders();
            
            DB::commit();
            return $inventory;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Request inventory transfer
     */
    public function requestTransfer($fromLocationId, $toLocationId, $gasTypeId, $quantity, $reason = null, $cylinderIds = null)
    {
        // Check if source location has enough stock
        $sourceInventory = Inventory::where('location_id', $fromLocationId)
                                  ->where('gas_type_id', $gasTypeId)
                                  ->first();

        if (!$sourceInventory || $sourceInventory->available_count < $quantity) {
            throw new \Exception('Insufficient stock at source location');
        }

        $transfer = InventoryTransfer::create([
            'transfer_number' => InventoryTransfer::generateTransferNumber(),
            'from_location_id' => $fromLocationId,
            'to_location_id' => $toLocationId,
            'gas_type_id' => $gasTypeId,
            'quantity' => $quantity,
            'status' => 'pending',
            'requested_by' => Auth::id(),
            'requested_at' => now(),
            'reason' => $reason,
            'cylinder_ids' => $cylinderIds,
        ]);

        return $transfer;
    }

    /**
     * Get inventory statistics
     */
    public function getInventoryStatistics($locationIds = null)
    {
        $query = Inventory::query();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $inventories = $query->with(['location', 'gasType'])->get();

        return [
            'total_locations' => $inventories->groupBy('location_id')->count(),
            'total_gas_types' => $inventories->groupBy('gas_type_id')->count(),
            'total_cylinders' => $inventories->sum('total_count'),
            'available_cylinders' => $inventories->sum('available_count'),
            'in_use_cylinders' => $inventories->sum('in_use_count'),
            'damaged_cylinders' => $inventories->sum('damaged_count'),
            'maintenance_cylinders' => $inventories->sum('maintenance_count'),
            'low_stock_items' => $inventories->filter(fn($inv) => $inv->isLowStock())->count(),
            'critical_stock_items' => $inventories->filter(fn($inv) => $inv->isCriticalStock())->count(),
            'overstock_items' => $inventories->filter(fn($inv) => $inv->isOverstock())->count(),
        ];
    }

    /**
     * Get stock movement history
     */
    public function getStockMovementHistory($locationId = null, $gasTypeId = null, $days = 30)
    {
        $query = StockMovement::with(['location', 'gasType', 'user', 'cylinder'])
                             ->where('created_at', '>=', now()->subDays($days))
                             ->orderBy('created_at', 'desc');

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        if ($gasTypeId) {
            $query->where('gas_type_id', $gasTypeId);
        }

        return $query->paginate(50);
    }

    /**
     * Get pending transfers
     */
    public function getPendingTransfers($locationIds = null)
    {
        $query = InventoryTransfer::with(['fromLocation', 'toLocation', 'gasType', 'requestedBy'])
                                 ->pending()
                                 ->orderBy('requested_at', 'asc');

        if ($locationIds) {
            $query->where(function ($q) use ($locationIds) {
                $q->whereIn('from_location_id', $locationIds)
                  ->orWhereIn('to_location_id', $locationIds);
            });
        }

        return $query->get();
    }

    /**
     * Get transfer history
     */
    public function getTransferHistory($locationIds = null, $days = 30)
    {
        $query = InventoryTransfer::with(['fromLocation', 'toLocation', 'gasType', 'requestedBy', 'approvedBy'])
                                 ->where('created_at', '>=', now()->subDays($days))
                                 ->orderBy('created_at', 'desc');

        if ($locationIds) {
            $query->where(function ($q) use ($locationIds) {
                $q->whereIn('from_location_id', $locationIds)
                  ->orWhereIn('to_location_id', $locationIds);
            });
        }

        return $query->paginate(20);
    }

    /**
     * Update stock levels for inventory item
     */
    public function updateStockLevels($inventoryId, $minLevel, $maxLevel, $reorderLevel)
    {
        $inventory = Inventory::findOrFail($inventoryId);
        
        // Validate levels
        if ($minLevel >= $reorderLevel || $reorderLevel >= $maxLevel) {
            throw new \Exception('Invalid stock levels: min < reorder < max');
        }

        $inventory->update([
            'min_stock_level' => $minLevel,
            'max_stock_level' => $maxLevel,
            'reorder_level' => $reorderLevel,
        ]);

        return $inventory;
    }

    /**
     * Get reorder suggestions
     */
    public function getReorderSuggestions($locationIds = null)
    {
        $query = Inventory::with(['location', 'gasType'])
                         ->lowStock();

        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $lowStockItems = $query->get();
        
        $suggestions = [];
        
        foreach ($lowStockItems as $item) {
            $suggestedQuantity = $item->max_stock_level - $item->available_count;
            
            $suggestions[] = [
                'inventory' => $item,
                'current_stock' => $item->available_count,
                'reorder_level' => $item->reorder_level,
                'max_level' => $item->max_stock_level,
                'suggested_quantity' => $suggestedQuantity,
                'priority' => $item->isCriticalStock() ? 'critical' : 'normal',
            ];
        }

        return collect($suggestions)->sortBy(function ($suggestion) {
            return $suggestion['priority'] === 'critical' ? 0 : 1;
        });
    }
}
