<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Payment;
use App\Models\Order;
use App\Models\Rental;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class FinancialService
{
    /**
     * Create invoice from order
     */
    public function createInvoiceFromOrder(Order $order, array $invoiceData = []): Invoice
    {
        DB::beginTransaction();
        
        try {
            $invoice = Invoice::create([
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'order_id' => $order->id,
                'customer_id' => $order->customer_id,
                'location_id' => $order->location_id,
                'invoice_type' => 'order',
                'status' => 'pending',
                'subtotal' => $order->total_amount,
                'tax_rate' => $invoiceData['tax_rate'] ?? 0,
                'tax_amount' => $order->tax_amount,
                'discount_amount' => $order->discount_amount,
                'total_amount' => $order->final_amount,
                'outstanding_amount' => $order->final_amount,
                'currency' => $invoiceData['currency'] ?? 'USD',
                'payment_terms' => $invoiceData['payment_terms'] ?? 'net_30',
                'due_date' => $this->calculateDueDate($invoiceData['payment_terms'] ?? 'net_30'),
                'issued_at' => now(),
                'billing_address' => $this->formatAddress($order->customer, 'billing'),
                'shipping_address' => $this->formatAddress($order->customer, 'shipping'),
                'notes' => $invoiceData['notes'] ?? null,
            ]);

            // Create invoice items from order items
            foreach ($order->items as $orderItem) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => 'OrderItem',
                    'item_id' => $orderItem->id,
                    'description' => $orderItem->gasType->name . ' - ' . $orderItem->quantity . ' units',
                    'quantity' => $orderItem->quantity,
                    'unit_price' => $orderItem->rate,
                    'discount_amount' => $orderItem->discount_amount ?? 0,
                    'tax_rate' => $invoiceData['tax_rate'] ?? 0,
                    'tax_amount' => $orderItem->tax_amount ?? 0,
                    'total_amount' => $orderItem->final_amount,
                ]);
            }

            DB::commit();
            return $invoice->load(['items', 'customer', 'location']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Create invoice from rental
     */
    public function createInvoiceFromRental(Rental $rental, array $invoiceData = []): Invoice
    {
        DB::beginTransaction();
        
        try {
            $invoice = Invoice::create([
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'rental_id' => $rental->id,
                'customer_id' => $rental->customer_id,
                'location_id' => $rental->location_id,
                'invoice_type' => 'rental',
                'status' => 'pending',
                'subtotal' => $rental->total_amount,
                'tax_rate' => $invoiceData['tax_rate'] ?? 0,
                'tax_amount' => $rental->total_amount * (($invoiceData['tax_rate'] ?? 0) / 100),
                'discount_amount' => $invoiceData['discount_amount'] ?? 0,
                'total_amount' => $rental->total_amount + ($rental->total_amount * (($invoiceData['tax_rate'] ?? 0) / 100)),
                'outstanding_amount' => $rental->total_amount + ($rental->total_amount * (($invoiceData['tax_rate'] ?? 0) / 100)),
                'currency' => $invoiceData['currency'] ?? 'USD',
                'payment_terms' => $invoiceData['payment_terms'] ?? 'net_30',
                'due_date' => $this->calculateDueDate($invoiceData['payment_terms'] ?? 'net_30'),
                'issued_at' => now(),
                'billing_address' => $this->formatAddress($rental->customer, 'billing'),
                'shipping_address' => $this->formatAddress($rental->customer, 'shipping'),
                'notes' => $invoiceData['notes'] ?? null,
            ]);

            // Create rental invoice item
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'item_type' => 'Rental',
                'item_id' => $rental->id,
                'description' => "Rental: {$rental->gasType->name} ({$rental->getDurationInDays()} days)",
                'quantity' => 1,
                'unit_price' => $rental->total_amount,
                'tax_rate' => $invoiceData['tax_rate'] ?? 0,
                'tax_amount' => $rental->total_amount * (($invoiceData['tax_rate'] ?? 0) / 100),
                'total_amount' => $rental->total_amount + ($rental->total_amount * (($invoiceData['tax_rate'] ?? 0) / 100)),
            ]);

            // Add late fees if applicable
            if ($rental->late_fee > 0) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => 'late_fee',
                    'description' => 'Late Fee',
                    'quantity' => 1,
                    'unit_price' => $rental->late_fee,
                    'total_amount' => $rental->late_fee,
                ]);
            }

            // Add damage fees if applicable
            if ($rental->damage_fee > 0) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => 'damage_fee',
                    'description' => 'Damage Fee',
                    'quantity' => 1,
                    'unit_price' => $rental->damage_fee,
                    'total_amount' => $rental->damage_fee,
                ]);
            }

            DB::commit();
            return $invoice->load(['items', 'customer', 'location']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process payment for invoice
     */
    public function processPayment(Invoice $invoice, array $paymentData): Payment
    {
        DB::beginTransaction();
        
        try {
            $payment = Payment::create([
                'invoice_id' => $invoice->id,
                'customer_id' => $invoice->customer_id,
                'amount' => $paymentData['amount'],
                'payment_method' => $paymentData['payment_method'],
                'payment_reference' => $paymentData['payment_reference'] ?? null,
                'payment_date' => $paymentData['payment_date'] ?? now(),
                'status' => 'completed',
                'notes' => $paymentData['notes'] ?? null,
                'processed_by' => Auth::id(),
            ]);

            // Apply payment to invoice
            $invoice->applyPayment(
                $paymentData['amount'],
                $paymentData['payment_method'],
                $paymentData['payment_reference'] ?? null
            );

            DB::commit();
            return $payment;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Invoice $invoice, array $refundData): Payment
    {
        DB::beginTransaction();
        
        try {
            $refund = Payment::create([
                'invoice_id' => $invoice->id,
                'customer_id' => $invoice->customer_id,
                'amount' => -abs($refundData['amount']),
                'payment_method' => 'refund',
                'payment_reference' => $refundData['reason'] ?? null,
                'payment_date' => now(),
                'status' => 'completed',
                'notes' => $refundData['notes'] ?? null,
                'processed_by' => Auth::id(),
            ]);

            // Apply refund to invoice
            $invoice->processRefund(
                $refundData['amount'],
                $refundData['reason'] ?? null
            );

            DB::commit();
            return $refund;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Get financial statistics
     */
    public function getFinancialStatistics($locationIds = null, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        $invoiceQuery = Invoice::where('created_at', '>=', $startDate);
        $paymentQuery = Payment::where('created_at', '>=', $startDate)->completed();
        
        if ($locationIds) {
            $invoiceQuery->whereIn('location_id', $locationIds);
            $paymentQuery->whereHas('invoice', function ($q) use ($locationIds) {
                $q->whereIn('location_id', $locationIds);
            });
        }

        $invoices = $invoiceQuery->get();
        $payments = $paymentQuery->get();

        return [
            'total_invoices' => $invoices->count(),
            'total_revenue' => $invoices->sum('total_amount'),
            'paid_amount' => $invoices->sum('paid_amount'),
            'outstanding_amount' => $invoices->sum('outstanding_amount'),
            'overdue_amount' => $invoices->filter(fn($inv) => $inv->isOverdue())->sum('outstanding_amount'),
            'average_invoice_value' => $invoices->avg('total_amount'),
            'payment_count' => $payments->count(),
            'refund_amount' => $payments->refunds()->sum('amount'),
            'cash_payments' => $payments->where('payment_method', 'cash')->sum('amount'),
            'card_payments' => $payments->where('payment_method', 'card')->sum('amount'),
        ];
    }

    /**
     * Get aging report
     */
    public function getAgingReport($locationIds = null)
    {
        $query = Invoice::pending();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $invoices = $query->with(['customer'])->get();

        return [
            'current' => $invoices->filter(fn($inv) => !$inv->isOverdue())->sum('outstanding_amount'),
            '1_30_days' => $invoices->filter(fn($inv) => $inv->getDaysOverdue() >= 1 && $inv->getDaysOverdue() <= 30)->sum('outstanding_amount'),
            '31_60_days' => $invoices->filter(fn($inv) => $inv->getDaysOverdue() >= 31 && $inv->getDaysOverdue() <= 60)->sum('outstanding_amount'),
            '61_90_days' => $invoices->filter(fn($inv) => $inv->getDaysOverdue() >= 61 && $inv->getDaysOverdue() <= 90)->sum('outstanding_amount'),
            'over_90_days' => $invoices->filter(fn($inv) => $inv->getDaysOverdue() > 90)->sum('outstanding_amount'),
        ];
    }

    /**
     * Update overdue invoices
     */
    public function updateOverdueInvoices(): int
    {
        $overdueInvoices = Invoice::where('status', 'sent')
                                 ->where('due_date', '<', now())
                                 ->get();

        foreach ($overdueInvoices as $invoice) {
            $invoice->updateOverdueStatus();
        }

        return $overdueInvoices->count();
    }

    /**
     * Calculate due date based on payment terms
     */
    private function calculateDueDate(string $paymentTerms): Carbon
    {
        return match($paymentTerms) {
            'immediate' => now(),
            'net_7' => now()->addDays(7),
            'net_15' => now()->addDays(15),
            'net_30' => now()->addDays(30),
            'net_60' => now()->addDays(60),
            'net_90' => now()->addDays(90),
            default => now()->addDays(30)
        };
    }

    /**
     * Format customer address
     */
    private function formatAddress(Customer $customer, string $type = 'billing'): array
    {
        return [
            'name' => $customer->name,
            'company' => $customer->company_name,
            'address_line_1' => $customer->address,
            'city' => $customer->city,
            'state' => $customer->state,
            'postal_code' => $customer->postal_code,
            'country' => $customer->country ?? 'US',
            'phone' => $customer->phone,
            'email' => $customer->email,
        ];
    }
}
