<?php
/**
 * Check Database Structure
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🔍 Checking Database Structure\n";
echo "=============================\n\n";

try {
    echo "1. Checking rentals table structure:\n";
    echo "====================================\n";
    
    $columns = DB::select('DESCRIBE rentals');
    foreach($columns as $column) {
        echo "   {$column->Field} - {$column->Type}\n";
    }
    
    echo "\n2. Checking for missing columns:\n";
    echo "================================\n";
    
    $requiredColumns = [
        'return_date',
        'expected_return_date', 
        'actual_return_date',
        'late_fee',
        'status'
    ];
    
    $existingColumns = array_map(function($col) {
        return $col->Field;
    }, $columns);
    
    foreach($requiredColumns as $required) {
        if (in_array($required, $existingColumns)) {
            echo "   ✅ $required - EXISTS\n";
        } else {
            echo "   ❌ $required - MISSING\n";
        }
    }
    
    echo "\n3. Checking other important tables:\n";
    echo "==================================\n";
    
    $tables = ['cylinders', 'orders', 'customers', 'locations'];
    foreach($tables as $table) {
        try {
            $count = DB::table($table)->count();
            echo "   ✅ $table - $count records\n";
        } catch(Exception $e) {
            echo "   ❌ $table - ERROR: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
