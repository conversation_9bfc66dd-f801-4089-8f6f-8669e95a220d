<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rentals', function (Blueprint $table) {
            // Add missing columns for comprehensive rental management (avoiding conflicts)
            // Skip rental_number as it already exists
            $table->foreignId('location_id')->nullable()->constrained()->after('customer_id');
            $table->foreignId('gas_type_id')->nullable()->constrained()->after('cylinder_id');
            $table->enum('rental_type', ['daily', 'weekly', 'monthly', 'long_term', 'event'])->default('daily')->after('gas_type_id');
            $table->decimal('weekly_rate', 10, 2)->nullable()->after('daily_rate');
            $table->decimal('monthly_rate', 10, 2)->nullable()->after('weekly_rate');
            $table->decimal('deposit_amount', 10, 2)->default(0)->after('monthly_rate');
            $table->decimal('paid_amount', 10, 2)->default(0)->after('total_amount');
            $table->decimal('outstanding_amount', 10, 2)->default(0)->after('paid_amount');
            $table->decimal('damage_fee', 10, 2)->default(0)->after('late_fee');
            $table->text('damage_description')->nullable()->after('damage_fee');
            $table->enum('billing_cycle', ['daily', 'weekly', 'monthly', 'upfront'])->default('monthly')->after('damage_description');
            $table->date('next_billing_date')->nullable()->after('billing_cycle');
            $table->boolean('auto_renew')->default(false)->after('next_billing_date');
            $table->enum('renewal_period', ['weekly', 'monthly'])->nullable()->after('auto_renew');
            $table->text('terms_conditions')->nullable()->after('renewal_period');
            $table->text('special_instructions')->nullable()->after('terms_conditions');
            $table->text('delivery_address')->nullable()->after('special_instructions');
            $table->timestamp('pickup_scheduled_at')->nullable()->after('delivery_address');
            $table->timestamp('return_scheduled_at')->nullable()->after('pickup_scheduled_at');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->after('return_scheduled_at');
            $table->foreignId('created_by')->nullable()->constrained('users')->after('assigned_to');
            $table->foreignId('updated_by')->nullable()->constrained('users')->after('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rentals', function (Blueprint $table) {
            $table->dropColumn([
                'location_id',
                'gas_type_id',
                'rental_type',
                'weekly_rate',
                'monthly_rate',
                'deposit_amount',
                'paid_amount',
                'outstanding_amount',
                'damage_fee',
                'damage_description',
                'billing_cycle',
                'next_billing_date',
                'auto_renew',
                'renewal_period',
                'terms_conditions',
                'special_instructions',
                'delivery_address',
                'pickup_scheduled_at',
                'return_scheduled_at',
                'assigned_to',
                'created_by',
                'updated_by'
            ]);
        });
    }
};
