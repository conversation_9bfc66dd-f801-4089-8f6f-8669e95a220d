@props([
    'tanks' => []
])

<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 dashboard-card">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        <i class="fas fa-tachometer-alt text-orange-500 mr-2"></i>
        Tank Levels
    </h3>
    
    <div class="space-y-4">
        @forelse($tanks as $tank)
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full mr-3 status-indicator 
                        {{ $tank['level'] > 50 ? 'bg-green-500 status-good' : 
                           ($tank['level'] > 25 ? 'bg-yellow-500 status-warning' : 'bg-red-500 status-critical') }}">
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $tank['name'] }}</span>
                </div>
                
                <div class="text-right">
                    <div class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ $tank['level'] }}%</div>
                    <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 tank-level">
                        <div class="tank-level-fill h-2 rounded-full transition-all duration-1000 ease-in-out
                            {{ $tank['level'] > 50 ? 'bg-green-500' : 
                               ($tank['level'] > 25 ? 'bg-yellow-500' : 'bg-red-500') }}" 
                             style="width: {{ $tank['level'] }}%">
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="text-center py-8">
                <i class="fas fa-tachometer-alt text-gray-400 text-3xl mb-3"></i>
                <p class="text-gray-500 dark:text-gray-400">No tank data available</p>
            </div>
        @endforelse
    </div>
    
    @if(count($tanks) > 0)
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <a href="{{ route('tanks.index') }}" 
               class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                View All Tanks →
            </a>
        </div>
    @endif
</div>

<style>
.tank-level {
    position: relative;
    overflow: hidden;
}

.tank-level-fill {
    position: relative;
}

.tank-level-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0.1) 25%, 
        transparent 25%, 
        transparent 50%, 
        rgba(255, 255, 255, 0.1) 50%, 
        rgba(255, 255, 255, 0.1) 75%, 
        transparent 75%);
    background-size: 20px 20px;
    animation: move 1s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
}

.status-indicator.status-good::before {
    background: rgba(34, 197, 94, 0.3);
}

.status-indicator.status-warning::before {
    background: rgba(251, 191, 36, 0.3);
}

.status-indicator.status-critical::before {
    background: rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}
</style>
