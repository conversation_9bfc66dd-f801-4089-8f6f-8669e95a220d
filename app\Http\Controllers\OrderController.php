<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Customer;
use App\Models\Location;
use App\Models\GasType;
use App\Models\User;
use App\Services\OrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->middleware('auth');
        $this->middleware('permission:view_orders', ['only' => ['index', 'show']];
        $this->middleware('permission:create_orders', ['only' => ['create', 'store']];
        $this->middleware('permission:edit_orders', ['only' => ['edit', 'update']];
        $this->middleware('permission:delete_orders', ['only' => ['destroy']];
        $this->middleware('permission:assign_orders', ['only' => ['assign']];
        $this->middleware('permission:fulfill_orders', ['only' => ['fulfill', 'deliver']];
        $this->orderService = $orderService;
    }

    /**
     * Display orders listing
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $query = Order::with(['customer', 'location', 'assignedTo', 'items.gasType']);

        // Apply location filtering
        $query->whereIn('location_id', $accessibleLocationIds);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('location')) {
            $query->where('location_id', $request->location);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Special filters
        if ($request->filter === 'overdue') {
            $query->overdue();
        } elseif ($request->filter === 'high_priority') {
            $query->highPriority();
        } elseif ($request->filter === 'my_orders') {
            $query->assignedTo($user->id);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(20);

        $locations = $this->getAccessibleLocations($user);
        $staff = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['staff', 'location_manager']);
        })->get();

        // Get statistics
        $statistics = $this->orderService->getOrderStatistics($accessibleLocationIds);

        return view('orders.index', compact('orders', 'locations', 'staff', 'statistics'));
    }

    /**
     * Show order creation form
     */
    public function create()
    {
        $user = Auth::user();
        $customers = Customer::active()->get();
        $locations = $this->getAccessibleLocations($user);
        $gasTypes = GasType::active()->get();

        return view('orders.create', compact('customers', 'locations', 'gasTypes'));
    }

    /**
     * Store new order
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'type' => 'required|in:delivery,pickup,exchange,refill,rental',
            'priority' => 'nullable|in:low,normal,high,urgent',
            'delivery_address' => 'nullable|string|max:500',
            'special_instructions' => 'nullable|string|max:1000',
            'scheduled_at' => 'nullable|date|after:now',
            'payment_method' => 'required|in:cash,card,bank_transfer,credit',
            'items' => 'required|array|min:1',
            'items.*.gas_type_id' => 'required|exists:gas_types,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.rate' => 'required|numeric|min:0',
            'items.*.rental_days' => 'nullable|integer|min:1',
        ]);

        // Check location access
        if (!$user->hasLocationAccess($request->location_id)) {
            abort(403, 'You do not have access to this location.');
        }

        try {
            $order = $this->orderService->createOrder(
                $request, ['only' => ['customer_id', 'location_id', 'type', 'priority', 'delivery_address', 'special_instructions', 'scheduled_at', 'payment_method']],
                $request->items
            );

            return redirect()->route('orders.show', $order)
                           ->with('success', 'Order created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', $e->getMessage());
        }
    }

    /**
     * Display order details
     */
    public function show(Order $order)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($order->location_id)) {
            abort(403, 'You do not have access to this order.');
        }

        $order->load(['customer', 'location', 'assignedTo', 'items.gasType', 'invoice']);

        return view('orders.show', compact('order'));
    }

    /**
     * Assign order to staff member
     */
    public function assign(Request $request, Order $order)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($order->location_id)) {
            abort(403, 'You do not have access to this order.');
        }

        $request->validate([
            'assigned_to' => 'required|exists:users,id',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        try {
            $this->orderService->assignOrder(
                $order,
                $request->assigned_to,
                $request->scheduled_at
            );

            return response()->json([
                'success' => true,
                'message' => 'Order assigned successfully.',
                'order' => $order->fresh(['assignedTo']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mark order as in progress
     */
    public function markInProgress(Order $order)
    {
        $user = Auth::user();

        // Check location access and assignment
        if (!$user->hasLocationAccess($order->location_id) || $order->assigned_to !== $user->id) {
            abort(403, 'You do not have access to this order.');
        }

        if ($order->markInProgress()) {
            return response()->json([
                'success' => true,
                'message' => 'Order marked as in progress.',
                'order' => $order->fresh(),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Order cannot be marked as in progress.',
        ], 400);
    }

    /**
     * Complete order delivery
     */
    public function deliver(Request $request, Order $order)
    {
        $user = Auth::user();

        // Check location access and assignment
        if (!$user->hasLocationAccess($order->location_id) || $order->assigned_to !== $user->id) {
            abort(403, 'You do not have access to this order.');
        }

        $request->validate([
            'delivery_otp' => 'required|string|size:6',
            'delivery_proof' => 'nullable|array',
            'notes' => 'nullable|string|max:1000',
            'payment_received' => 'boolean',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        // Verify OTP
        if (!$order->verifyDeliveryOtp($request->delivery_otp)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid delivery OTP.',
            ], 400);
        }

        try {
            $this->orderService->completeDelivery($order, [
                'delivery_proof' => $request->delivery_proof,
                'notes' => $request->notes,
                'payment_received' => $request->payment_received,
                'payment_reference' => $request->payment_reference,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order delivered successfully.',
                'order' => $order->fresh(['customer', 'location', 'assignedTo']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Cancel order
     */
    public function cancel(Request $request, Order $order)
    {
        $user = Auth::user();

        // Check location access
        if (!$user->hasLocationAccess($order->location_id)) {
            abort(403, 'You do not have access to this order.');
        }

        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        try {
            $this->orderService->cancelOrder($order, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully.',
                'order' => $order->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Generate delivery OTP
     */
    public function generateOtp(Order $order)
    {
        $user = Auth::user();

        // Check location access and assignment
        if (!$user->hasLocationAccess($order->location_id) || $order->assigned_to !== $user->id) {
            abort(403, 'You do not have access to this order.');
        }

        if (!$order->canBeDelivered()) {
            return response()->json([
                'success' => false,
                'message' => 'Order cannot generate OTP in its current status.',
            ], 400);
        }

        $otp = $order->generateDeliveryOtp();

        return response()->json([
            'success' => true,
            'otp' => $otp,
            'message' => 'Delivery OTP generated successfully.',
        ]);
    }

    /**
     * Get order statistics
     */
    public function statistics(Request $request)
    {
        $user = Auth::user();
        $accessibleLocationIds = $this->getAccessibleLocationIds($user);

        $days = $request->get('days', 30);
        $statistics = $this->orderService->getOrderStatistics($accessibleLocationIds, $days);
        $attention = $this->orderService->getOrdersRequiringAttention($accessibleLocationIds);

        return response()->json([
            'statistics' => $statistics,
            'requiring_attention' => [
                'overdue' => $attention['overdue']->count(),
                'high_priority' => $attention['high_priority']->count(),
                'pending_assignment' => $attention['pending_assignment']->count(),
                'payment_overdue' => $attention['payment_overdue']->count(),
            ],
        ]);
    }

    /**
     * Get accessible location IDs for user
     */
    private function getAccessibleLocationIds($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::pluck('id')->toArray();
        }

        $locationIds = $user->locations->pluck('id')->toArray();

        if ($user->hasRole('location_manager')) {
            $managedLocationIds = $user->managedLocations->pluck('id')->toArray();
            $locationIds = array_merge($locationIds, $managedLocationIds);
        }

        return array_unique($locationIds);
    }

    /**
     * Get accessible locations for user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return Location::active()->get();
        }

        $locations = $user->locations()->active()->get();

        if ($user->hasRole('location_manager')) {
            $managedLocations = $user->managedLocations()->active()->get();
            $locations = $locations->merge($managedLocations)->unique('id');
        }

        return $locations;
    }
}
