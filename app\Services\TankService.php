<?php

namespace App\Services;

use App\Models\Tank;
use App\Models\TankReading;
use App\Models\TankRefill;
use App\Models\TankAlert;
use App\Models\TankMaintenance;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TankService
{
    /**
     * Create new tank
     */
    public function createTank(array $tankData): Tank
    {
        DB::beginTransaction();
        
        try {
            $tank = Tank::create([
                'tank_number' => Tank::generateTankNumber(),
                'location_id' => $tankData['location_id'],
                'gas_type_id' => $tankData['gas_type_id'],
                'supplier_id' => $tankData['supplier_id'] ?? null,
                'tank_type' => $tankData['tank_type'],
                'capacity' => $tankData['capacity'],
                'current_level' => $tankData['current_level'] ?? 0,
                'unit_of_measurement' => $tankData['unit_of_measurement'],
                'min_level' => $tankData['min_level'] ?? 0,
                'max_level' => $tankData['max_level'] ?? $tankData['capacity'],
                'reorder_level' => $tankData['reorder_level'],
                'critical_level' => $tankData['critical_level'],
                'status' => $tankData['status'] ?? 'active',
                'installation_date' => $tankData['installation_date'] ?? now(),
                'pressure_rating' => $tankData['pressure_rating'] ?? null,
                'safety_systems' => $tankData['safety_systems'] ?? [],
                'compliance_certificate' => $tankData['compliance_certificate'] ?? null,
                'certificate_expiry' => $tankData['certificate_expiry'] ?? null,
                'notes' => $tankData['notes'] ?? null,
                'sensor_id' => $tankData['sensor_id'] ?? null,
                'auto_refill_enabled' => $tankData['auto_refill_enabled'] ?? false,
                'refill_threshold' => $tankData['refill_threshold'] ?? $tankData['reorder_level'],
            ]);

            // Create initial reading
            if ($tank->current_level > 0) {
                TankReading::create([
                    'tank_id' => $tank->id,
                    'reading_value' => $tank->current_level,
                    'reading_type' => 'level',
                    'unit' => $tank->unit_of_measurement,
                    'source' => 'manual',
                    'recorded_at' => now(),
                    'recorded_by' => Auth::id(),
                    'notes' => 'Initial tank setup',
                ]);
            }

            DB::commit();
            return $tank->load(['location', 'gasType', 'supplier']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Update tank reading
     */
    public function updateTankReading(Tank $tank, array $readingData): TankReading
    {
        DB::beginTransaction();
        
        try {
            // Update tank level
            $tank->updateLevel(
                $readingData['reading_value'],
                $readingData['source'] ?? 'manual'
            );

            // Create reading record
            $reading = TankReading::create([
                'tank_id' => $tank->id,
                'reading_value' => $readingData['reading_value'],
                'reading_type' => $readingData['reading_type'] ?? 'level',
                'unit' => $readingData['unit'] ?? $tank->unit_of_measurement,
                'source' => $readingData['source'] ?? 'manual',
                'recorded_at' => $readingData['recorded_at'] ?? now(),
                'recorded_by' => Auth::id(),
                'notes' => $readingData['notes'] ?? null,
                'temperature' => $readingData['temperature'] ?? null,
                'pressure' => $readingData['pressure'] ?? null,
                'humidity' => $readingData['humidity'] ?? null,
                'sensor_data' => $readingData['sensor_data'] ?? null,
            ]);

            DB::commit();
            return $reading;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Schedule tank refill
     */
    public function scheduleRefill(Tank $tank, array $refillData): TankRefill
    {
        DB::beginTransaction();
        
        try {
            $refill = TankRefill::create([
                'tank_id' => $tank->id,
                'supplier_id' => $refillData['supplier_id'] ?? $tank->supplier_id,
                'scheduled_date' => $refillData['scheduled_date'],
                'requested_quantity' => $refillData['requested_quantity'],
                'unit_price' => $refillData['unit_price'] ?? 0,
                'total_cost' => ($refillData['unit_price'] ?? 0) * $refillData['requested_quantity'],
                'status' => 'scheduled',
                'priority' => $refillData['priority'] ?? ($tank->isCritical() ? 'urgent' : 'normal'),
                'delivery_method' => $refillData['delivery_method'] ?? 'truck',
                'notes' => $refillData['notes'] ?? null,
                'requested_by' => Auth::id(),
            ]);

            // Create alert for scheduled refill
            TankAlert::create([
                'tank_id' => $tank->id,
                'alert_type' => 'refill_scheduled',
                'severity' => 'info',
                'message' => "Refill scheduled for tank {$tank->tank_number} on {$refill->scheduled_date->format('M d, Y')}",
                'triggered_at' => now(),
                'status' => 'active',
            ]);

            DB::commit();
            return $refill->load(['tank', 'supplier']);
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Complete tank refill
     */
    public function completeRefill(TankRefill $refill, array $completionData): bool
    {
        DB::beginTransaction();
        
        try {
            // Mark refill as completed
            $refill->markAsCompleted($completionData);

            // Create completion alert
            TankAlert::create([
                'tank_id' => $refill->tank_id,
                'alert_type' => 'refill_completed',
                'severity' => 'info',
                'message' => "Refill completed for tank {$refill->tank->tank_number}. Delivered: {$refill->delivered_quantity} {$refill->tank->getUnitLabel()}",
                'triggered_at' => now(),
                'status' => 'active',
            ]);

            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process automatic refill scheduling
     */
    public function processAutoRefills(): int
    {
        $tanksNeedingRefill = Tank::needsRefill()
                                 ->where('auto_refill_enabled', true)
                                 ->whereDoesntHave('refills', function ($query) {
                                     $query->whereIn('status', ['scheduled', 'confirmed', 'in_progress']);
                                 })
                                 ->get();

        $scheduledCount = 0;

        foreach ($tanksNeedingRefill as $tank) {
            try {
                $this->scheduleRefill($tank, [
                    'scheduled_date' => now()->addDays(1),
                    'requested_quantity' => $tank->capacity - $tank->current_level,
                    'priority' => $tank->isCritical() ? 'urgent' : 'normal',
                    'notes' => 'Auto-scheduled refill due to low level',
                ]);
                
                $scheduledCount++;
            } catch (\Exception $e) {
                // Log error but continue with other tanks
                \Log::error("Failed to auto-schedule refill for tank {$tank->id}: " . $e->getMessage());
            }
        }

        return $scheduledCount;
    }

    /**
     * Get tank statistics
     */
    public function getTankStatistics($locationIds = null)
    {
        $query = Tank::query();
        
        if ($locationIds) {
            $query->whereIn('location_id', $locationIds);
        }

        $tanks = $query->with(['gasType', 'location'])->get();

        return [
            'total_tanks' => $tanks->count(),
            'active_tanks' => $tanks->where('status', 'active')->count(),
            'critical_tanks' => $tanks->filter(fn($tank) => $tank->isCritical())->count(),
            'tanks_needing_refill' => $tanks->filter(fn($tank) => $tank->needsRefill())->count(),
            'maintenance_due' => $tanks->filter(fn($tank) => $tank->isMaintenanceDue())->count(),
            'total_capacity' => $tanks->sum('capacity'),
            'total_current_level' => $tanks->sum('current_level'),
            'average_fill_percentage' => $tanks->avg(fn($tank) => $tank->getCurrentLevelPercentage()),
            'tanks_by_type' => $tanks->groupBy('tank_type')->map->count(),
            'tanks_by_status' => $tanks->groupBy('status')->map->count(),
            'tanks_by_gas_type' => $tanks->groupBy('gasType.name')->map->count(),
        ];
    }

    /**
     * Get tank alerts summary
     */
    public function getAlertsStatistics($locationIds = null)
    {
        $query = TankAlert::with(['tank']);
        
        if ($locationIds) {
            $query->whereHas('tank', function ($q) use ($locationIds) {
                $q->whereIn('location_id', $locationIds);
            });
        }

        $alerts = $query->get();

        return [
            'total_alerts' => $alerts->count(),
            'active_alerts' => $alerts->where('status', 'active')->count(),
            'critical_alerts' => $alerts->where('severity', 'critical')->count(),
            'emergency_alerts' => $alerts->where('severity', 'emergency')->count(),
            'overdue_alerts' => $alerts->filter(fn($alert) => $alert->isOverdueForAcknowledgment())->count(),
            'alerts_by_type' => $alerts->groupBy('alert_type')->map->count(),
            'alerts_by_severity' => $alerts->groupBy('severity')->map->count(),
            'alerts_by_status' => $alerts->groupBy('status')->map->count(),
        ];
    }

    /**
     * Get refill statistics
     */
    public function getRefillStatistics($locationIds = null, $days = 30)
    {
        $startDate = now()->subDays($days);
        
        $query = TankRefill::with(['tank'])
                          ->where('created_at', '>=', $startDate);
        
        if ($locationIds) {
            $query->whereHas('tank', function ($q) use ($locationIds) {
                $q->whereIn('location_id', $locationIds);
            });
        }

        $refills = $query->get();

        return [
            'total_refills' => $refills->count(),
            'completed_refills' => $refills->where('status', 'completed')->count(),
            'pending_refills' => $refills->whereIn('status', ['scheduled', 'confirmed'])->count(),
            'overdue_refills' => $refills->filter(fn($refill) => $refill->isOverdue())->count(),
            'total_quantity_delivered' => $refills->where('status', 'completed')->sum('delivered_quantity'),
            'total_cost' => $refills->where('status', 'completed')->sum('total_cost'),
            'average_delivery_time' => $this->calculateAverageDeliveryTime($refills->where('status', 'completed')),
            'refills_by_priority' => $refills->groupBy('priority')->map->count(),
            'refills_by_status' => $refills->groupBy('status')->map->count(),
        ];
    }

    /**
     * Update maintenance schedule
     */
    public function updateMaintenanceSchedule(Tank $tank, Carbon $nextMaintenanceDate, string $notes = null): bool
    {
        return $tank->update([
            'next_maintenance_date' => $nextMaintenanceDate,
            'notes' => $notes ?? $tank->notes,
        ]);
    }

    /**
     * Check and create maintenance alerts
     */
    public function checkMaintenanceAlerts(): int
    {
        $tanksNeedingMaintenance = Tank::maintenanceDue()->get();
        $alertsCreated = 0;

        foreach ($tanksNeedingMaintenance as $tank) {
            // Check if alert already exists
            $existingAlert = TankAlert::where('tank_id', $tank->id)
                                    ->where('alert_type', 'maintenance_due')
                                    ->where('status', 'active')
                                    ->first();

            if (!$existingAlert) {
                TankAlert::create([
                    'tank_id' => $tank->id,
                    'alert_type' => 'maintenance_due',
                    'severity' => 'warning',
                    'message' => "Maintenance is due for tank {$tank->tank_number}",
                    'triggered_at' => now(),
                    'status' => 'active',
                ]);
                
                $alertsCreated++;
            }
        }

        return $alertsCreated;
    }

    /**
     * Calculate average delivery time
     */
    private function calculateAverageDeliveryTime($completedRefills): float
    {
        if ($completedRefills->isEmpty()) {
            return 0;
        }

        $totalHours = $completedRefills->sum(function ($refill) {
            return $refill->scheduled_date->diffInHours($refill->completed_date);
        });

        return $totalHours / $completedRefills->count();
    }
}
