<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get locations assigned to this user
     */
    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'location_users')
                    ->withTimestamps()
                    ->withPivot('assigned_at');
    }

    /**
     * Get locations managed by this user
     */
    public function managedLocations(): HasMany
    {
        return $this->hasMany(Location::class, 'manager_id');
    }

    /**
     * Get orders assigned to this user
     */
    public function assignedOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'assigned_to');
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for users with specific role
     */
    public function scopeWithRole($query, $role)
    {
        return $query->role($role);
    }

    /**
     * Check if user has access to specific location
     */
    public function hasLocationAccess($locationId): bool
    {
        // Super Admin and Admin have access to all locations
        if ($this->hasAnyRole(['super_admin', 'admin'])) {
            return true;
        }

        // Check if user is assigned to this location
        return $this->locations()->where('location_id', $locationId)->exists() ||
               $this->managedLocations()->where('id', $locationId)->exists();
    }

    /**
     * Get user's primary role
     */
    public function getPrimaryRole(): string
    {
        return $this->roles->first()?->name ?? 'staff';
    }

    /**
     * Get user's role label
     */
    public function getRoleLabel(): string
    {
        $role = $this->getPrimaryRole();

        return match($role) {
            'super_admin' => 'Super Admin',
            'admin' => 'Admin',
            'auditor' => 'Auditor',
            'location_manager' => 'Location Manager',
            'staff' => 'Staff',
            'customer' => 'Customer',
            default => ucfirst(str_replace('_', ' ', $role))
        };
    }

    /**
     * Check if user is a customer
     */
    public function isCustomer(): bool
    {
        return $this->hasRole('customer');
    }

    /**
     * Check if user is staff or higher
     */
    public function isStaff(): bool
    {
        return $this->hasAnyRole(['staff', 'location_manager', 'admin', 'super_admin']);
    }

    /**
     * Check if user can manage locations
     */
    public function canManageLocations(): bool
    {
        return $this->hasAnyRole(['admin', 'super_admin']);
    }

    /**
     * Check if user can view reports
     */
    public function canViewReports(): bool
    {
        return $this->hasAnyRole(['auditor', 'location_manager', 'admin', 'super_admin']);
    }
}
