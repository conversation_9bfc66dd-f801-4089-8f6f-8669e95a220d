<?php
/**
 * Quick-deploy script for production setup via browser.
 * Upload this to your project root and visit /deploy.php in your browser.
 * After successful run, DELETE this file for security.
 */
set_time_limit(0);
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>Starting Laravel Production Setup...</h2>";

// Load framework
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';

// Resolve Artisan console kernel
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

// Define commands to run
$commands = [
    'key:generate --force',
    'config:cache',
    'route:cache',
    'view:cache',
    'storage:link',
    'migrate --force'
];

foreach ($commands as $cmd) {
    echo "<p><strong>Running:</strong> php artisan {$cmd}</p>";
    $status = $kernel->call($cmd);
    echo '<pre>' . htmlspecialchars($kernel->output()) . '</pre>';
    if ($status !== 0) {
        echo "<p style='color:red;'>Command `{$cmd}` failed with status {$status}. Aborting.</p>";
        exit(1);
    }
}

echo "<h3 style='color:green;'>Production setup completed successfully.</h3>";

echo "<p>IMPORTANT: For security, DELETE deploy.php from your server now.</p>";
exit(0);
