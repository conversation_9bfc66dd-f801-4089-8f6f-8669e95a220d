<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Symfony\Component\HttpFoundation\Response;

class CheckInstallation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if installation is complete
        if (!File::exists(storage_path('installed'))) {
            // Allow installation routes
            if ($request->is('install*')) {
                return $next($request);
            }

            // Redirect to installation for all other routes
            return redirect()->route('install.index');
        }

        // If installation is complete, don't allow access to installation routes
        if ($request->is('install*')) {
            return redirect('/')->with('error', 'GCMS is already installed.');
        }

        return $next($request);
    }
}
