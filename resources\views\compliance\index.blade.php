<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Compliance & Audit Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                <button onclick="generateComplianceReport()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    📊 Compliance Report
                </button>
                <button onclick="generateAuditReport()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    🔍 Audit Report
                </button>
                <button onclick="openExportModal()" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    📤 Export
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Compliance Overview -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Compliance Overview
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="text-center">
                            <div class="relative w-24 h-24 mx-auto mb-2">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $complianceData['status']['overall_score'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-green-600">{{ number_format($complianceData['status']['overall_score'], 1) }}%</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Compliance</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="relative w-24 h-24 mx-auto mb-2">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-blue-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $complianceData['status']['safety_compliance']['score'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-blue-600">{{ number_format($complianceData['status']['safety_compliance']['score'], 1) }}%</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Safety Compliance</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="relative w-24 h-24 mx-auto mb-2">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-purple-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $complianceData['status']['regulatory_compliance']['score'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-purple-600">{{ number_format($complianceData['status']['regulatory_compliance']['score'], 1) }}%</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Regulatory Compliance</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="relative w-24 h-24 mx-auto mb-2">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-emerald-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $complianceData['status']['environmental_compliance']['score'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-xl font-bold text-emerald-600">{{ number_format($complianceData['status']['environmental_compliance']['score'], 1) }}%</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Environmental Compliance</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Alerts -->
            @if(count($complianceData['alerts']) > 0)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                        <span class="w-3 h-3 bg-red-500 rounded-full mr-2 animate-pulse"></span>
                        Compliance Alerts
                    </h3>
                    
                    <div class="space-y-3">
                        @foreach($complianceData['alerts'] as $alert)
                        <div class="border-l-4 {{ $alert['type'] === 'warning' ? 'border-yellow-500 bg-yellow-50' : 'border-blue-500 bg-blue-50' }} p-4 rounded-r-lg">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-medium {{ $alert['type'] === 'warning' ? 'text-yellow-800' : 'text-blue-800' }}">
                                        {{ $alert['category'] }}
                                    </h4>
                                    <p class="{{ $alert['type'] === 'warning' ? 'text-yellow-700' : 'text-blue-700' }} text-sm mt-1">
                                        {{ $alert['message'] }}
                                    </p>
                                    <p class="{{ $alert['type'] === 'warning' ? 'text-yellow-600' : 'text-blue-600' }} text-xs mt-2">
                                        Due: {{ $alert['due_date'] }}
                                    </p>
                                </div>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $alert['priority'] === 'high' ? 'bg-red-100 text-red-800' : ($alert['priority'] === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') }}">
                                    {{ ucfirst($alert['priority']) }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Compliance Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Cylinder Compliance -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Cylinder Compliance
                        </h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total Cylinders</span>
                                <span class="font-medium">{{ $complianceData['cylinder_compliance']['total_cylinders'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Compliant</span>
                                <span class="font-medium text-green-600">{{ $complianceData['cylinder_compliance']['compliant_cylinders'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Due for Inspection</span>
                                <span class="font-medium text-yellow-600">{{ $complianceData['cylinder_compliance']['due_for_inspection'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Overdue Inspection</span>
                                <span class="font-medium text-red-600">{{ $complianceData['cylinder_compliance']['overdue_inspection'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Compliance Rate</span>
                                <span class="font-medium text-blue-600">{{ number_format($complianceData['cylinder_compliance']['compliance_rate'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tank Compliance -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Tank Compliance
                        </h3>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Total Tanks</span>
                                <span class="font-medium">{{ $complianceData['tank_compliance']['total_tanks'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Compliant</span>
                                <span class="font-medium text-green-600">{{ $complianceData['tank_compliance']['compliant_tanks'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Pressure Test Due</span>
                                <span class="font-medium text-yellow-600">{{ $complianceData['tank_compliance']['pressure_test_due'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Overdue Tests</span>
                                <span class="font-medium text-red-600">{{ $complianceData['tank_compliance']['overdue_pressure_test'] }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Compliance Rate</span>
                                <span class="font-medium text-blue-600">{{ number_format($complianceData['tank_compliance']['compliance_rate'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Regulatory Compliance -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Regulatory Compliance Status
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($complianceData['regulatory_compliance'] as $key => $value)
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                                {{ ucfirst(str_replace('_', ' ', $key)) }}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                @if(is_array($value))
                                    {{ $value['status'] ?? 'N/A' }}
                                    @if(isset($value['expires']))
                                        <br><span class="text-xs">Expires: {{ $value['expires'] }}</span>
                                    @endif
                                @else
                                    {{ $value }}
                                @endif
                            </p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Upcoming Deadlines -->
            @if(count($complianceData['status']['upcoming_deadlines']) > 0)
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Upcoming Deadlines
                    </h3>
                    
                    <div class="space-y-3">
                        @foreach($complianceData['status']['upcoming_deadlines'] as $deadline)
                        <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-gray-100">{{ $deadline['item'] }}</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $deadline['type'] }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $deadline['due_date'] }}</p>
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $deadline['priority'] === 'high' ? 'bg-red-100 text-red-800' : ($deadline['priority'] === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') }}">
                                    {{ ucfirst($deadline['priority']) }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Export Compliance Data</h3>
                <form id="exportForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Report Type</label>
                        <select name="report_type" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="compliance">Compliance Report</option>
                            <option value="audit">Audit Trail Report</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Format</label>
                        <select name="format" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <input type="date" name="start_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <input type="date" name="end_date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeExportModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Export
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Generate compliance report
        async function generateComplianceReport() {
            try {
                const response = await fetch('/compliance/report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                console.log('Compliance Report:', data);
                alert('Compliance report generated successfully!');
            } catch (error) {
                console.error('Failed to generate compliance report:', error);
                alert('Failed to generate compliance report');
            }
        }

        // Generate audit report
        async function generateAuditReport() {
            try {
                const response = await fetch('/compliance/audit-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                console.log('Audit Report:', data);
                alert('Audit report generated successfully!');
            } catch (error) {
                console.error('Failed to generate audit report:', error);
                alert('Failed to generate audit report');
            }
        }

        // Modal functions
        function openExportModal() {
            document.getElementById('exportModal').classList.remove('hidden');
        }

        function closeExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        // Export form handler
        document.getElementById('exportForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                params.append(key, value);
            }
            
            const reportType = formData.get('report_type');
            const url = reportType === 'audit' ? '/compliance/export-audit' : '/compliance/export-compliance';
            
            window.open(`${url}?${params.toString()}`, '_blank');
            closeExportModal();
        });
    </script>
</x-app-layout>
