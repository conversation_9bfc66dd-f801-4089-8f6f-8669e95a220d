<?php
/**
 * Verify Tanks Table Fix
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🔍 Verifying Tanks Table Fix\n";
echo "============================\n\n";

try {
    echo "1. Checking if 'name' column exists:\n";
    echo "====================================\n";
    
    $columns = DB::select('DESCRIBE tanks');
    $columnNames = array_map(function($col) {
        return $col->Field;
    }, $columns);
    
    if (in_array('name', $columnNames)) {
        echo "✅ 'name' column EXISTS!\n\n";
        
        echo "2. Testing the problematic query:\n";
        echo "=================================\n";
        
        try {
            $result = DB::select('SELECT name, current_level, capacity, min_level FROM tanks LIMIT 1');
            echo "✅ Query executed successfully!\n";
            echo "   Result: " . (empty($result) ? "No data (table empty)" : "Data found") . "\n";
        } catch (Exception $e) {
            echo "❌ Query still failing: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ 'name' column still MISSING!\n";
        echo "Available columns: " . implode(', ', $columnNames) . "\n";
    }
    
    echo "\n3. Current table structure:\n";
    echo "===========================\n";
    foreach($columns as $column) {
        $highlight = ($column->Field === 'name') ? '✅ ' : '   ';
        echo "{$highlight}{$column->Field} - {$column->Type}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 Status:\n";
echo "===========\n";
echo "The tanks table should now have the 'name' column and the SQL error should be resolved.\n";
?>
