<?php
/**
 * Fix Controller Middleware Syntax
 */

echo "🔧 Fixing Controller Middleware Syntax\n";
echo "=====================================\n\n";

$controllers = [
    'app/Http/Controllers/CylinderController.php',
    'app/Http/Controllers/CustomerController.php',
    'app/Http/Controllers/OrderController.php',
    'app/Http/Controllers/LocationController.php',
    'app/Http/Controllers/RentalController.php',
    'app/Http/Controllers/InvoiceController.php',
    'app/Http/Controllers/PaymentController.php',
    'app/Http/Controllers/TankController.php',
    'app/Http/Controllers/ReportController.php',
    'app/Http/Controllers/WhatsAppController.php',
    'app/Http/Controllers/ComplianceController.php',
    'app/Http/Controllers/SystemController.php',
];

foreach ($controllers as $controllerPath) {
    if (file_exists($controllerPath)) {
        echo "Fixing: $controllerPath\n";
        
        $content = file_get_contents($controllerPath);
        
        // Fix middleware syntax
        $patterns = [
            '/->only\(\[([^\]]+)\]\)/' => ", ['only' => [$1]]",
            '/->except\(\[([^\]]+)\]\)/' => ", ['except' => [$1]]",
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }
        
        file_put_contents($controllerPath, $content);
        echo "✅ Fixed: $controllerPath\n";
    } else {
        echo "⚠️  Not found: $controllerPath\n";
    }
}

echo "\n🎉 Controller middleware syntax fixed!\n";
echo "Now testing routes...\n";
?>
