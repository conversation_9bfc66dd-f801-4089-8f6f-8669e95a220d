<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->json('allocated_cylinders')->nullable()->after('cylinder_ids');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('subtotal');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('discount_amount');
            $table->decimal('final_amount', 10, 2)->default(0)->after('tax_amount');
            $table->enum('status', ['pending', 'allocated', 'fulfilled', 'cancelled'])->default('pending')->after('final_amount');
            $table->text('notes')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn([
                'allocated_cylinders',
                'discount_amount',
                'tax_amount',
                'final_amount',
                'status',
                'notes'
            ]);
        });
    }
};
