<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Rental extends Model
{
    use HasFactory;

    protected $fillable = [
        'rental_number',
        'order_id',
        'customer_id',
        'location_id',
        'cylinder_id',
        'gas_type_id',
        'rental_type',
        'status',
        'start_date',
        'end_date',
        'actual_return_date',
        'daily_rate',
        'weekly_rate',
        'monthly_rate',
        'deposit_amount',
        'total_amount',
        'paid_amount',
        'outstanding_amount',
        'late_fee',
        'damage_fee',
        'billing_cycle',
        'next_billing_date',
        'auto_renew',
        'renewal_period',
        'terms_conditions',
        'special_instructions',
        'delivery_address',
        'pickup_scheduled_at',
        'return_scheduled_at',
        'assigned_to',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'actual_return_date' => 'date',
        'next_billing_date' => 'date',
        'pickup_scheduled_at' => 'datetime',
        'return_scheduled_at' => 'datetime',
        'daily_rate' => 'decimal:2',
        'weekly_rate' => 'decimal:2',
        'monthly_rate' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'outstanding_amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'damage_fee' => 'decimal:2',
        'auto_renew' => 'boolean',
    ];

    /**
     * Rental types
     */
    const RENTAL_TYPES = [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'long_term' => 'Long Term',
        'event' => 'Event',
    ];

    /**
     * Rental statuses
     */
    const STATUSES = [
        'pending' => 'Pending',
        'active' => 'Active',
        'overdue' => 'Overdue',
        'returned' => 'Returned',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'damaged' => 'Damaged',
    ];

    /**
     * Billing cycles
     */
    const BILLING_CYCLES = [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'upfront' => 'Upfront',
    ];

    /**
     * Get the order for this rental
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the customer for this rental
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the location for this rental
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the cylinder for this rental
     */
    public function cylinder(): BelongsTo
    {
        return $this->belongsTo(Cylinder::class);
    }

    /**
     * Get the gas type for this rental
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the assigned staff member
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get rental billing records
     */
    public function billings(): HasMany
    {
        return $this->hasMany(RentalBilling::class);
    }

    /**
     * Get rental extensions
     */
    public function extensions(): HasMany
    {
        return $this->hasMany(RentalExtension::class);
    }

    /**
     * Generate unique rental number
     */
    public static function generateRentalNumber(): string
    {
        $prefix = 'RNT-' . date('Ymd') . '-';
        $lastRental = static::where('rental_number', 'like', $prefix . '%')
                           ->orderBy('id', 'desc')
                           ->first();

        if ($lastRental) {
            $lastNumber = (int) substr($lastRental->rental_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get rental type label
     */
    public function getRentalTypeLabel(): string
    {
        return self::RENTAL_TYPES[$this->rental_type] ?? ucfirst($this->rental_type);
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'active' => ['label' => 'Active', 'color' => 'green'],
            'overdue' => ['label' => 'Overdue', 'color' => 'red'],
            'returned' => ['label' => 'Returned', 'color' => 'blue'],
            'completed' => ['label' => 'Completed', 'color' => 'gray'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'red'],
            'damaged' => ['label' => 'Damaged', 'color' => 'orange'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get billing cycle label
     */
    public function getBillingCycleLabel(): string
    {
        return self::BILLING_CYCLES[$this->billing_cycle] ?? ucfirst($this->billing_cycle);
    }

    /**
     * Calculate rental duration in days
     */
    public function getDurationInDays(): int
    {
        $endDate = $this->actual_return_date ?? $this->end_date ?? now();
        return $this->start_date->diffInDays($endDate) + 1;
    }

    /**
     * Check if rental is overdue
     */
    public function isOverdue(): bool
    {
        if ($this->status === 'returned' || $this->status === 'completed') {
            return false;
        }

        return $this->end_date && $this->end_date->isPast();
    }

    /**
     * Check if rental needs billing
     */
    public function needsBilling(): bool
    {
        if ($this->status !== 'active' || $this->billing_cycle === 'upfront') {
            return false;
        }

        return $this->next_billing_date && $this->next_billing_date->isPast();
    }

    /**
     * Calculate current rental amount
     */
    public function calculateCurrentAmount(): float
    {
        $days = $this->getDurationInDays();

        return match($this->rental_type) {
            'daily' => $days * $this->daily_rate,
            'weekly' => ceil($days / 7) * $this->weekly_rate,
            'monthly' => ceil($days / 30) * $this->monthly_rate,
            default => $days * $this->daily_rate
        };
    }

    /**
     * Calculate late fees
     */
    public function calculateLateFees(): float
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $overdueDays = $this->end_date->diffInDays(now());
        $dailyLateFee = $this->daily_rate * 0.1; // 10% of daily rate as late fee

        return $overdueDays * $dailyLateFee;
    }

    /**
     * Extend rental period
     */
    public function extend($newEndDate, $reason = null): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $oldEndDate = $this->end_date;

        $this->update([
            'end_date' => $newEndDate,
            'next_billing_date' => $this->calculateNextBillingDate($newEndDate),
        ]);

        // Create extension record
        RentalExtension::create([
            'rental_id' => $this->id,
            'old_end_date' => $oldEndDate,
            'new_end_date' => $newEndDate,
            'reason' => $reason,
            'extended_by' => auth()->id(),
        ]);

        return true;
    }

    /**
     * Process rental return
     */
    public function processReturn($returnDate = null, $condition = 'good', $notes = null): bool
    {
        if (!in_array($this->status, ['active', 'overdue'])) {
            return false;
        }

        $returnDate = $returnDate ? Carbon::parse($returnDate) : now();

        // Calculate final amounts
        $this->actual_return_date = $returnDate;
        $this->total_amount = $this->calculateCurrentAmount();

        if ($this->isOverdue()) {
            $this->late_fee = $this->calculateLateFees();
            $this->total_amount += $this->late_fee;
        }

        // Handle damage fees
        if ($condition === 'damaged') {
            $this->damage_fee = $this->deposit_amount * 0.5; // 50% of deposit as damage fee
            $this->total_amount += $this->damage_fee;
            $this->status = 'damaged';
        } else {
            $this->status = 'returned';
        }

        $this->outstanding_amount = $this->total_amount - $this->paid_amount;
        $this->save();

        // Update cylinder status
        if ($this->cylinder) {
            $newStatus = $condition === 'damaged' ? 'damaged' : 'empty';
            $this->cylinder->update(['status' => $newStatus]);
        }

        return true;
    }

    /**
     * Activate rental
     */
    public function activate(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'active',
            'start_date' => now()->toDateString(),
            'next_billing_date' => $this->calculateNextBillingDate(),
        ]);

        // Update cylinder status
        if ($this->cylinder) {
            $this->cylinder->update(['status' => 'in_use']);
        }

        return true;
    }

    /**
     * Cancel rental
     */
    public function cancel($reason = null): bool
    {
        if (in_array($this->status, ['returned', 'completed', 'cancelled'])) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'special_instructions' => $reason,
        ]);

        // Release cylinder
        if ($this->cylinder) {
            $this->cylinder->update(['status' => 'full']);
        }

        return true;
    }

    /**
     * Calculate next billing date
     */
    private function calculateNextBillingDate($fromDate = null): ?Carbon
    {
        if ($this->billing_cycle === 'upfront') {
            return null;
        }

        $baseDate = $fromDate ? Carbon::parse($fromDate) : ($this->start_date ?? now());

        return match($this->billing_cycle) {
            'daily' => $baseDate->addDay(),
            'weekly' => $baseDate->addWeek(),
            'monthly' => $baseDate->addMonth(),
            default => $baseDate->addDay()
        };
    }

    /**
     * Scope for active rentals
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for overdue rentals
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function ($q) {
                        $q->where('status', 'active')
                          ->where('end_date', '<', now());
                    });
    }

    /**
     * Scope for rentals needing billing
     */
    public function scopeNeedsBilling($query)
    {
        return $query->where('status', 'active')
                    ->where('billing_cycle', '!=', 'upfront')
                    ->where('next_billing_date', '<=', now());
    }

    /**
     * Scope for rentals at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for rentals assigned to specific user
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }
}
