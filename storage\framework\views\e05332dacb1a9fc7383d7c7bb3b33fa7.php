<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>GCMS Installation - <?php echo $__env->yieldContent('title'); ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .step-active {
            background: linear-gradient(135deg, #6366f1, #06b6d4);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(99,102,241,0.15);
            transition: all 0.3s ease;
            transform: scale(1.1);
        }
        .step-completed {
            background: linear-gradient(135deg, #22d3ee, #22c55e);
            color: white;
            box-shadow: 0 4px 14px 0 rgba(34,197,94,0.15);
            transition: all 0.3s ease;
        }
        .step-pending {
            background: #f3f4f6;
            color: #6b7280;
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }
        .installation-bg {
            background: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
            min-height: 100vh;
        }
        .glass {
            background: rgba(255,255,255,0.85);
            backdrop-filter: blur(8px);
        }
        .step-grid-item {
            transition: transform 0.2s ease;
        }
        .step-grid-item:hover {
            transform: translateY(-2px);
        }
        @media (max-width: 768px) {
            .step-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        @media (max-width: 640px) {
            .step-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="installation-bg flex flex-col min-h-screen">
        <!-- Header -->
        <header class="w-full py-6 shadow-lg bg-white/80 backdrop-blur-md">
            <div class="max-w-7xl mx-auto flex justify-between items-center px-4">
                <div class="flex items-center gap-3">
                    <span class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-tr from-indigo-500 to-cyan-400 shadow-lg">
                        <i class="fas fa-gas-pump text-white text-2xl"></i>
                    </span>
                    <span class="text-3xl font-extrabold text-gray-800 tracking-tight">GCMS Installation</span>
                </div>
                <span class="text-sm text-gray-500 font-medium">Gas Cylinder Management System v1.0</span>
            </div>
        </header>

        <!-- Progress Steps -->
        <nav class="w-full bg-white/70 shadow-sm py-4">
            <div class="max-w-6xl mx-auto px-4">
                <!-- Current Step Display -->
                <div class="text-center mb-4">
                    <div class="inline-flex items-center gap-3 bg-white/90 rounded-full px-4 md:px-6 py-2 md:py-3 shadow-lg">
                        <div class="w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs md:text-sm font-bold step-active">
                            <?php echo e($currentStep); ?>

                        </div>
                        <div class="flex flex-col md:flex-row md:items-center md:gap-3">
                            <span class="text-sm md:text-lg font-semibold text-gray-800"><?php echo e($steps[$currentStep]); ?></span>
                            <span class="text-xs md:text-sm text-gray-500">Step <?php echo e($currentStep); ?> of <?php echo e(count($steps)); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div class="bg-gradient-to-r from-indigo-500 to-cyan-400 h-2 rounded-full transition-all duration-500"
                         style="width: <?php echo e(($currentStep / count($steps)) * 100); ?>%"></div>
                </div>

                <!-- Step Grid (Responsive) -->
                <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-12 gap-2 md:gap-3">
                    <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stepNumber => $stepName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex flex-col items-center step-grid-item">
                            <div class="w-7 h-7 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs font-bold shadow-sm mb-1
                                <?php if($stepNumber < $currentStep): ?> step-completed
                                <?php elseif($stepNumber == $currentStep): ?> step-active
                                <?php else: ?> step-pending
                                <?php endif; ?>">
                                <?php if($stepNumber < $currentStep): ?>
                                    <i class="fas fa-check text-xs"></i>
                                <?php else: ?>
                                    <?php echo e($stepNumber); ?>

                                <?php endif; ?>
                            </div>
                            <span class="text-xs text-center font-medium text-gray-600 leading-tight max-w-full truncate">
                                <?php echo e(Str::limit($stepName, 12)); ?>

                            </span>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 flex items-center justify-center py-8">
            <div class="w-full max-w-2xl px-6 py-8 glass rounded-2xl shadow-2xl border border-gray-200">
                <div id="alerts-container"></div>
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </main>

        <!-- Footer -->
        <footer class="text-center py-6 mt-auto">
            <p class="text-white text-sm opacity-80 font-medium">
                © <?php echo e(date('Y')); ?> Gas Cylinder Management System. All rights reserved.
            </p>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // CSRF Token Setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Loading state management
        function showLoading(button) {
            const originalText = button.html();
            button.data('original-text', originalText);
            button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Processing...');
            button.prop('disabled', true);
        }

        function hideLoading(button) {
            const originalText = button.data('original-text');
            button.html(originalText);
            button.prop('disabled', false);
        }

        // Alert functions
        function showAlert(message, type = 'success') {
            const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
            const iconClass = type === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400';
            
            const alertHtml = `
                <div class="border-l-4 p-4 mb-4 ${alertClass}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas ${iconClass}"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">${message}</p>
                        </div>
                    </div>
                </div>
            `;
            
            $('#alerts-container').html(alertHtml).show();
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    $('#alerts-container').fadeOut();
                }, 3000);
            }
        }

        // Form validation helper
        function showValidationErrors(errors) {
            let errorHtml = '<div class="border-l-4 border-red-400 bg-red-100 p-4 mb-4"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-circle text-red-400"></i></div><div class="ml-3"><h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3><ul class="mt-2 text-sm text-red-700 list-disc list-inside">';
            
            $.each(errors, function(field, messages) {
                $.each(messages, function(index, message) {
                    errorHtml += '<li>' + message + '</li>';
                });
            });
            
            errorHtml += '</ul></div></div></div>';
            $('#alerts-container').html(errorHtml).show();
        }
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\GCMS\resources\views/installation/layout.blade.php ENDPATH**/ ?>