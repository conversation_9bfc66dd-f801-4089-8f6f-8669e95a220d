<?php
/**
 * GCMS Server Compatibility Checker
 * Check if server meets GCMS requirements
 */

// Basic server information
$phpVersion = PHP_VERSION;
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown';

// Check PHP version
$phpOk = version_compare($phpVersion, '8.1.0', '>=');

// Check required PHP extensions
$requiredExtensions = [
    'bcmath', 'ctype', 'fileinfo', 'json', 'mbstring', 
    'openssl', 'pdo', 'pdo_mysql', 'tokenizer', 'xml', 'gd', 'curl'
];

$extensionStatus = [];
foreach ($requiredExtensions as $ext) {
    $extensionStatus[$ext] = extension_loaded($ext);
}

// Check file permissions
$pathsToCheck = [
    '../storage' => is_writable(__DIR__ . '/../storage'),
    '../bootstrap/cache' => is_writable(__DIR__ . '/../bootstrap/cache'),
];

// Check if files exist
$filesExist = [
    'composer.json' => file_exists(__DIR__ . '/../composer.json'),
    'artisan' => file_exists(__DIR__ . '/../artisan'),
    '.env' => file_exists(__DIR__ . '/../.env'),
    'vendor/autoload.php' => file_exists(__DIR__ . '/../vendor/autoload.php'),
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCMS Server Compatibility Check</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2563eb;
            margin: 0;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #374151;
        }
        .status-ok {
            color: #059669;
            font-weight: bold;
        }
        .status-error {
            color: #dc2626;
            font-weight: bold;
        }
        .status-warning {
            color: #d97706;
            font-weight: bold;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            align-items: center;
        }
        .install-button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
            margin-top: 20px;
        }
        .install-button:hover {
            background: #1d4ed8;
        }
        .install-button.disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .error-box {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 GCMS Server Compatibility Check</h1>
            <p>Checking if your server meets GCMS requirements</p>
        </div>

        <!-- Server Information -->
        <div class="section">
            <h3>📊 Server Information</h3>
            <div class="grid">
                <span>PHP Version:</span>
                <span class="<?= $phpOk ? 'status-ok' : 'status-error' ?>">
                    <?= $phpVersion ?> <?= $phpOk ? '✅' : '❌' ?>
                </span>
                
                <span>Server Software:</span>
                <span><?= htmlspecialchars($serverSoftware) ?></span>
                
                <span>Document Root:</span>
                <span><?= htmlspecialchars($documentRoot) ?></span>
            </div>
        </div>

        <!-- PHP Extensions -->
        <div class="section">
            <h3>🔧 PHP Extensions</h3>
            <div class="grid">
                <?php foreach ($extensionStatus as $ext => $loaded): ?>
                    <span><?= $ext ?>:</span>
                    <span class="<?= $loaded ? 'status-ok' : 'status-error' ?>">
                        <?= $loaded ? 'Loaded ✅' : 'Missing ❌' ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- File Permissions -->
        <div class="section">
            <h3>📁 File Permissions</h3>
            <div class="grid">
                <?php foreach ($pathsToCheck as $path => $writable): ?>
                    <span><?= $path ?>:</span>
                    <span class="<?= $writable ? 'status-ok' : 'status-error' ?>">
                        <?= $writable ? 'Writable ✅' : 'Not Writable ❌' ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Required Files -->
        <div class="section">
            <h3>📄 Required Files</h3>
            <div class="grid">
                <?php foreach ($filesExist as $file => $exists): ?>
                    <span><?= $file ?>:</span>
                    <span class="<?= $exists ? 'status-ok' : 'status-error' ?>">
                        <?= $exists ? 'Found ✅' : 'Missing ❌' ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Overall Status -->
        <div class="section">
            <h3>🎯 Installation Status</h3>
            <?php
            $allExtensionsOk = !in_array(false, $extensionStatus);
            $allPermissionsOk = !in_array(false, $pathsToCheck);
            $allFilesExist = !in_array(false, $filesExist);
            $canInstall = $phpOk && $allExtensionsOk && $allPermissionsOk && $allFilesExist;
            ?>
            
            <?php if ($canInstall): ?>
                <div class="info-box">
                    <strong>🎉 Server Ready!</strong><br>
                    Your server meets all GCMS requirements. You can proceed with installation.
                </div>
                <a href="server-install.php" class="install-button">
                    🚀 Start GCMS Installation
                </a>
            <?php else: ?>
                <div class="error-box">
                    <strong>⚠️ Requirements Not Met</strong><br>
                    Please fix the issues above before installing GCMS.
                </div>
                
                <?php if (!$phpOk): ?>
                    <div class="error-box">
                        <strong>PHP Version Issue:</strong><br>
                        GCMS requires PHP 8.1 or higher. Your server has PHP <?= $phpVersion ?>.
                        Contact your hosting provider to upgrade PHP.
                    </div>
                <?php endif; ?>
                
                <?php if (!$allExtensionsOk): ?>
                    <div class="error-box">
                        <strong>Missing PHP Extensions:</strong><br>
                        Contact your hosting provider to install the missing PHP extensions.
                    </div>
                <?php endif; ?>
                
                <?php if (!$allPermissionsOk): ?>
                    <div class="error-box">
                        <strong>Permission Issues:</strong><br>
                        The storage and bootstrap/cache directories must be writable.
                        Contact your hosting provider to fix file permissions.
                    </div>
                <?php endif; ?>
                
                <?php if (!$filesExist['vendor/autoload.php']): ?>
                    <div class="error-box">
                        <strong>Dependencies Missing:</strong><br>
                        Laravel dependencies are not installed. Ask your hosting provider to run:<br>
                        <code>composer install --no-dev --optimize-autoloader</code>
                    </div>
                <?php endif; ?>
                
                <a href="#" class="install-button disabled">
                    ❌ Fix Issues First
                </a>
            <?php endif; ?>
        </div>

        <!-- Help Section -->
        <div class="section">
            <h3>💡 Need Help?</h3>
            <p><strong>Contact Your Hosting Provider</strong> if you need help with:</p>
            <ul>
                <li>Upgrading PHP version</li>
                <li>Installing PHP extensions</li>
                <li>Setting file permissions</li>
                <li>Running Composer commands</li>
            </ul>
            
            <p><strong>Common Hosting Commands:</strong></p>
            <ul>
                <li><code>composer install --no-dev --optimize-autoloader</code> - Install dependencies</li>
                <li><code>chmod 755 storage bootstrap/cache</code> - Fix permissions</li>
                <li><code>php -v</code> - Check PHP version</li>
            </ul>
        </div>
    </div>
</body>
</html>
