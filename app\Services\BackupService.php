<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Carbon\Carbon;
use ZipArchive;

class BackupService
{
    protected $backupDisk;
    protected $backupPath;

    public function __construct()
    {
        $this->backupDisk = config('backup.backup.destination.disks')[0] ?? 'local';
        $this->backupPath = config('backup.backup.name', config('app.name'));
    }

    /**
     * Create full system backup
     */
    public function createFullBackup(): array
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupName = "full_backup_{$timestamp}";
        
        try {
            $results = [
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'components' => []
            ];

            // Database backup
            $dbBackup = $this->createDatabaseBackup($backupName);
            $results['components']['database'] = $dbBackup;

            // Files backup
            $filesBackup = $this->createFilesBackup($backupName);
            $results['components']['files'] = $filesBackup;

            // Configuration backup
            $configBackup = $this->createConfigBackup($backupName);
            $results['components']['config'] = $configBackup;

            // Create manifest
            $this->createBackupManifest($backupName, $results);

            // Cleanup old backups
            $this->cleanupOldBackups();

            $results['status'] = 'success';
            $results['total_size'] = $this->calculateBackupSize($backupName);

            Log::info('Full backup completed successfully', $results);

            return $results;

        } catch (\Exception $e) {
            Log::error('Backup failed: ' . $e->getMessage());
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'backup_name' => $backupName,
                'timestamp' => $timestamp
            ];
        }
    }

    /**
     * Create database backup
     */
    public function createDatabaseBackup(string $backupName): array
    {
        try {
            $filename = "database_{$backupName}.sql";
            $filepath = storage_path("app/backups/{$filename}");

            // Create backup directory
            if (!file_exists(dirname($filepath))) {
                mkdir(dirname($filepath), 0755, true);
            }

            // Get database configuration
            $connection = config('database.default');
            $config = config("database.connections.{$connection}");

            // Create mysqldump command
            $command = sprintf(
                'mysqldump --user=%s --password=%s --host=%s --port=%s %s > %s',
                escapeshellarg($config['username']),
                escapeshellarg($config['password']),
                escapeshellarg($config['host']),
                escapeshellarg($config['port']),
                escapeshellarg($config['database']),
                escapeshellarg($filepath)
            );

            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($filepath)) {
                return [
                    'status' => 'success',
                    'filename' => $filename,
                    'size' => filesize($filepath),
                    'path' => $filepath
                ];
            } else {
                throw new \Exception('Database backup failed with return code: ' . $returnCode);
            }

        } catch (\Exception $e) {
            Log::error('Database backup failed: ' . $e->getMessage());
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create files backup
     */
    public function createFilesBackup(string $backupName): array
    {
        try {
            $filename = "files_{$backupName}.zip";
            $filepath = storage_path("app/backups/{$filename}");

            $zip = new ZipArchive();
            if ($zip->open($filepath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
                throw new \Exception('Cannot create zip file');
            }

            // Add storage files
            $this->addDirectoryToZip($zip, storage_path('app'), 'storage/app');
            
            // Add public uploads
            if (file_exists(public_path('uploads'))) {
                $this->addDirectoryToZip($zip, public_path('uploads'), 'public/uploads');
            }

            // Add configuration files
            $this->addDirectoryToZip($zip, base_path('config'), 'config');

            $zip->close();

            return [
                'status' => 'success',
                'filename' => $filename,
                'size' => filesize($filepath),
                'path' => $filepath
            ];

        } catch (\Exception $e) {
            Log::error('Files backup failed: ' . $e->getMessage());
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create configuration backup
     */
    public function createConfigBackup(string $backupName): array
    {
        try {
            $filename = "config_{$backupName}.json";
            $filepath = storage_path("app/backups/{$filename}");

            $config = [
                'app_name' => config('app.name'),
                'app_version' => config('app.version', '1.0.0'),
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_driver' => config('database.default'),
                'cache_driver' => config('cache.default'),
                'queue_driver' => config('queue.default'),
                'mail_driver' => config('mail.default'),
                'backup_timestamp' => now()->toISOString(),
                'environment' => config('app.env'),
            ];

            file_put_contents($filepath, json_encode($config, JSON_PRETTY_PRINT));

            return [
                'status' => 'success',
                'filename' => $filename,
                'size' => filesize($filepath),
                'path' => $filepath
            ];

        } catch (\Exception $e) {
            Log::error('Configuration backup failed: ' . $e->getMessage());
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Restore from backup
     */
    public function restoreFromBackup(string $backupName): array
    {
        try {
            $results = [
                'backup_name' => $backupName,
                'components' => []
            ];

            // Check if backup exists
            if (!$this->backupExists($backupName)) {
                throw new \Exception('Backup not found: ' . $backupName);
            }

            // Restore database
            $dbRestore = $this->restoreDatabase($backupName);
            $results['components']['database'] = $dbRestore;

            // Restore files
            $filesRestore = $this->restoreFiles($backupName);
            $results['components']['files'] = $filesRestore;

            $results['status'] = 'success';

            Log::info('Backup restored successfully', $results);

            return $results;

        } catch (\Exception $e) {
            Log::error('Backup restore failed: ' . $e->getMessage());
            
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'backup_name' => $backupName
            ];
        }
    }

    /**
     * List available backups
     */
    public function listBackups(): array
    {
        $backupPath = storage_path('app/backups');
        
        if (!file_exists($backupPath)) {
            return [];
        }

        $backups = [];
        $files = scandir($backupPath);

        foreach ($files as $file) {
            if (strpos($file, 'manifest_') === 0 && pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                $manifestPath = $backupPath . '/' . $file;
                $manifest = json_decode(file_get_contents($manifestPath), true);
                
                if ($manifest) {
                    $backups[] = [
                        'name' => $manifest['backup_name'],
                        'timestamp' => $manifest['timestamp'],
                        'size' => $manifest['total_size'] ?? 0,
                        'status' => $manifest['status'] ?? 'unknown',
                        'components' => array_keys($manifest['components'] ?? [])
                    ];
                }
            }
        }

        // Sort by timestamp descending
        usort($backups, function ($a, $b) {
            return strcmp($b['timestamp'], $a['timestamp']);
        });

        return $backups;
    }

    /**
     * Delete backup
     */
    public function deleteBackup(string $backupName): bool
    {
        try {
            $backupPath = storage_path('app/backups');
            $files = [
                "database_{$backupName}.sql",
                "files_{$backupName}.zip",
                "config_{$backupName}.json",
                "manifest_{$backupName}.json"
            ];

            foreach ($files as $file) {
                $filepath = $backupPath . '/' . $file;
                if (file_exists($filepath)) {
                    unlink($filepath);
                }
            }

            Log::info('Backup deleted: ' . $backupName);
            return true;

        } catch (\Exception $e) {
            Log::error('Failed to delete backup: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get backup status
     */
    public function getBackupStatus(): array
    {
        $backups = $this->listBackups();
        $lastBackup = !empty($backups) ? $backups[0] : null;

        return [
            'total_backups' => count($backups),
            'last_backup' => $lastBackup,
            'backup_frequency' => config('backup.backup.frequency', 'daily'),
            'retention_period' => config('backup.cleanup.delete_oldest_backups_when_using_more_megabytes_than', 5000),
            'storage_used' => $this->calculateTotalBackupSize(),
            'next_scheduled_backup' => $this->getNextScheduledBackup(),
        ];
    }

    // Helper methods
    protected function addDirectoryToZip(ZipArchive $zip, string $dir, string $zipPath): void
    {
        if (!file_exists($dir)) {
            return;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipPath . '/' . substr($filePath, strlen($dir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    protected function createBackupManifest(string $backupName, array $results): void
    {
        $manifestPath = storage_path("app/backups/manifest_{$backupName}.json");
        file_put_contents($manifestPath, json_encode($results, JSON_PRETTY_PRINT));
    }

    protected function calculateBackupSize(string $backupName): int
    {
        $backupPath = storage_path('app/backups');
        $files = [
            "database_{$backupName}.sql",
            "files_{$backupName}.zip",
            "config_{$backupName}.json"
        ];

        $totalSize = 0;
        foreach ($files as $file) {
            $filepath = $backupPath . '/' . $file;
            if (file_exists($filepath)) {
                $totalSize += filesize($filepath);
            }
        }

        return $totalSize;
    }

    protected function cleanupOldBackups(): void
    {
        $maxBackups = config('backup.cleanup.keep_all_backups_for_days', 7);
        $backups = $this->listBackups();

        if (count($backups) > $maxBackups) {
            $backupsToDelete = array_slice($backups, $maxBackups);
            
            foreach ($backupsToDelete as $backup) {
                $this->deleteBackup($backup['name']);
            }
        }
    }

    protected function backupExists(string $backupName): bool
    {
        $manifestPath = storage_path("app/backups/manifest_{$backupName}.json");
        return file_exists($manifestPath);
    }

    protected function restoreDatabase(string $backupName): array
    {
        // Implementation would restore database from SQL file
        return ['status' => 'success', 'message' => 'Database restore not implemented'];
    }

    protected function restoreFiles(string $backupName): array
    {
        // Implementation would extract and restore files from zip
        return ['status' => 'success', 'message' => 'Files restore not implemented'];
    }

    protected function calculateTotalBackupSize(): int
    {
        $backupPath = storage_path('app/backups');
        if (!file_exists($backupPath)) {
            return 0;
        }

        $totalSize = 0;
        $files = scandir($backupPath);
        
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                $totalSize += filesize($backupPath . '/' . $file);
            }
        }

        return $totalSize;
    }

    protected function getNextScheduledBackup(): string
    {
        // This would calculate based on backup schedule
        return now()->addDay()->format('Y-m-d H:i:s');
    }
}
