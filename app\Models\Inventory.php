<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Inventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'location_id',
        'gas_type_id',
        'full_count',
        'empty_count',
        'damaged_count',
        'maintenance_count',
        'in_use_count',
        'in_transit_count',
        'expired_count',
        'min_stock_level',
        'max_stock_level',
        'reorder_level',
        'last_counted_at',
        'last_counted_by',
    ];

    protected $casts = [
        'last_counted_at' => 'datetime',
    ];

    /**
     * Get the location for this inventory
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the gas type for this inventory
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the user who last counted this inventory
     */
    public function lastCountedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_counted_by');
    }

    /**
     * Get stock movements for this inventory
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'location_id', 'location_id')
                    ->where('gas_type_id', $this->gas_type_id);
    }

    /**
     * Get total cylinder count
     */
    public function getTotalCountAttribute(): int
    {
        return $this->full_count + $this->empty_count + $this->damaged_count +
               $this->maintenance_count + $this->in_use_count + $this->in_transit_count +
               $this->expired_count;
    }

    /**
     * Get available cylinder count (full + empty)
     */
    public function getAvailableCountAttribute(): int
    {
        return $this->full_count + $this->empty_count;
    }

    /**
     * Get unavailable cylinder count
     */
    public function getUnavailableCountAttribute(): int
    {
        return $this->damaged_count + $this->maintenance_count + $this->expired_count;
    }

    /**
     * Check if stock is below reorder level
     */
    public function isLowStock(): bool
    {
        return $this->available_count <= $this->reorder_level;
    }

    /**
     * Check if stock is below minimum level
     */
    public function isCriticalStock(): bool
    {
        return $this->available_count <= $this->min_stock_level;
    }

    /**
     * Check if stock is above maximum level
     */
    public function isOverstock(): bool
    {
        return $this->total_count >= $this->max_stock_level;
    }

    /**
     * Get stock status
     */
    public function getStockStatus(): array
    {
        if ($this->isCriticalStock()) {
            return ['status' => 'critical', 'color' => 'red', 'label' => 'Critical'];
        } elseif ($this->isLowStock()) {
            return ['status' => 'low', 'color' => 'yellow', 'label' => 'Low Stock'];
        } elseif ($this->isOverstock()) {
            return ['status' => 'overstock', 'color' => 'orange', 'label' => 'Overstock'];
        } else {
            return ['status' => 'normal', 'color' => 'green', 'label' => 'Normal'];
        }
    }

    /**
     * Update inventory counts from actual cylinders
     */
    public function syncWithCylinders(): void
    {
        $cylinders = Cylinder::where('location_id', $this->location_id)
                            ->where('gas_type_id', $this->gas_type_id)
                            ->selectRaw('status, count(*) as count')
                            ->groupBy('status')
                            ->pluck('count', 'status');

        $this->update([
            'full_count' => $cylinders->get('full', 0),
            'empty_count' => $cylinders->get('empty', 0),
            'damaged_count' => $cylinders->get('damaged', 0),
            'maintenance_count' => $cylinders->get('maintenance', 0),
            'in_use_count' => $cylinders->get('in_use', 0),
            'in_transit_count' => $cylinders->get('in_transit', 0),
            'expired_count' => $cylinders->get('expired', 0),
            'last_counted_at' => now(),
            'last_counted_by' => auth()->id(),
        ]);
    }

    /**
     * Scope for low stock items
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('(full_count + empty_count) <= reorder_level');
    }

    /**
     * Scope for critical stock items
     */
    public function scopeCriticalStock($query)
    {
        return $query->whereRaw('(full_count + empty_count) <= min_stock_level');
    }

    /**
     * Scope for overstock items
     */
    public function scopeOverstock($query)
    {
        return $query->whereRaw('(full_count + empty_count + damaged_count + maintenance_count + in_use_count + in_transit_count + expired_count) >= max_stock_level');
    }
}
