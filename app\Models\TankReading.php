<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TankReading extends Model
{
    use HasFactory;

    protected $fillable = [
        'tank_id',
        'reading_value',
        'reading_type',
        'unit',
        'source',
        'recorded_at',
        'recorded_by',
        'notes',
        'temperature',
        'pressure',
        'humidity',
        'sensor_data',
    ];

    protected $casts = [
        'reading_value' => 'decimal:2',
        'temperature' => 'decimal:2',
        'pressure' => 'decimal:2',
        'humidity' => 'decimal:2',
        'recorded_at' => 'datetime',
        'sensor_data' => 'array',
    ];

    /**
     * Reading types
     */
    const READING_TYPES = [
        'level' => 'Level',
        'temperature' => 'Temperature',
        'pressure' => 'Pressure',
        'flow_rate' => 'Flow Rate',
        'consumption' => 'Consumption',
        'refill' => 'Refill',
    ];

    /**
     * Reading sources
     */
    const SOURCES = [
        'manual' => 'Manual Entry',
        'sensor' => 'Sensor Reading',
        'automatic' => 'Automatic System',
        'inspection' => 'Inspection',
        'maintenance' => 'Maintenance',
    ];

    /**
     * Get the tank for this reading
     */
    public function tank(): BelongsTo
    {
        return $this->belongsTo(Tank::class);
    }

    /**
     * Get the user who recorded this reading
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Get reading type label
     */
    public function getReadingTypeLabel(): string
    {
        return self::READING_TYPES[$this->reading_type] ?? ucfirst(str_replace('_', ' ', $this->reading_type));
    }

    /**
     * Get source label
     */
    public function getSourceLabel(): string
    {
        return self::SOURCES[$this->source] ?? ucfirst(str_replace('_', ' ', $this->source));
    }

    /**
     * Scope for specific reading type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('reading_type', $type);
    }

    /**
     * Scope for specific tank
     */
    public function scopeForTank($query, $tankId)
    {
        return $query->where('tank_id', $tankId);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('recorded_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent readings
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('recorded_at', '>=', now()->subHours($hours));
    }
}
