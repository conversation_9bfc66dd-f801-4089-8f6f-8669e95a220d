<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'item_type',
        'item_id',
        'description',
        'quantity',
        'unit_price',
        'discount_amount',
        'tax_rate',
        'tax_amount',
        'total_amount',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_rate' => 'decimal:4',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Item types
     */
    const ITEM_TYPES = [
        'product' => 'Product',
        'service' => 'Service',
        'rental' => 'Rental',
        'delivery' => 'Delivery',
        'late_fee' => 'Late Fee',
        'damage_fee' => 'Damage Fee',
        'deposit' => 'Deposit',
    ];

    /**
     * Get the invoice for this item
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the related item (polymorphic)
     */
    public function item()
    {
        if ($this->item_type && $this->item_id) {
            return $this->morphTo('item', 'item_type', 'item_id');
        }
        return null;
    }

    /**
     * Get item type label
     */
    public function getItemTypeLabel(): string
    {
        return self::ITEM_TYPES[$this->item_type] ?? ucfirst(str_replace('_', ' ', $this->item_type));
    }

    /**
     * Calculate subtotal (quantity * unit_price)
     */
    public function getSubtotalAttribute(): float
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * Calculate line total (subtotal - discount + tax)
     */
    public function calculateLineTotal(): float
    {
        $subtotal = $this->subtotal;
        $afterDiscount = $subtotal - $this->discount_amount;
        return $afterDiscount + $this->tax_amount;
    }

    /**
     * Auto-calculate amounts before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($invoiceItem) {
            // Calculate tax amount if tax rate is provided
            if ($invoiceItem->tax_rate && !$invoiceItem->tax_amount) {
                $taxableAmount = $invoiceItem->subtotal - $invoiceItem->discount_amount;
                $invoiceItem->tax_amount = $taxableAmount * ($invoiceItem->tax_rate / 100);
            }

            // Calculate total amount
            if (!$invoiceItem->total_amount) {
                $invoiceItem->total_amount = $invoiceItem->calculateLineTotal();
            }
        });
    }
}
