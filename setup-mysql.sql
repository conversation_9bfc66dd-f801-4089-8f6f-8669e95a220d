-- Gas Cylinder Management System - MySQL Database Setup
-- Run this script to create the database and user for GCMS

-- Create database
CREATE DATABASE IF NOT EXISTS gcms_database 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Create user (optional - you can use root or existing user)
-- CREATE USER IF NOT EXISTS 'gcms_user'@'localhost' IDENTIFIED BY 'secure_password_here';

-- Grant privileges
-- GRANT ALL PRIVILEGES ON gcms_database.* TO 'gcms_user'@'localhost';

-- If using root user, just grant privileges to root
GRANT ALL PRIVILEGES ON gcms_database.* TO 'root'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE gcms_database;

-- Show database info
SELECT 'Database gcms_database created successfully!' as Status;
SHOW DATABASES LIKE 'gcms_database';
