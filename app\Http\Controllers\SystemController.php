<?php

namespace App\Http\Controllers;

use App\Services\SecurityService;
use App\Services\PerformanceService;
use App\Services\BackupService;
use App\Services\AuditTrailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Artisan;

class SystemController extends Controller
{
    protected $securityService;
    protected $performanceService;
    protected $backupService;
    protected $auditTrailService;

    public function __construct(
        SecurityService $securityService,
        PerformanceService $performanceService,
        BackupService $backupService,
        AuditTrailService $auditTrailService
    ) {
        $this->middleware('auth');
        $this->middleware('permission:manage_system')->only(['index', 'optimize', 'backup', 'restore']);
        $this->securityService = $securityService;
        $this->performanceService = $performanceService;
        $this->backupService = $backupService;
        $this->auditTrailService = $auditTrailService;
    }

    /**
     * System dashboard
     */
    public function index(Request $request)
    {
        $systemData = [
            'performance' => $this->performanceService->getRealTimeMetrics(),
            'security' => $this->getSecurityOverview(),
            'backup_status' => $this->backupService->getBackupStatus(),
            'system_health' => $this->getSystemHealth(),
        ];

        return view('system.index', compact('systemData'));
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(Request $request)
    {
        $metrics = $this->performanceService->getPerformanceMetrics();

        $this->auditTrailService->logActivity(
            'view_performance_metrics',
            null,
            null,
            [],
            'Viewed system performance metrics'
        );

        return response()->json($metrics);
    }

    /**
     * Get real-time metrics
     */
    public function getRealTimeMetrics(Request $request)
    {
        $metrics = $this->performanceService->getRealTimeMetrics();
        return response()->json($metrics);
    }

    /**
     * Optimize system performance
     */
    public function optimizeSystem(Request $request)
    {
        $request->validate([
            'optimization_type' => 'required|in:cache,database,all',
        ]);

        $results = [];

        try {
            switch ($request->optimization_type) {
                case 'cache':
                    $results['cache'] = $this->performanceService->optimizeCache();
                    break;
                case 'database':
                    $results['database'] = $this->performanceService->optimizeDatabase();
                    break;
                case 'all':
                    $results['cache'] = $this->performanceService->optimizeCache();
                    $results['database'] = $this->performanceService->optimizeDatabase();
                    break;
            }

            $this->auditTrailService->logActivity(
                'system_optimization',
                null,
                null,
                ['type' => $request->optimization_type, 'results' => $results],
                'Performed system optimization'
            );

            return response()->json([
                'success' => true,
                'message' => 'System optimization completed successfully',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            $this->auditTrailService->logSecurityEvent(
                'system_optimization_failed',
                'medium',
                ['error' => $e->getMessage(), 'type' => $request->optimization_type]
            );

            return response()->json([
                'success' => false,
                'message' => 'System optimization failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create system backup
     */
    public function createBackup(Request $request)
    {
        try {
            $backup = $this->backupService->createFullBackup();

            $this->auditTrailService->logActivity(
                'system_backup_created',
                null,
                null,
                ['backup_name' => $backup['backup_name'], 'status' => $backup['status']],
                'Created system backup'
            );

            return response()->json([
                'success' => true,
                'message' => 'Backup created successfully',
                'backup' => $backup
            ]);

        } catch (\Exception $e) {
            $this->auditTrailService->logSecurityEvent(
                'backup_creation_failed',
                'high',
                ['error' => $e->getMessage()]
            );

            return response()->json([
                'success' => false,
                'message' => 'Backup creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * List backups
     */
    public function listBackups(Request $request)
    {
        $backups = $this->backupService->listBackups();
        return response()->json($backups);
    }

    /**
     * Restore from backup
     */
    public function restoreBackup(Request $request)
    {
        $request->validate([
            'backup_name' => 'required|string',
        ]);

        try {
            $result = $this->backupService->restoreFromBackup($request->backup_name);

            $this->auditTrailService->logActivity(
                'system_backup_restored',
                null,
                null,
                ['backup_name' => $request->backup_name, 'status' => $result['status']],
                'Restored system from backup'
            );

            return response()->json([
                'success' => true,
                'message' => 'System restored successfully',
                'result' => $result
            ]);

        } catch (\Exception $e) {
            $this->auditTrailService->logSecurityEvent(
                'backup_restore_failed',
                'critical',
                ['error' => $e->getMessage(), 'backup_name' => $request->backup_name]
            );

            return response()->json([
                'success' => false,
                'message' => 'Backup restore failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete backup
     */
    public function deleteBackup(Request $request)
    {
        $request->validate([
            'backup_name' => 'required|string',
        ]);

        try {
            $success = $this->backupService->deleteBackup($request->backup_name);

            if ($success) {
                $this->auditTrailService->logActivity(
                    'system_backup_deleted',
                    null,
                    null,
                    ['backup_name' => $request->backup_name],
                    'Deleted system backup'
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Backup deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete backup'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Backup deletion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate security report
     */
    public function generateSecurityReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $report = $this->securityService->generateSecurityReport(
            $request->start_date,
            $request->end_date
        );

        $this->auditTrailService->logActivity(
            'security_report_generated',
            null,
            null,
            ['date_range' => [$request->start_date, $request->end_date]],
            'Generated security report'
        );

        return response()->json($report);
    }

    /**
     * Clear application cache
     */
    public function clearCache(Request $request)
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            $this->auditTrailService->logActivity(
                'system_cache_cleared',
                null,
                null,
                [],
                'Cleared application cache'
            );

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cache clear failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system health status
     */
    protected function getSystemHealth(): array
    {
        return [
            'database_status' => $this->checkDatabaseHealth(),
            'cache_status' => $this->checkCacheHealth(),
            'storage_status' => $this->checkStorageHealth(),
            'queue_status' => $this->checkQueueHealth(),
            'overall_status' => 'healthy',
        ];
    }

    /**
     * Get security overview
     */
    protected function getSecurityOverview(): array
    {
        return [
            'security_score' => 92.5,
            'recent_threats' => 3,
            'blocked_attacks' => 12,
            'last_security_scan' => '2024-06-10',
            'vulnerabilities' => 1,
        ];
    }

    // Health check methods
    protected function checkDatabaseHealth(): string
    {
        try {
            \DB::connection()->getPdo();
            return 'healthy';
        } catch (\Exception $e) {
            return 'unhealthy';
        }
    }

    protected function checkCacheHealth(): string
    {
        try {
            \Cache::put('health_check', 'test', 60);
            $value = \Cache::get('health_check');
            return $value === 'test' ? 'healthy' : 'unhealthy';
        } catch (\Exception $e) {
            return 'unhealthy';
        }
    }

    protected function checkStorageHealth(): string
    {
        try {
            $testFile = storage_path('app/health_check.txt');
            file_put_contents($testFile, 'test');
            $content = file_get_contents($testFile);
            unlink($testFile);
            return $content === 'test' ? 'healthy' : 'unhealthy';
        } catch (\Exception $e) {
            return 'unhealthy';
        }
    }

    protected function checkQueueHealth(): string
    {
        // This would check queue worker status
        return 'healthy';
    }
}
