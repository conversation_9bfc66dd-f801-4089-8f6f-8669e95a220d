<?php
/**
 * Complete Database Reset Script
 * This script completely drops and recreates the database
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🗄️ Complete Database Reset\n";
echo "==========================\n\n";

try {
    // Get database name from config
    $databaseName = env('DB_DATABASE', 'gcms_database');
    
    echo "1. Dropping all tables:\n";
    echo "=======================\n";
    
    // Disable foreign key checks
    DB::statement('SET FOREIGN_KEY_CHECKS=0');
    
    // Get all tables
    $tables = DB::select('SHOW TABLES');
    $tableCount = 0;
    
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        DB::statement("DROP TABLE IF EXISTS `{$tableName}`");
        echo "   ✅ Dropped table: {$tableName}\n";
        $tableCount++;
    }
    
    // Re-enable foreign key checks
    DB::statement('SET FOREIGN_KEY_CHECKS=1');
    
    echo "   📊 Total tables dropped: {$tableCount}\n";
    
    echo "\n2. Verifying database is empty:\n";
    echo "===============================\n";
    
    $remainingTables = DB::select('SHOW TABLES');
    if (empty($remainingTables)) {
        echo "   ✅ Database is completely empty\n";
    } else {
        echo "   ⚠️  Some tables still exist:\n";
        foreach ($remainingTables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "      - {$tableName}\n";
        }
    }
    
    echo "\n3. Removing installation markers:\n";
    echo "=================================\n";
    
    // Remove installation marker file
    $installedFile = storage_path('installed');
    if (file_exists($installedFile)) {
        unlink($installedFile);
        echo "   ✅ Removed installation marker file\n";
    } else {
        echo "   ℹ️  Installation marker file not found\n";
    }
    
    // Clear Laravel caches
    echo "\n4. Clearing Laravel caches:\n";
    echo "===========================\n";
    
    try {
        \Illuminate\Support\Facades\Artisan::call('config:clear');
        echo "   ✅ Configuration cache cleared\n";
    } catch (Exception $e) {
        echo "   ⚠️  Config cache: " . $e->getMessage() . "\n";
    }
    
    try {
        \Illuminate\Support\Facades\Artisan::call('route:clear');
        echo "   ✅ Route cache cleared\n";
    } catch (Exception $e) {
        echo "   ⚠️  Route cache: " . $e->getMessage() . "\n";
    }
    
    try {
        \Illuminate\Support\Facades\Artisan::call('view:clear');
        echo "   ✅ View cache cleared\n";
    } catch (Exception $e) {
        echo "   ⚠️  View cache: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. Database reset summary:\n";
    echo "==========================\n";
    echo "   ✅ All tables dropped\n";
    echo "   ✅ Installation markers removed\n";
    echo "   ✅ Caches cleared\n";
    echo "   ✅ Database ready for fresh installation\n";
    
    echo "\n🎯 Next Steps:\n";
    echo "==============\n";
    echo "   1. Visit: http://localhost:8000/install\n";
    echo "   2. Choose: Quick Setup (dummy data) or Manual Setup\n";
    echo "   3. Follow: Installation wizard\n";
    echo "   4. Login: With your new credentials\n";
    
    echo "\n✅ Database reset completed successfully!\n";
    echo "🚀 Ready for fresh GCMS installation!\n";
    
} catch (Exception $e) {
    echo "❌ Error during database reset: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
