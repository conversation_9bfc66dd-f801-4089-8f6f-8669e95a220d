<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Cylinder Scanner') }}
            </h2>
            <a href="{{ route('cylinders.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Cylinders
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Scanner Interface -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            Scan <PERSON>er QR Code
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Use your device camera to scan a cylinder QR code or enter the code manually
                        </p>
                    </div>

                    <!-- Camera Scanner -->
                    <div id="scanner-container" class="mb-6">
                        <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden" style="height: 300px;">
                            <video id="scanner-video" class="w-full h-full object-cover" autoplay muted playsinline></video>
                            <div id="scanner-overlay" class="absolute inset-0 flex items-center justify-center">
                                <div class="border-2 border-white rounded-lg" style="width: 200px; height: 200px; border-style: dashed;"></div>
                            </div>
                            <div id="scanner-status" class="absolute bottom-4 left-4 right-4 text-center">
                                <button id="start-camera" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    📷 Start Camera
                                </button>
                                <button id="stop-camera" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded ml-2" style="display: none;">
                                    ⏹️ Stop Camera
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Input -->
                    <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                        <h4 class="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Or Enter QR Code Manually
                        </h4>
                        <form id="manual-scan-form" class="flex space-x-4">
                            <div class="flex-1">
                                <input type="text" id="qr-code-input" placeholder="Enter QR Code (e.g., CYL-ABC12345)" 
                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>
                            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                🔍 Scan
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Scan Results -->
            <div id="scan-results" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg" style="display: none;">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Scan Results
                    </h3>
                    <div id="scan-results-content">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Recent Scans -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Recent Scans
                    </h3>
                    <div id="recent-scans" class="space-y-3">
                        <p class="text-gray-500 dark:text-gray-400 text-center py-4">
                            No recent scans
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scanner JavaScript -->
    <script>
        class CylinderScanner {
            constructor() {
                this.video = document.getElementById('scanner-video');
                this.startButton = document.getElementById('start-camera');
                this.stopButton = document.getElementById('stop-camera');
                this.manualForm = document.getElementById('manual-scan-form');
                this.qrInput = document.getElementById('qr-code-input');
                this.resultsDiv = document.getElementById('scan-results');
                this.resultsContent = document.getElementById('scan-results-content');
                this.recentScans = document.getElementById('recent-scans');
                
                this.stream = null;
                this.scanning = false;
                
                this.initializeEventListeners();
                this.loadRecentScans();
            }

            initializeEventListeners() {
                this.startButton.addEventListener('click', () => this.startCamera());
                this.stopButton.addEventListener('click', () => this.stopCamera());
                this.manualForm.addEventListener('submit', (e) => this.handleManualScan(e));
            }

            async startCamera() {
                try {
                    this.stream = await navigator.mediaDevices.getUserMedia({ 
                        video: { 
                            facingMode: 'environment',
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        } 
                    });
                    
                    this.video.srcObject = this.stream;
                    this.startButton.style.display = 'none';
                    this.stopButton.style.display = 'inline-block';
                    this.scanning = true;
                    
                    // Start scanning for QR codes
                    this.scanForQRCode();
                } catch (error) {
                    console.error('Error accessing camera:', error);
                    alert('Unable to access camera. Please check permissions or use manual input.');
                }
            }

            stopCamera() {
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }
                
                this.video.srcObject = null;
                this.startButton.style.display = 'inline-block';
                this.stopButton.style.display = 'none';
                this.scanning = false;
            }

            scanForQRCode() {
                // This is a simplified QR code detection
                // In a real implementation, you would use a library like jsQR or QuaggaJS
                if (!this.scanning) return;
                
                // Simulate QR code detection for demo
                // In reality, you would capture video frames and process them
                setTimeout(() => {
                    if (this.scanning) {
                        this.scanForQRCode();
                    }
                }, 100);
            }

            handleManualScan(e) {
                e.preventDefault();
                const qrCode = this.qrInput.value.trim();
                
                if (!qrCode) {
                    alert('Please enter a QR code');
                    return;
                }
                
                this.performScan(qrCode);
            }

            async performScan(qrCode) {
                try {
                    const response = await fetch('{{ route("cylinders.scan") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ qr_code: qrCode })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.displayScanResult(data.cylinder, data.status_label);
                        this.addToRecentScans(data.cylinder);
                        this.qrInput.value = '';
                    } else {
                        this.displayError(data.message);
                    }
                } catch (error) {
                    console.error('Scan error:', error);
                    this.displayError('Failed to scan cylinder. Please try again.');
                }
            }

            displayScanResult(cylinder, statusLabel) {
                const html = `
                    <div class="border border-green-200 bg-green-50 dark:bg-green-900 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-green-800 dark:text-green-200">
                                ✅ Cylinder Found
                            </h4>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-${statusLabel.color}-100 text-${statusLabel.color}-800">
                                ${statusLabel.label}
                            </span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p><strong>Cylinder ID:</strong> ${cylinder.unique_id}</p>
                                <p><strong>QR Code:</strong> ${cylinder.qr_code}</p>
                                <p><strong>Gas Type:</strong> ${cylinder.gas_type.name} (${cylinder.gas_type.code})</p>
                                <p><strong>Capacity:</strong> ${cylinder.capacity}L</p>
                            </div>
                            <div>
                                <p><strong>Location:</strong> ${cylinder.location.name}</p>
                                <p><strong>Status:</strong> ${statusLabel.label}</p>
                                <p><strong>Last Filled:</strong> ${cylinder.last_filled_at || 'Never'}</p>
                                <p><strong>Expiry Date:</strong> ${cylinder.expiry_date || 'Not set'}</p>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <a href="/cylinders/${cylinder.id}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                View Details
                            </a>
                            <a href="/cylinders/${cylinder.id}/edit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                                Edit
                            </a>
                        </div>
                    </div>
                `;
                
                this.resultsContent.innerHTML = html;
                this.resultsDiv.style.display = 'block';
            }

            displayError(message) {
                const html = `
                    <div class="border border-red-200 bg-red-50 dark:bg-red-900 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                            ❌ Scan Failed
                        </h4>
                        <p class="text-red-700 dark:text-red-300">${message}</p>
                    </div>
                `;
                
                this.resultsContent.innerHTML = html;
                this.resultsDiv.style.display = 'block';
            }

            addToRecentScans(cylinder) {
                const recentScans = this.getRecentScansFromStorage();
                
                // Add new scan to the beginning
                recentScans.unshift({
                    cylinder: cylinder,
                    scanned_at: new Date().toISOString()
                });
                
                // Keep only last 5 scans
                const limitedScans = recentScans.slice(0, 5);
                
                // Save to localStorage
                localStorage.setItem('recent_scans', JSON.stringify(limitedScans));
                
                // Update display
                this.displayRecentScans(limitedScans);
            }

            getRecentScansFromStorage() {
                try {
                    return JSON.parse(localStorage.getItem('recent_scans') || '[]');
                } catch {
                    return [];
                }
            }

            loadRecentScans() {
                const recentScans = this.getRecentScansFromStorage();
                this.displayRecentScans(recentScans);
            }

            displayRecentScans(scans) {
                if (scans.length === 0) {
                    this.recentScans.innerHTML = '<p class="text-gray-500 dark:text-gray-400 text-center py-4">No recent scans</p>';
                    return;
                }

                const html = scans.map(scan => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900 dark:text-gray-100">${scan.cylinder.unique_id}</div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">${scan.cylinder.gas_type.name}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                ${new Date(scan.scanned_at).toLocaleString()}
                            </div>
                            <a href="/cylinders/${scan.cylinder.id}" class="text-blue-600 hover:text-blue-800 text-sm">
                                View
                            </a>
                        </div>
                    </div>
                `).join('');

                this.recentScans.innerHTML = html;
            }
        }

        // Initialize scanner when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new CylinderScanner();
        });
    </script>
</x-app-layout>
