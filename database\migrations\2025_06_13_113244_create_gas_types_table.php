<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gas_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Oxygen, Nitrogen, Argon, etc.
            $table->string('code', 10)->unique(); // O2, N2, AR, etc.
            $table->string('color_code', 7)->nullable(); // Hex color for UI
            $table->text('safety_info')->nullable();
            $table->decimal('base_price', 10, 2)->default(0);
            $table->decimal('rental_rate', 10, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gas_types');
    }
};
