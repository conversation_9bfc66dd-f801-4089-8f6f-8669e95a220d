<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cylinders', function (Blueprint $table) {
            $table->id();
            $table->string('unique_id')->unique(); // Cylinder serial number
            $table->string('qr_code')->unique(); // QR code for scanning
            $table->foreignId('gas_type_id')->constrained();
            $table->foreignId('location_id')->constrained();
            $table->enum('status', ['empty', 'full', 'in_use', 'damaged', 'expired', 'maintenance', 'in_transit']);
            $table->decimal('capacity', 8, 2); // Cylinder capacity in liters
            $table->decimal('tare_weight', 8, 2); // Empty weight in kg
            $table->decimal('current_weight', 8, 2)->nullable(); // Current weight
            $table->timestamp('last_filled_at')->nullable();
            $table->date('expiry_date')->nullable();
            $table->date('last_inspection_date')->nullable();
            $table->date('next_inspection_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['status', 'location_id']);
            $table->index(['gas_type_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cylinders');
    }
};
