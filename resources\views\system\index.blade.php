<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('System Administration') }}
            </h2>
            <div class="flex space-x-2">
                <button onclick="optimizeSystem('cache')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    🚀 Optimize Cache
                </button>
                <button onclick="createBackup()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    💾 Create Backup
                </button>
                <button onclick="clearCache()" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                    🗑️ Clear Cache
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- System Health Overview -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        System Health Overview
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                            <div class="text-3xl mb-2">✅</div>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Database</h4>
                            <p class="text-sm text-green-600 dark:text-green-400">{{ ucfirst($systemData['system_health']['database_status']) }}</p>
                        </div>
                        
                        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <div class="text-3xl mb-2">⚡</div>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">Cache</h4>
                            <p class="text-sm text-blue-600 dark:text-blue-400">{{ ucfirst($systemData['system_health']['cache_status']) }}</p>
                        </div>
                        
                        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg">
                            <div class="text-3xl mb-2">💾</div>
                            <h4 class="font-medium text-purple-800 dark:text-purple-200">Storage</h4>
                            <p class="text-sm text-purple-600 dark:text-purple-400">{{ ucfirst($systemData['system_health']['storage_status']) }}</p>
                        </div>
                        
                        <div class="text-center p-4 bg-emerald-50 dark:bg-emerald-900 rounded-lg">
                            <div class="text-3xl mb-2">🔄</div>
                            <h4 class="font-medium text-emerald-800 dark:text-emerald-200">Queue</h4>
                            <p class="text-sm text-emerald-600 dark:text-emerald-400">{{ ucfirst($systemData['system_health']['queue_status']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Real-time Metrics -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Real-time Performance
                        </h3>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">System Load</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $systemData['performance']['current_load'] * 100 }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ number_format($systemData['performance']['current_load'] * 100, 1) }}%</span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Memory Usage</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $systemData['performance']['memory_usage'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ number_format($systemData['performance']['memory_usage'], 1) }}%</span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Cache Hit Rate</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: {{ $systemData['performance']['cache_hit_rate'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ number_format($systemData['performance']['cache_hit_rate'], 1) }}%</span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Active Connections</span>
                                <span class="text-sm font-medium">{{ $systemData['performance']['active_connections'] }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Avg Response Time</span>
                                <span class="text-sm font-medium">{{ number_format($systemData['performance']['response_time'], 1) }}ms</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Overview -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Security Status
                        </h3>
                        
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Security Score</span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $systemData['security']['security_score'] }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ number_format($systemData['security']['security_score'], 1) }}%</span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Recent Threats</span>
                                <span class="text-sm font-medium text-yellow-600">{{ $systemData['security']['recent_threats'] }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Blocked Attacks</span>
                                <span class="text-sm font-medium text-green-600">{{ $systemData['security']['blocked_attacks'] }}</span>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Vulnerabilities</span>
                                <span class="text-sm font-medium {{ $systemData['security']['vulnerabilities'] > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    {{ $systemData['security']['vulnerabilities'] }}
                                </span>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Last Security Scan</span>
                                <span class="text-sm font-medium">{{ $systemData['security']['last_security_scan'] }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup Status -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Backup Status
                        </h3>
                        <button onclick="openBackupModal()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                            Manage Backups
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Total Backups</h4>
                            <p class="text-2xl font-bold text-blue-600">{{ $systemData['backup_status']['total_backups'] }}</p>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Last Backup</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                @if($systemData['backup_status']['last_backup'])
                                    {{ $systemData['backup_status']['last_backup']['timestamp'] }}
                                @else
                                    No backups found
                                @endif
                            </p>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Storage Used</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ number_format($systemData['backup_status']['storage_used'] / 1024 / 1024, 2) }} MB</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Actions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        System Actions
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button onclick="optimizeSystem('all')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🚀</div>
                            <div class="text-sm">Full Optimization</div>
                        </button>
                        
                        <button onclick="generateSecurityReport()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🔒</div>
                            <div class="text-sm">Security Report</div>
                        </button>
                        
                        <button onclick="viewPerformanceMetrics()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">📊</div>
                            <div class="text-sm">Performance Report</div>
                        </button>
                        
                        <button onclick="openMaintenanceModal()" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg text-center">
                            <div class="text-2xl mb-2">🔧</div>
                            <div class="text-sm">Maintenance Mode</div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Management Modal -->
    <div id="backupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Backup Management</h3>
                
                <div class="mb-4">
                    <button onclick="createBackup()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2">
                        Create New Backup
                    </button>
                    <button onclick="refreshBackupList()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Refresh List
                    </button>
                </div>
                
                <div id="backupList" class="space-y-2">
                    <!-- Backup list will be populated here -->
                </div>
                
                <div class="flex justify-end mt-4">
                    <button onclick="closeBackupModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mt-4">Processing...</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">Please wait while we process your request.</p>
            </div>
        </div>
    </div>

    <script>
        // System optimization
        async function optimizeSystem(type) {
            showLoading();
            
            try {
                const response = await fetch('/system/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ optimization_type: type })
                });

                const data = await response.json();
                hideLoading();
                
                if (data.success) {
                    alert('System optimization completed successfully!');
                    location.reload();
                } else {
                    alert('Optimization failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                console.error('Optimization failed:', error);
                alert('Optimization failed. Please try again.');
            }
        }

        // Create backup
        async function createBackup() {
            showLoading();
            
            try {
                const response = await fetch('/system/backup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();
                
                if (data.success) {
                    alert('Backup created successfully!');
                    refreshBackupList();
                } else {
                    alert('Backup creation failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                console.error('Backup creation failed:', error);
                alert('Backup creation failed. Please try again.');
            }
        }

        // Clear cache
        async function clearCache() {
            showLoading();
            
            try {
                const response = await fetch('/system/clear-cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                hideLoading();
                
                if (data.success) {
                    alert('Cache cleared successfully!');
                } else {
                    alert('Cache clear failed: ' + data.message);
                }
            } catch (error) {
                hideLoading();
                console.error('Cache clear failed:', error);
                alert('Cache clear failed. Please try again.');
            }
        }

        // Modal functions
        function openBackupModal() {
            document.getElementById('backupModal').classList.remove('hidden');
            refreshBackupList();
        }

        function closeBackupModal() {
            document.getElementById('backupModal').classList.add('hidden');
        }

        function openMaintenanceModal() {
            alert('Maintenance mode functionality would be implemented here');
        }

        // Refresh backup list
        async function refreshBackupList() {
            try {
                const response = await fetch('/system/backups');
                const backups = await response.json();
                
                const backupList = document.getElementById('backupList');
                backupList.innerHTML = backups.map(backup => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900 dark:text-gray-100">${backup.name}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">${backup.timestamp} • ${(backup.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                        <div class="space-x-2">
                            <button onclick="restoreBackup('${backup.name}')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                Restore
                            </button>
                            <button onclick="deleteBackup('${backup.name}')" class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm">
                                Delete
                            </button>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Failed to load backup list:', error);
            }
        }

        // Generate security report
        async function generateSecurityReport() {
            try {
                const response = await fetch('/system/security-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                console.log('Security Report:', data);
                alert('Security report generated successfully!');
            } catch (error) {
                console.error('Security report generation failed:', error);
                alert('Security report generation failed.');
            }
        }

        // View performance metrics
        async function viewPerformanceMetrics() {
            try {
                const response = await fetch('/system/performance-metrics');
                const data = await response.json();
                console.log('Performance Metrics:', data);
                alert('Performance metrics retrieved successfully! Check console for details.');
            } catch (error) {
                console.error('Performance metrics retrieval failed:', error);
                alert('Performance metrics retrieval failed.');
            }
        }

        // Helper functions
        function showLoading() {
            document.getElementById('loadingIndicator').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingIndicator').classList.add('hidden');
        }

        // Auto-refresh performance metrics every 30 seconds
        setInterval(async () => {
            try {
                const response = await fetch('/system/real-time-metrics');
                const metrics = await response.json();
                
                // Update the UI with new metrics
                // This would update the progress bars and values
                console.log('Updated metrics:', metrics);
            } catch (error) {
                console.error('Failed to update metrics:', error);
            }
        }, 30000);
    </script>
</x-app-layout>
