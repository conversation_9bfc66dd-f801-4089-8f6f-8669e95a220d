# 🚀 **GCMS Installation Guide**

## 📋 **Complete Installation Process**

The Gas Cylinder Management System (GCMS) includes a comprehensive web-based installation wizard that guides you through the entire setup process.

---

## 🎯 **Installation Overview**

### **12-Step Installation Process:**

1. **Welcome & Overview** - Introduction to GCMS features
2. **Requirements Check** - Verify server requirements
3. **File Permissions** - Check directory permissions
4. **Database Configuration** - Configure MySQL connection
5. **Database Setup** - Run migrations and create tables
6. **Admin Account** - Create system administrator
7. **Company Information** - Set up company details
8. **Location Setup** - Configure business locations
9. **Gas Types Setup** - Define gas types and properties
10. **Tank Configuration** - Set up storage tanks
11. **Final Setup** - Complete system configuration
12. **Installation Complete** - Ready to use!

---

## 🔧 **Pre-Installation Requirements**

### **Server Requirements:**
- **PHP 8.1+** with required extensions
- **MySQL 8.0+** database server
- **Web Server** (Apache/Nginx)
- **Redis** (recommended for caching)
- **Composer** for dependency management

### **PHP Extensions Required:**
- OpenSSL
- PDO & PDO MySQL
- Mbstring
- Tokenizer
- XML
- Ctype
- JSON
- cURL
- GD (for QR code generation)

### **File Permissions:**
- `storage/` directory: **755** (writable)
- `bootstrap/cache/` directory: **755** (writable)
- `.env` file: **644** (writable)

---

## 📁 **File Upload & Setup**

### **1. Upload Files to Server**
```bash
# Upload all GCMS files to your web server
# Ensure the web root points to the 'public' directory
```

### **2. Set File Permissions**
```bash
# Set proper permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chmod 644 .env

# Set ownership (if needed)
chown -R www-data:www-data storage/
chown -R www-data:www-data bootstrap/cache/
```

### **3. Install Dependencies**
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Generate application key
php artisan key:generate
```

---

## 🌐 **Web-Based Installation**

### **Access Installation Wizard**
1. Open your browser
2. Navigate to your domain: `https://yourdomain.com`
3. You'll be automatically redirected to the installation wizard
4. Follow the step-by-step process

### **Installation URL**
```
https://yourdomain.com/install
```

---

## 📊 **Installation Steps Detailed**

### **Step 1: Welcome**
- Overview of GCMS features
- Installation process explanation
- System requirements preview

### **Step 2: Requirements Check**
- PHP version verification
- Extension availability check
- Performance recommendations
- Troubleshooting guide for failed requirements

### **Step 3: File Permissions**
- Directory write permissions check
- File permission verification
- Automatic permission fixing suggestions

### **Step 4: Database Configuration**
- MySQL connection setup
- Database credentials input
- Connection testing
- Configuration saving

### **Step 5: Database Setup**
- Migration execution
- Table creation
- Index optimization
- Seed data preparation

### **Step 6: Admin Account**
- Super administrator creation
- Secure password setup
- Contact information
- Role assignment

### **Step 7: Company Information**
- Business details setup
- Contact information
- Tax and license numbers
- Currency and timezone configuration

### **Step 8: Location Setup**
- Main location configuration
- Address and contact details
- Multi-location support setup

### **Step 9: Gas Types Setup**
- Gas type definitions
- Safety classifications
- Code assignments
- Properties configuration

### **Step 10: Tank Configuration**
- Storage tank setup
- Capacity and level settings
- Location assignments
- Monitoring thresholds

### **Step 11: Final Setup**
- System optimization
- Cache configuration
- Security setup
- Performance tuning

### **Step 12: Installation Complete**
- Success confirmation
- Login credentials
- Next steps guidance
- System access

---

## 🔒 **Security Considerations**

### **During Installation:**
- Use strong database passwords
- Create secure admin credentials
- Ensure HTTPS is enabled
- Verify file permissions

### **Post-Installation:**
- Remove installation files (optional)
- Enable firewall rules
- Configure backup systems
- Set up monitoring

---

## 🛠️ **Troubleshooting**

### **Common Issues:**

#### **1. Requirements Not Met**
```bash
# Install missing PHP extensions
sudo apt-get install php-{extension-name}

# Restart web server
sudo systemctl restart apache2
# or
sudo systemctl restart nginx
```

#### **2. Permission Errors**
```bash
# Fix storage permissions
sudo chmod -R 755 storage/
sudo chown -R www-data:www-data storage/

# Fix cache permissions
sudo chmod -R 755 bootstrap/cache/
sudo chown -R www-data:www-data bootstrap/cache/
```

#### **3. Database Connection Issues**
- Verify MySQL service is running
- Check database credentials
- Ensure database exists
- Verify user permissions

#### **4. Installation Stuck**
```bash
# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Restart installation
rm storage/installed
```

---

## 📋 **Pre-Installation Checklist**

### **Server Setup:**
- [ ] PHP 8.1+ installed
- [ ] MySQL 8.0+ running
- [ ] Web server configured
- [ ] Domain/subdomain pointing to public directory
- [ ] SSL certificate installed (recommended)

### **Database Preparation:**
- [ ] MySQL database created
- [ ] Database user with full privileges
- [ ] Connection tested
- [ ] UTF8MB4 charset configured

### **File Preparation:**
- [ ] All GCMS files uploaded
- [ ] Composer dependencies installed
- [ ] File permissions set correctly
- [ ] .env file exists and writable

### **Information Ready:**
- [ ] Database credentials
- [ ] Admin account details
- [ ] Company information
- [ ] Location details
- [ ] Gas types list
- [ ] Tank specifications

---

## 🎉 **Post-Installation**

### **Immediate Steps:**
1. **Login** with admin credentials
2. **Verify** all modules are working
3. **Configure** additional settings
4. **Import** existing data (if any)
5. **Train** users on the system

### **System Configuration:**
- Set up automated backups
- Configure email settings
- Enable WhatsApp integration
- Set up monitoring alerts
- Configure user roles and permissions

### **Data Setup:**
- Import customer data
- Add cylinder inventory
- Set up rental rates
- Configure invoice templates
- Set up notification templates

---

## 📞 **Support**

### **Installation Support:**
- Check troubleshooting guide
- Verify system requirements
- Review error logs
- Contact technical support

### **Documentation:**
- User manual
- API documentation
- Configuration guide
- Best practices

---

## ✅ **Installation Complete!**

Once installation is complete, you'll have:

✨ **Fully functional GCMS system**  
✨ **Admin access with full permissions**  
✨ **Database with all tables created**  
✨ **Company and location configured**  
✨ **Gas types and tanks set up**  
✨ **Ready for production use**  

**Your Gas Cylinder Management System is now ready to revolutionize your business operations! 🚀**

---

*Installation Guide v1.0*  
*Last Updated: June 15, 2025*
