<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CylinderLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'cylinder_id',
        'action',
        'old_status',
        'new_status',
        'location_id',
        'user_id',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Get the cylinder for this log
     */
    public function cylinder(): BelongsTo
    {
        return $this->belongsTo(Cylinder::class);
    }

    /**
     * Get the location for this log
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the user who performed this action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for specific action
     */
    public function scopeWithAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for specific cylinder
     */
    public function scopeForCylinder($query, $cylinderId)
    {
        return $query->where('cylinder_id', $cylinderId);
    }

    /**
     * Get action label with color
     */
    public function getActionLabel()
    {
        return match($this->action) {
            'filled' => ['label' => 'Filled', 'color' => 'green'],
            'emptied' => ['label' => 'Emptied', 'color' => 'gray'],
            'moved' => ['label' => 'Moved', 'color' => 'blue'],
            'damaged' => ['label' => 'Damaged', 'color' => 'red'],
            'repaired' => ['label' => 'Repaired', 'color' => 'green'],
            'inspected' => ['label' => 'Inspected', 'color' => 'yellow'],
            'delivered' => ['label' => 'Delivered', 'color' => 'purple'],
            'returned' => ['label' => 'Returned', 'color' => 'indigo'],
            'scanned' => ['label' => 'Scanned', 'color' => 'cyan'],
            default => ['label' => ucfirst($this->action), 'color' => 'gray']
        };
    }

    /**
     * Create a log entry
     */
    public static function createLog($cylinderId, $action, $data = [])
    {
        return static::create([
            'cylinder_id' => $cylinderId,
            'action' => $action,
            'old_status' => $data['old_status'] ?? null,
            'new_status' => $data['new_status'] ?? null,
            'location_id' => $data['location_id'] ?? null,
            'user_id' => $data['user_id'] ?? auth()->id(),
            'notes' => $data['notes'] ?? null,
            'metadata' => $data['metadata'] ?? null,
        ]);
    }
}
