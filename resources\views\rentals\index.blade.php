<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Rental Management') }}
            </h2>
            <div class="flex space-x-2">
                @can('view_rental_billing')
                    <a href="{{ route('rentals.billing.index') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        💰 Billing
                    </a>
                @endcan
                @can('create_rentals')
                    <a href="{{ route('rentals.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        📝 New Rental
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Statistics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($statistics['total_rentals']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Rentals</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($statistics['active_rentals']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Active</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($statistics['overdue_rentals']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Overdue</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                            ${{ number_format($statistics['total_revenue'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Revenue</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                            ${{ number_format($statistics['outstanding_amount'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Outstanding</div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="GET" action="{{ route('rentals.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Rental number or customer"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <select name="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Statuses</option>
                                @foreach(['pending', 'active', 'overdue', 'returned', 'completed', 'cancelled', 'damaged'] as $status)
                                    <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                        {{ ucfirst($status) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Type</label>
                            <select name="rental_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Types</option>
                                @foreach(['daily', 'weekly', 'monthly', 'long_term', 'event'] as $type)
                                    <option value="{{ $type }}" {{ request('rental_type') == $type ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('_', ' ', $type)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
                            <select name="location" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">All Locations</option>
                                @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ request('location') == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                            <a href="{{ route('rentals.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('rentals.index', ['filter' => 'overdue']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                        🚨 Overdue Rentals
                    </a>
                    <a href="{{ route('rentals.index', ['filter' => 'needs_billing']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                        💰 Needs Billing
                    </a>
                    <a href="{{ route('rentals.index', ['filter' => 'ending_soon']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                        ⏰ Ending Soon
                    </a>
                    <a href="{{ route('rentals.index', ['filter' => 'my_rentals']) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        👤 My Rentals
                    </a>
                </div>
            </div>

            <!-- Rentals Table -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Rental
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Customer
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Period
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @forelse($rentals as $rental)
                                    @php $statusLabel = $rental->getStatusLabel(); @endphp
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $rental->rental_number }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $rental->getRentalTypeLabel() }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $rental->gasType->name }} ({{ $rental->cylinder->unique_id }})
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ $rental->customer->name }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $rental->customer->phone }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $rental->location->name }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                                {{ $statusLabel['label'] }}
                                            </span>
                                            @if($rental->isOverdue())
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 ml-1">
                                                    Overdue
                                                </span>
                                            @endif
                                            @if($rental->needsBilling())
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 ml-1">
                                                    Billing Due
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ $rental->start_date->format('M d') }} - {{ $rental->end_date->format('M d, Y') }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $rental->getDurationInDays() }} days
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                ${{ number_format($rental->total_amount, 2) }}
                                            </div>
                                            @if($rental->outstanding_amount > 0)
                                                <div class="text-sm text-red-600">
                                                    Outstanding: ${{ number_format($rental->outstanding_amount, 2) }}
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex justify-end space-x-2">
                                                @can('view_rentals')
                                                    <a href="{{ route('rentals.show', $rental) }}" class="text-indigo-600 hover:text-indigo-900">
                                                        View
                                                    </a>
                                                @endcan
                                                @can('manage_rentals')
                                                    @if($rental->status === 'pending')
                                                        <button onclick="activateRental({{ $rental->id }})" class="text-green-600 hover:text-green-900">
                                                            Activate
                                                        </button>
                                                    @endif
                                                    @if(in_array($rental->status, ['active', 'overdue']))
                                                        <button onclick="returnRental({{ $rental->id }})" class="text-blue-600 hover:text-blue-900">
                                                            Return
                                                        </button>
                                                        <button onclick="extendRental({{ $rental->id }})" class="text-purple-600 hover:text-purple-900">
                                                            Extend
                                                        </button>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                            No rentals found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $rentals->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function activateRental(rentalId) {
            if (!confirm('Are you sure you want to activate this rental?')) return;
            
            try {
                const response = await fetch(`/rentals/${rentalId}/activate`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Activation error:', error);
                alert('Failed to activate rental. Please try again.');
            }
        }

        async function returnRental(rentalId) {
            const condition = prompt('Enter cylinder condition (good/damaged/lost):', 'good');
            if (!condition) return;
            
            const notes = prompt('Return notes (optional):');
            
            try {
                const response = await fetch(`/rentals/${rentalId}/return`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        condition: condition,
                        notes: notes
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Return error:', error);
                alert('Failed to process return. Please try again.');
            }
        }

        async function extendRental(rentalId) {
            const newEndDate = prompt('Enter new end date (YYYY-MM-DD):');
            if (!newEndDate) return;
            
            const reason = prompt('Extension reason:');
            
            try {
                const response = await fetch(`/rentals/${rentalId}/extend`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        new_end_date: newEndDate,
                        reason: reason
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Extension error:', error);
                alert('Failed to extend rental. Please try again.');
            }
        }
    </script>
</x-app-layout>
