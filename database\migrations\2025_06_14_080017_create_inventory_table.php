<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->constrained();
            $table->foreignId('gas_type_id')->constrained();
            $table->integer('full_count')->default(0);
            $table->integer('empty_count')->default(0);
            $table->integer('damaged_count')->default(0);
            $table->integer('maintenance_count')->default(0);
            $table->integer('in_use_count')->default(0);
            $table->integer('in_transit_count')->default(0);
            $table->integer('expired_count')->default(0);
            $table->integer('min_stock_level')->default(10);
            $table->integer('max_stock_level')->default(100);
            $table->integer('reorder_level')->default(20);
            $table->timestamp('last_counted_at')->nullable();
            $table->foreignId('last_counted_by')->nullable()->constrained('users');
            $table->timestamps();

            $table->unique(['location_id', 'gas_type_id']);
            $table->index(['location_id', 'gas_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory');
    }
};
