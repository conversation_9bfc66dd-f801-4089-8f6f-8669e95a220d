<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Order extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'order_number',
        'customer_id',
        'location_id',
        'type',
        'status',
        'priority',
        'total_amount',
        'tax_amount',
        'discount_amount',
        'final_amount',
        'assigned_to',
        'assigned_at',
        'scheduled_at',
        'delivered_at',
        'delivery_address',
        'delivery_notes',
        'delivery_otp',
        'delivery_proof',
        'special_instructions',
        'payment_status',
        'payment_method',
        'payment_reference',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'assigned_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'delivered_at' => 'datetime',
        'delivery_proof' => 'array',
    ];

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'assigned_to', 'total_amount'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the customer for this order
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the location for this order
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the assigned staff member
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get all order items
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the invoice for this order
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    /**
     * Get all rentals for this order
     */
    public function rentals(): HasMany
    {
        return $this->hasMany(Rental::class);
    }

    /**
     * Scope for orders with specific status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for orders at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for orders assigned to specific user
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD-' . date('Ymd') . '-';
        $lastOrder = static::where('order_number', 'like', $prefix . '%')
                          ->orderBy('id', 'desc')
                          ->first();

        if ($lastOrder) {
            $lastNumber = (int) substr($lastOrder->order_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel()
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'assigned' => ['label' => 'Assigned', 'color' => 'blue'],
            'in_progress' => ['label' => 'In Progress', 'color' => 'purple'],
            'delivered' => ['label' => 'Delivered', 'color' => 'green'],
            'completed' => ['label' => 'Completed', 'color' => 'green'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'red'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Check if order can be assigned
     */
    public function canBeAssigned(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if order can be delivered
     */
    public function canBeDelivered(): bool
    {
        return in_array($this->status, ['assigned', 'in_progress']);
    }

    /**
     * Generate delivery OTP
     */
    public function generateDeliveryOtp(): string
    {
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $this->delivery_otp = $otp;
        $this->save();

        return $otp;
    }

    /**
     * Verify delivery OTP
     */
    public function verifyDeliveryOtp(string $otp): bool
    {
        return $this->delivery_otp === $otp;
    }

    /**
     * Get order type label
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'delivery' => 'Delivery',
            'pickup' => 'Pickup',
            'exchange' => 'Exchange',
            'refill' => 'Refill',
            'rental' => 'Rental',
            default => ucfirst($this->type)
        };
    }

    /**
     * Get priority label with color
     */
    public function getPriorityLabel(): array
    {
        return match($this->priority) {
            'low' => ['label' => 'Low', 'color' => 'green'],
            'normal' => ['label' => 'Normal', 'color' => 'blue'],
            'high' => ['label' => 'High', 'color' => 'yellow'],
            'urgent' => ['label' => 'Urgent', 'color' => 'red'],
            default => ['label' => 'Normal', 'color' => 'blue']
        };
    }

    /**
     * Get payment status label
     */
    public function getPaymentStatusLabel(): array
    {
        return match($this->payment_status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'partial' => ['label' => 'Partial', 'color' => 'orange'],
            'paid' => ['label' => 'Paid', 'color' => 'green'],
            'overdue' => ['label' => 'Overdue', 'color' => 'red'],
            'refunded' => ['label' => 'Refunded', 'color' => 'purple'],
            default => ['label' => 'Unknown', 'color' => 'gray']
        };
    }

    /**
     * Calculate total items quantity
     */
    public function getTotalQuantity(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Calculate total weight
     */
    public function getTotalWeight(): float
    {
        return $this->items->sum(function ($item) {
            return $item->quantity * ($item->gasType->weight_per_unit ?? 0);
        });
    }

    /**
     * Check if order is overdue
     */
    public function isOverdue(): bool
    {
        if (!$this->scheduled_at) {
            return false;
        }

        return $this->scheduled_at->isPast() && !in_array($this->status, ['delivered', 'completed', 'cancelled']);
    }

    /**
     * Check if order needs attention
     */
    public function needsAttention(): bool
    {
        return $this->isOverdue() ||
               $this->priority === 'urgent' ||
               ($this->payment_status === 'overdue' && $this->status !== 'cancelled');
    }

    /**
     * Assign order to staff member
     */
    public function assignTo($userId, $scheduledAt = null): bool
    {
        if (!$this->canBeAssigned()) {
            return false;
        }

        $this->update([
            'assigned_to' => $userId,
            'assigned_at' => now(),
            'scheduled_at' => $scheduledAt,
            'status' => 'assigned',
        ]);

        return true;
    }

    /**
     * Mark order as in progress
     */
    public function markInProgress(): bool
    {
        if ($this->status !== 'assigned') {
            return false;
        }

        $this->update(['status' => 'in_progress']);
        return true;
    }

    /**
     * Complete delivery
     */
    public function completeDelivery($deliveryProof = null, $notes = null): bool
    {
        if (!$this->canBeDelivered()) {
            return false;
        }

        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'delivery_notes' => $notes,
            'delivery_proof' => $deliveryProof,
        ]);

        // Update cylinder statuses to in_use for delivery orders
        if ($this->type === 'delivery') {
            foreach ($this->items as $item) {
                if ($item->allocated_cylinders) {
                    Cylinder::whereIn('id', $item->allocated_cylinders)
                           ->update(['status' => 'in_use']);
                }
            }
        }

        return true;
    }

    /**
     * Cancel order
     */
    public function cancel($reason = null): bool
    {
        if (in_array($this->status, ['delivered', 'completed', 'cancelled'])) {
            return false;
        }

        // Release allocated cylinders
        foreach ($this->items as $item) {
            if ($item->allocated_cylinders) {
                Cylinder::whereIn('id', $item->allocated_cylinders)
                       ->update(['status' => 'full']);

                $item->update(['allocated_cylinders' => null]);
            }
        }

        $this->update([
            'status' => 'cancelled',
            'delivery_notes' => $reason,
        ]);

        return true;
    }

    /**
     * Scope for pending orders
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for assigned orders
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', 'assigned');
    }

    /**
     * Scope for overdue orders
     */
    public function scopeOverdue($query)
    {
        return $query->where('scheduled_at', '<', now())
                    ->whereNotIn('status', ['delivered', 'completed', 'cancelled']);
    }

    /**
     * Scope for high priority orders
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }
}
