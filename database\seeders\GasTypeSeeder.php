<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GasType;

class GasTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gasTypes = [
            [
                'name' => 'Oxygen',
                'code' => 'O2',
                'color_code' => '#22C55E', // Green
                'safety_info' => 'Oxidizing gas. Keep away from heat, sparks, and flames. Store in well-ventilated area.',
                'base_price' => 25.00,
                'rental_rate' => 5.00,
                'is_active' => true,
            ],
            [
                'name' => 'Nitrogen',
                'code' => 'N2',
                'color_code' => '#3B82F6', // Blue
                'safety_info' => 'Inert gas. Can cause asphyxiation in confined spaces. Ensure adequate ventilation.',
                'base_price' => 20.00,
                'rental_rate' => 4.00,
                'is_active' => true,
            ],
            [
                'name' => 'Argon',
                'code' => 'AR',
                'color_code' => '#8B5CF6', // Purple
                'safety_info' => 'Inert gas. Can cause asphyxiation. Use in well-ventilated areas only.',
                'base_price' => 30.00,
                'rental_rate' => 6.00,
                'is_active' => true,
            ],
            [
                'name' => 'Carbon Dioxide',
                'code' => 'CO2',
                'color_code' => '#EF4444', // Red
                'safety_info' => 'Can cause asphyxiation. Heavier than air. Ensure proper ventilation.',
                'base_price' => 18.00,
                'rental_rate' => 3.50,
                'is_active' => true,
            ],
            [
                'name' => 'Acetylene',
                'code' => 'C2H2',
                'color_code' => '#F59E0B', // Orange
                'safety_info' => 'Highly flammable. Keep away from heat, sparks, and flames. Store upright.',
                'base_price' => 35.00,
                'rental_rate' => 7.00,
                'is_active' => true,
            ],
            [
                'name' => 'Helium',
                'code' => 'HE',
                'color_code' => '#EC4899', // Pink
                'safety_info' => 'Inert gas. Can cause asphyxiation in confined spaces. Non-toxic but displaces oxygen.',
                'base_price' => 45.00,
                'rental_rate' => 8.00,
                'is_active' => true,
            ],
            [
                'name' => 'Propane',
                'code' => 'C3H8',
                'color_code' => '#F97316', // Orange-red
                'safety_info' => 'Highly flammable. Heavier than air. Keep away from ignition sources.',
                'base_price' => 22.00,
                'rental_rate' => 4.50,
                'is_active' => true,
            ],
            [
                'name' => 'Hydrogen',
                'code' => 'H2',
                'color_code' => '#06B6D4', // Cyan
                'safety_info' => 'Extremely flammable. Lighter than air. Keep away from all ignition sources.',
                'base_price' => 40.00,
                'rental_rate' => 8.50,
                'is_active' => true,
            ],
        ];

        foreach ($gasTypes as $gasType) {
            GasType::create($gasType);
        }

        $this->command->info('Gas types created successfully!');
    }
}
