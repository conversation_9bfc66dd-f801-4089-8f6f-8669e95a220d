<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Make order_id nullable and add rental_id
            $table->foreignId('order_id')->nullable()->change();
            $table->foreignId('rental_id')->nullable()->constrained()->after('order_id');
            $table->foreignId('location_id')->constrained()->after('customer_id');
            
            // Add invoice type and enhanced status
            $table->enum('invoice_type', ['order', 'rental', 'service', 'credit', 'debit'])->default('order')->after('invoice_number');
            $table->enum('status', ['draft', 'pending', 'sent', 'paid', 'partial', 'overdue', 'cancelled', 'refunded'])->default('pending')->change();
            
            // Rename and enhance amount fields
            $table->renameColumn('amount', 'subtotal');
            $table->decimal('tax_rate', 5, 4)->default(0)->after('subtotal');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('tax_amount');
            $table->decimal('paid_amount', 10, 2)->default(0)->after('total_amount');
            $table->decimal('outstanding_amount', 10, 2)->default(0)->after('paid_amount');
            
            // Add financial fields
            $table->string('currency', 3)->default('USD')->after('outstanding_amount');
            $table->enum('payment_terms', ['immediate', 'net_7', 'net_15', 'net_30', 'net_60', 'net_90'])->default('net_30')->after('currency');
            $table->timestamp('issued_at')->nullable()->after('payment_terms');
            $table->json('billing_address')->nullable()->after('issued_at');
            $table->json('shipping_address')->nullable()->after('billing_address');
            $table->string('payment_method')->nullable()->after('shipping_address');
            $table->string('payment_reference')->nullable()->after('payment_method');
            $table->decimal('late_fee', 10, 2)->default(0)->after('payment_reference');
            $table->decimal('finance_charge', 10, 2)->default(0)->after('late_fee');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'rental_id',
                'location_id',
                'invoice_type',
                'tax_rate',
                'discount_amount',
                'paid_amount',
                'outstanding_amount',
                'currency',
                'payment_terms',
                'issued_at',
                'billing_address',
                'shipping_address',
                'payment_method',
                'payment_reference',
                'late_fee',
                'finance_charge'
            ]);
            
            $table->renameColumn('subtotal', 'amount');
            $table->enum('status', ['draft', 'sent', 'paid', 'overdue', 'cancelled'])->change();
            $table->foreignId('order_id')->constrained()->change();
        });
    }
};
