<?php
/**
 * GCMS Server Setup Helper
 * Provides detailed instructions for server setup
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCMS Server Setup Help</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2563eb;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #2563eb;
            background: #f8fafc;
        }
        .error-section {
            border-left-color: #dc2626;
            background: #fef2f2;
        }
        .success-section {
            border-left-color: #059669;
            background: #f0fdf4;
        }
        .warning-section {
            border-left-color: #d97706;
            background: #fffbeb;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .file-list {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
        }
        .step {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
        }
        .step h4 {
            color: #2563eb;
            margin-top: 0;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        .checklist li:before {
            content: "❌";
            position: absolute;
            left: 0;
        }
        .checklist li.done:before {
            content: "✅";
        }
        .email-template {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 GCMS Server Setup Help</h1>
            <p>Complete guide to fix server issues and install GCMS</p>
        </div>

        <!-- Current Issues -->
        <div class="section error-section">
            <h2>🚨 Current Issues Detected</h2>
            <ul class="checklist">
                <li>Missing core Laravel files (composer.json, artisan, .env)</li>
                <li>Vendor directory missing (Composer dependencies not installed)</li>
                <li>Storage directory not writable</li>
                <li>Bootstrap/cache directory not writable</li>
            </ul>
        </div>

        <!-- Step 1: Upload Files -->
        <div class="step">
            <h4>📁 Step 1: Upload Missing Files</h4>
            <p>You need to upload these essential files to your server:</p>
            <div class="file-list">
📁 Your Server Root Directory/<br>
├── 📄 composer.json<br>
├── 📄 artisan<br>
├── 📄 .env<br>
├── 📁 app/<br>
├── 📁 bootstrap/<br>
├── 📁 config/<br>
├── 📁 database/<br>
├── 📁 resources/<br>
├── 📁 routes/<br>
├── 📁 storage/<br>
├── 📁 vendor/ (if you have it)<br>
└── 📁 public/
            </div>
            <p><strong>Important:</strong> Make sure you upload ALL Laravel files, not just the public folder.</p>
        </div>

        <!-- Step 2: Create .env File -->
        <div class="step">
            <h4>⚙️ Step 2: Create .env File</h4>
            <p>Create a file named <code>.env</code> in your server root with this content:</p>
            <div class="code-block">
APP_NAME="Gas Cylinder Management System"
APP_ENV=production
APP_KEY=base64:7KWV2QVB/jpZLw1HJXkRMRhKep85Tt3iJ+heWopjMh8=
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_username
DB_PASSWORD=your_database_password

SESSION_DRIVER=database
CACHE_STORE=database
QUEUE_CONNECTION=database
            </div>
            <p><strong>Replace:</strong> yourdomain.com, your_database_name, your_database_username, your_database_password with your actual values.</p>
        </div>

        <!-- Step 3: Contact Hosting Provider -->
        <div class="step">
            <h4>📧 Step 3: Contact Your Hosting Provider</h4>
            <p>Send this message to your hosting provider's support:</p>
            <div class="email-template">
                <strong>Subject:</strong> Laravel Application Setup Request<br><br>
                
                <strong>Message:</strong><br>
                Hello,<br><br>
                
                I need help setting up a Laravel application on my server. Please assist with:<br><br>
                
                <strong>1. FILE PERMISSIONS:</strong><br>
                Please set these directories to be writable (755 or 775):<br>
                - storage/<br>
                - bootstrap/cache/<br><br>
                
                Command: <code>chmod -R 755 storage bootstrap/cache</code><br><br>
                
                <strong>2. COMPOSER DEPENDENCIES:</strong><br>
                Please run this command in my website directory:<br>
                <code>composer install --no-dev --optimize-autoloader</code><br><br>
                
                <strong>3. LARAVEL OPTIMIZATION:</strong><br>
                After Composer install, please run:<br>
                <code>php artisan config:cache</code><br>
                <code>php artisan route:cache</code><br>
                <code>php artisan view:cache</code><br><br>
                
                My website directory: [YOUR_DOMAIN_FOLDER]<br>
                PHP Version Required: 8.1 or higher<br><br>
                
                Thank you for your assistance.
            </div>
        </div>

        <!-- Step 4: Alternative Solutions -->
        <div class="step">
            <h4>🔄 Step 4: Alternative Solutions</h4>
            
            <div class="warning-section">
                <h5>Option A: Upload Vendor Folder Manually</h5>
                <p>If your hosting provider can't run Composer:</p>
                <ol>
                    <li>Run <code>composer install</code> on your local computer</li>
                    <li>Upload the entire <code>vendor/</code> folder to your server</li>
                    <li>This folder will be large (100MB+) but will work</li>
                </ol>
            </div>

            <div class="warning-section">
                <h5>Option B: Use Laravel-Friendly Hosting</h5>
                <p>Consider these hosting providers that support Laravel:</p>
                <ul>
                    <li><strong>DigitalOcean:</strong> Full server control with SSH</li>
                    <li><strong>Linode:</strong> Developer-friendly hosting</li>
                    <li><strong>SiteGround:</strong> Shared hosting with Composer</li>
                    <li><strong>A2 Hosting:</strong> Laravel-optimized shared hosting</li>
                    <li><strong>Cloudways:</strong> Managed Laravel hosting</li>
                </ul>
            </div>
        </div>

        <!-- Step 5: Verification -->
        <div class="step">
            <h4>✅ Step 5: Verify Setup</h4>
            <p>After your hosting provider fixes the issues:</p>
            <ol>
                <li>Visit <code>https://yourdomain.com/check-server.php</code></li>
                <li>Verify all checks show ✅ (green checkmarks)</li>
                <li>If all good, visit <code>https://yourdomain.com/server-install.php</code></li>
                <li>Run the GCMS installation</li>
            </ol>
        </div>

        <!-- Common Commands -->
        <div class="section">
            <h3>🛠️ Common Commands for Hosting Provider</h3>
            <p>Share these commands with your hosting provider:</p>
            <div class="code-block">
# Fix file permissions
chmod -R 755 storage bootstrap/cache

# Install Composer dependencies
composer install --no-dev --optimize-autoloader

# Laravel optimization commands
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Check PHP version
php -v

# Check if Composer is available
composer --version
            </div>
        </div>

        <!-- Success Section -->
        <div class="section success-section">
            <h3>🎉 After Setup Complete</h3>
            <p>Once your hosting provider fixes these issues, you'll be able to:</p>
            <ul>
                <li>✅ Access the GCMS installation wizard</li>
                <li>✅ Choose Quick Setup (with sample data) or Manual Setup</li>
                <li>✅ Complete installation in 2-15 minutes</li>
                <li>✅ Start using your professional GCMS system</li>
            </ul>
        </div>

        <!-- Contact Info -->
        <div class="section">
            <h3>💡 Need More Help?</h3>
            <p><strong>If your hosting provider needs more information:</strong></p>
            <ul>
                <li>Show them this page: <code>https://yourdomain.com/server-setup-help.php</code></li>
                <li>Mention it's a <strong>Laravel 12.18.0</strong> application</li>
                <li>Emphasize <strong>PHP 8.1+</strong> requirement</li>
                <li>Explain that <strong>Composer</strong> is required for Laravel</li>
            </ul>
        </div>
    </div>
</body>
</html>
