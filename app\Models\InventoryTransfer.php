<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InventoryTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'transfer_number',
        'from_location_id',
        'to_location_id',
        'gas_type_id',
        'quantity',
        'status',
        'requested_by',
        'approved_by',
        'shipped_by',
        'received_by',
        'requested_at',
        'approved_at',
        'shipped_at',
        'received_at',
        'reason',
        'notes',
        'cylinder_ids',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'shipped_at' => 'datetime',
        'received_at' => 'datetime',
        'cylinder_ids' => 'array',
    ];

    /**
     * Transfer statuses
     */
    const STATUSES = [
        'pending' => 'Pending Approval',
        'approved' => 'Approved',
        'in_transit' => 'In Transit',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ];

    /**
     * Get the source location
     */
    public function fromLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'from_location_id');
    }

    /**
     * Get the destination location
     */
    public function toLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'to_location_id');
    }

    /**
     * Get the gas type
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the user who requested the transfer
     */
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the user who approved the transfer
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who shipped the transfer
     */
    public function shippedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'shipped_by');
    }

    /**
     * Get the user who received the transfer
     */
    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending Approval', 'color' => 'yellow'],
            'approved' => ['label' => 'Approved', 'color' => 'blue'],
            'in_transit' => ['label' => 'In Transit', 'color' => 'purple'],
            'completed' => ['label' => 'Completed', 'color' => 'green'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'red'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Generate unique transfer number
     */
    public static function generateTransferNumber(): string
    {
        $prefix = 'TRF-' . date('Ymd') . '-';
        $lastTransfer = static::where('transfer_number', 'like', $prefix . '%')
                             ->orderBy('id', 'desc')
                             ->first();

        if ($lastTransfer) {
            $lastNumber = (int) substr($lastTransfer->transfer_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Approve the transfer
     */
    public function approve($userId = null): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $userId ?? auth()->id(),
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * Ship the transfer
     */
    public function ship($userId = null): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $this->update([
            'status' => 'in_transit',
            'shipped_by' => $userId ?? auth()->id(),
            'shipped_at' => now(),
        ]);

        // Create stock movement for source location
        StockMovement::createMovement([
            'location_id' => $this->from_location_id,
            'gas_type_id' => $this->gas_type_id,
            'movement_type' => 'transfer_out',
            'quantity' => $this->quantity,
            'reference_type' => 'InventoryTransfer',
            'reference_id' => $this->id,
            'notes' => "Transfer to {$this->toLocation->name}",
        ]);

        // Update cylinder locations if specific cylinders are transferred
        if ($this->cylinder_ids) {
            Cylinder::whereIn('id', $this->cylinder_ids)
                   ->update(['status' => 'in_transit']);
        }

        return true;
    }

    /**
     * Complete the transfer
     */
    public function complete($userId = null): bool
    {
        if ($this->status !== 'in_transit') {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'received_by' => $userId ?? auth()->id(),
            'received_at' => now(),
        ]);

        // Create stock movement for destination location
        StockMovement::createMovement([
            'location_id' => $this->to_location_id,
            'gas_type_id' => $this->gas_type_id,
            'movement_type' => 'transfer_in',
            'quantity' => $this->quantity,
            'reference_type' => 'InventoryTransfer',
            'reference_id' => $this->id,
            'notes' => "Transfer from {$this->fromLocation->name}",
        ]);

        // Update cylinder locations if specific cylinders are transferred
        if ($this->cylinder_ids) {
            Cylinder::whereIn('id', $this->cylinder_ids)
                   ->update([
                       'location_id' => $this->to_location_id,
                       'status' => 'full', // Assume transferred cylinders are full
                   ]);
        }

        return true;
    }

    /**
     * Cancel the transfer
     */
    public function cancel($userId = null): bool
    {
        if (in_array($this->status, ['completed', 'cancelled'])) {
            return false;
        }

        // If transfer was in transit, reverse the stock movements
        if ($this->status === 'in_transit') {
            // Reverse the out movement
            StockMovement::createMovement([
                'location_id' => $this->from_location_id,
                'gas_type_id' => $this->gas_type_id,
                'movement_type' => 'transfer_in',
                'quantity' => $this->quantity,
                'reference_type' => 'InventoryTransfer',
                'reference_id' => $this->id,
                'notes' => "Transfer cancelled - stock returned",
            ]);

            // Update cylinder status back
            if ($this->cylinder_ids) {
                Cylinder::whereIn('id', $this->cylinder_ids)
                       ->update(['status' => 'full']);
            }
        }

        $this->update([
            'status' => 'cancelled',
        ]);

        return true;
    }

    /**
     * Scope for pending transfers
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for transfers from specific location
     */
    public function scopeFromLocation($query, $locationId)
    {
        return $query->where('from_location_id', $locationId);
    }

    /**
     * Scope for transfers to specific location
     */
    public function scopeToLocation($query, $locationId)
    {
        return $query->where('to_location_id', $locationId);
    }
}
