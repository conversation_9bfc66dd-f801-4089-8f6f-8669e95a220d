@extends('installation.layout')

@section('content')
    <h2 class="text-2xl font-bold text-indigo-700 mb-2 flex items-center gap-2">
        <i class="fas fa-database text-cyan-500"></i> Database Setup
    </h2>
    <p class="text-gray-600 mb-6">Click the button below to run the database migrations.</p>

    <form id="run-migrations-form" class="mb-4">
        @csrf
        <button type="submit" class="w-full px-6 py-2 rounded-lg bg-gradient-to-tr from-indigo-500 to-cyan-400 text-white font-bold shadow-lg hover:scale-105 transition-transform flex items-center justify-center gap-2">
            <i class="fas fa-play"></i> Run Migrations
        </button>
    </form>

    <div id="migration-status" class="mt-3"></div>

    <a href="{{ route('install.admin.account') }}" id="next-step" class="w-full mt-4 inline-block px-6 py-2 rounded-lg bg-gradient-to-tr from-green-500 to-emerald-400 text-white font-bold shadow-lg hover:scale-105 transition-transform text-center" style="display: none;">
        Next <i class="fas fa-arrow-right ml-2"></i>
    </a>
@endsection

@push('scripts')
<script>
    document.getElementById('run-migrations-form').addEventListener('submit', function (e) {
        e.preventDefault();
        document.getElementById('migration-status').innerHTML = '<div class="flex items-center gap-2 text-blue-600 font-semibold"><i class="fas fa-spinner fa-spin"></i> Running migrations...</div>';

        fetch('{{ route('install.run.migrations') }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('migration-status').innerHTML = '<div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg text-green-700 flex items-center gap-2"><i class="fas fa-check-circle"></i>' + data.message + '</div>';
                document.getElementById('next-step').style.display = 'block';
            } else {
                document.getElementById('migration-status').innerHTML = '<div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg text-red-700 flex items-center gap-2"><i class="fas fa-exclamation-triangle"></i>' + data.message + '</div>';
            }
        });
    });
</script>
@endpush
