<?php

namespace App\Services;

use App\Models\Cylinder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class QRCodeService
{
    /**
     * Generate QR code for cylinder
     */
    public function generateCylinderQR(Cylinder $cylinder, $format = 'svg', $size = 200)
    {
        // Use simple text-based QR for now, will enhance with actual QR library
        $qrData = [
            'type' => 'cylinder',
            'id' => $cylinder->id,
            'qr_code' => $cylinder->qr_code,
            'gas_type' => $cylinder->gasType->code,
            'location' => $cylinder->location->name,
            'url' => route('scan.result', $cylinder->qr_code),
        ];

        return json_encode($qrData);
    }

    /**
     * Generate unique QR code string
     */
    public function generateUniqueQRCode($prefix = 'CYL')
    {
        do {
            $qrCode = $prefix . '-' . strtoupper(Str::random(8));
        } while (Cylinder::where('qr_code', $qrCode)->exists());

        return $qrCode;
    }

    /**
     * Generate QR code image and save to storage
     */
    public function generateAndSaveQR(Cylinder $cylinder, $format = 'png')
    {
        $qrData = $this->generateCylinderQR($cylinder);
        
        // For now, create a simple text file representing the QR code
        // This will be replaced with actual QR code generation
        $filename = "qr_codes/{$cylinder->qr_code}.{$format}";
        
        Storage::disk('public')->put($filename, $qrData);
        
        return $filename;
    }

    /**
     * Get QR code URL for cylinder
     */
    public function getQRCodeUrl(Cylinder $cylinder)
    {
        return route('cylinders.qr', $cylinder);
    }

    /**
     * Validate QR code format
     */
    public function validateQRCode($qrCode)
    {
        // Basic validation for QR code format
        return preg_match('/^[A-Z]{3}-[A-Z0-9]{8}$/', $qrCode);
    }

    /**
     * Parse QR code data
     */
    public function parseQRCode($qrCode)
    {
        $cylinder = Cylinder::where('qr_code', $qrCode)->first();
        
        if (!$cylinder) {
            return null;
        }

        return [
            'cylinder' => $cylinder,
            'gas_type' => $cylinder->gasType,
            'location' => $cylinder->location,
            'status' => $cylinder->getStatusLabel(),
            'last_scan' => $cylinder->logs()->where('action', 'scanned')->latest()->first(),
        ];
    }

    /**
     * Generate batch QR codes for multiple cylinders
     */
    public function generateBatchQRCodes($cylinders, $format = 'png')
    {
        $results = [];
        
        foreach ($cylinders as $cylinder) {
            try {
                $filename = $this->generateAndSaveQR($cylinder, $format);
                $results[] = [
                    'cylinder_id' => $cylinder->id,
                    'qr_code' => $cylinder->qr_code,
                    'filename' => $filename,
                    'success' => true,
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'cylinder_id' => $cylinder->id,
                    'qr_code' => $cylinder->qr_code,
                    'error' => $e->getMessage(),
                    'success' => false,
                ];
            }
        }
        
        return $results;
    }

    /**
     * Create printable QR code labels
     */
    public function createPrintableLabels($cylinders, $labelSize = 'small')
    {
        $labels = [];
        
        foreach ($cylinders as $cylinder) {
            $labels[] = [
                'qr_code' => $cylinder->qr_code,
                'unique_id' => $cylinder->unique_id,
                'gas_type' => $cylinder->gasType->name,
                'capacity' => $cylinder->capacity . 'L',
                'qr_image_url' => $this->getQRCodeUrl($cylinder),
                'scan_url' => route('scan.result', $cylinder->qr_code),
            ];
        }
        
        return $labels;
    }
}
