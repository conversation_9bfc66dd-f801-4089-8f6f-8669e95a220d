<?php
/**
 * GCMS Database Connection Fixer
 * This script helps diagnose and fix database connection issues
 */

echo "🔧 GCMS Database Connection Fixer\n";
echo "==================================\n\n";

// Check if we're in the right directory
if (!file_exists('.env')) {
    echo "❌ Error: .env file not found. Please run this script from the GCMS root directory\n";
    exit(1);
}

echo "1. Current Database Configuration:\n";
echo "==================================\n";

// Read current .env configuration
$envContent = file_get_contents('.env');
preg_match('/DB_HOST=(.*)/', $envContent, $host_match);
preg_match('/DB_PORT=(.*)/', $envContent, $port_match);
preg_match('/DB_DATABASE=(.*)/', $envContent, $db_match);
preg_match('/DB_USERNAME=(.*)/', $envContent, $user_match);
preg_match('/DB_PASSWORD=(.*)/', $envContent, $pass_match);

$current_host = trim($host_match[1] ?? '127.0.0.1');
$current_port = trim($port_match[1] ?? '3306');
$current_database = trim($db_match[1] ?? 'gcms_database');
$current_username = trim($user_match[1] ?? 'root');
$current_password = trim($pass_match[1] ?? '');

echo "Host: $current_host\n";
echo "Port: $current_port\n";
echo "Database: $current_database\n";
echo "Username: $current_username\n";
echo "Password: " . (empty($current_password) ? '(empty)' : '(set)') . "\n\n";

echo "2. Testing Database Connections:\n";
echo "================================\n";

// Test configurations to try
$test_configs = [
    [
        'name' => 'Current Configuration',
        'host' => $current_host,
        'port' => $current_port,
        'database' => $current_database,
        'username' => $current_username,
        'password' => $current_password
    ],
    [
        'name' => 'Local MySQL (root, no password)',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'gcms_database',
        'username' => 'root',
        'password' => ''
    ],
    [
        'name' => 'Local MySQL (root, password)',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'gcms_database',
        'username' => 'root',
        'password' => 'password'
    ],
    [
        'name' => 'XAMPP Default',
        'host' => 'localhost',
        'port' => '3306',
        'database' => 'gcms_database',
        'username' => 'root',
        'password' => ''
    ],
    [
        'name' => 'WAMP Default',
        'host' => 'localhost',
        'port' => '3306',
        'database' => 'gcms_database',
        'username' => 'root',
        'password' => ''
    ]
];

$working_config = null;

foreach ($test_configs as $config) {
    echo "Testing: {$config['name']}...\n";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        
        // Test if database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
        $db_exists = $stmt->rowCount() > 0;
        
        if ($db_exists) {
            echo "✅ Connection successful! Database '{$config['database']}' exists.\n";
            $working_config = $config;
            break;
        } else {
            echo "⚠️  Connection successful, but database '{$config['database']}' doesn't exist.\n";
            
            // Try to create the database
            try {
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "✅ Database '{$config['database']}' created successfully!\n";
                $working_config = $config;
                break;
            } catch (Exception $e) {
                echo "❌ Could not create database: " . $e->getMessage() . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Connection failed: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

if ($working_config) {
    echo "3. Updating .env Configuration:\n";
    echo "===============================\n";
    
    // Update .env file with working configuration
    $newEnvContent = $envContent;
    $newEnvContent = preg_replace('/DB_HOST=.*/', "DB_HOST={$working_config['host']}", $newEnvContent);
    $newEnvContent = preg_replace('/DB_PORT=.*/', "DB_PORT={$working_config['port']}", $newEnvContent);
    $newEnvContent = preg_replace('/DB_DATABASE=.*/', "DB_DATABASE={$working_config['database']}", $newEnvContent);
    $newEnvContent = preg_replace('/DB_USERNAME=.*/', "DB_USERNAME={$working_config['username']}", $newEnvContent);
    $newEnvContent = preg_replace('/DB_PASSWORD=.*/', "DB_PASSWORD={$working_config['password']}", $newEnvContent);
    
    file_put_contents('.env', $newEnvContent);
    echo "✅ .env file updated with working database configuration!\n\n";
    
    echo "4. Testing Laravel Database Connection:\n";
    echo "======================================\n";
    
    // Clear Laravel config cache
    exec('php artisan config:clear', $output);
    
    // Test Laravel connection
    try {
        exec('php artisan tinker --execute="DB::connection()->getPdo(); echo \'Laravel database connection successful!\';"', $laravelTest, $returnCode);
        if ($returnCode === 0) {
            echo "✅ Laravel database connection working!\n\n";
            
            echo "5. Running Database Migrations:\n";
            echo "===============================\n";
            
            exec('php artisan migrate --force', $migrateOutput, $migrateCode);
            if ($migrateCode === 0) {
                echo "✅ Database migrations completed successfully!\n\n";
                
                echo "🎉 Database Setup Complete!\n";
                echo "===========================\n";
                echo "✅ Database connection fixed\n";
                echo "✅ Database created/verified\n";
                echo "✅ Laravel connection working\n";
                echo "✅ Migrations completed\n\n";
                
                echo "🚀 Next Steps:\n";
                echo "1. Start the server: php -S localhost:8000 -t public\n";
                echo "2. Access installation wizard: http://localhost:8000/install\n";
                echo "3. Complete the setup process\n\n";
                
            } else {
                echo "❌ Migration failed. Output:\n";
                echo implode("\n", $migrateOutput) . "\n\n";
                echo "💡 Try running the installation wizard instead: http://localhost:8000/install\n";
            }
        } else {
            echo "❌ Laravel database connection still failing\n";
            echo "💡 Try running: php artisan config:clear\n";
        }
    } catch (Exception $e) {
        echo "❌ Laravel test failed: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ No working database configuration found!\n\n";
    echo "🔧 Manual Setup Required:\n";
    echo "=========================\n";
    echo "1. Install MySQL/MariaDB if not installed\n";
    echo "2. Start MySQL service\n";
    echo "3. Create database manually:\n";
    echo "   mysql -u root -p\n";
    echo "   CREATE DATABASE gcms_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "   exit\n";
    echo "4. Update .env file with correct credentials\n";
    echo "5. Run this script again\n\n";
    
    echo "📋 Common MySQL Installation Commands:\n";
    echo "=====================================\n";
    echo "Windows (XAMPP): Start XAMPP Control Panel\n";
    echo "Windows (Standalone): net start mysql\n";
    echo "macOS (Homebrew): brew services start mysql\n";
    echo "Linux (Ubuntu): sudo systemctl start mysql\n";
}

echo "\n🔍 Troubleshooting Tips:\n";
echo "========================\n";
echo "- Check if MySQL service is running\n";
echo "- Verify MySQL credentials\n";
echo "- Try connecting with MySQL client\n";
echo "- Check firewall settings\n";
echo "- Ensure MySQL is listening on correct port\n";
echo "\n📞 If issues persist, check MySQL error logs\n";
?>
