<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TankAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'tank_id',
        'alert_type',
        'severity',
        'message',
        'triggered_at',
        'acknowledged_at',
        'resolved_at',
        'status',
        'acknowledged_by',
        'resolved_by',
        'action_taken',
        'notes',
        'alert_data',
    ];

    protected $casts = [
        'triggered_at' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'alert_data' => 'array',
    ];

    /**
     * Alert types
     */
    const ALERT_TYPES = [
        'low_level' => 'Low Level',
        'critical_level' => 'Critical Level',
        'high_pressure' => 'High Pressure',
        'low_pressure' => 'Low Pressure',
        'temperature_high' => 'High Temperature',
        'temperature_low' => 'Low Temperature',
        'maintenance_due' => 'Maintenance Due',
        'inspection_due' => 'Inspection Due',
        'certificate_expiry' => 'Certificate Expiry',
        'sensor_failure' => 'Sensor Failure',
        'leak_detected' => 'Leak Detected',
        'valve_malfunction' => 'Valve Malfunction',
        'safety_system_failure' => 'Safety System Failure',
    ];

    /**
     * Severity levels
     */
    const SEVERITIES = [
        'info' => 'Information',
        'warning' => 'Warning',
        'critical' => 'Critical',
        'emergency' => 'Emergency',
    ];

    /**
     * Alert statuses
     */
    const STATUSES = [
        'active' => 'Active',
        'acknowledged' => 'Acknowledged',
        'resolved' => 'Resolved',
        'dismissed' => 'Dismissed',
    ];

    /**
     * Get the tank for this alert
     */
    public function tank(): BelongsTo
    {
        return $this->belongsTo(Tank::class);
    }

    /**
     * Get the user who acknowledged this alert
     */
    public function acknowledgedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'acknowledged_by');
    }

    /**
     * Get the user who resolved this alert
     */
    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Get alert type label
     */
    public function getAlertTypeLabel(): string
    {
        return self::ALERT_TYPES[$this->alert_type] ?? ucfirst(str_replace('_', ' ', $this->alert_type));
    }

    /**
     * Get severity label with color
     */
    public function getSeverityLabel(): array
    {
        return match($this->severity) {
            'info' => ['label' => 'Information', 'color' => 'blue'],
            'warning' => ['label' => 'Warning', 'color' => 'yellow'],
            'critical' => ['label' => 'Critical', 'color' => 'red'],
            'emergency' => ['label' => 'Emergency', 'color' => 'red'],
            default => ['label' => ucfirst($this->severity), 'color' => 'gray']
        };
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'active' => ['label' => 'Active', 'color' => 'red'],
            'acknowledged' => ['label' => 'Acknowledged', 'color' => 'yellow'],
            'resolved' => ['label' => 'Resolved', 'color' => 'green'],
            'dismissed' => ['label' => 'Dismissed', 'color' => 'gray'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Acknowledge alert
     */
    public function acknowledge(string $notes = null): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $this->update([
            'status' => 'acknowledged',
            'acknowledged_at' => now(),
            'acknowledged_by' => auth()->id(),
            'notes' => $notes,
        ]);

        return true;
    }

    /**
     * Resolve alert
     */
    public function resolve(string $actionTaken = null, string $notes = null): bool
    {
        if (!in_array($this->status, ['active', 'acknowledged'])) {
            return false;
        }

        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => auth()->id(),
            'action_taken' => $actionTaken,
            'notes' => $notes ?? $this->notes,
        ]);

        return true;
    }

    /**
     * Dismiss alert
     */
    public function dismiss(string $reason = null): bool
    {
        if ($this->status === 'resolved') {
            return false;
        }

        $this->update([
            'status' => 'dismissed',
            'resolved_at' => now(),
            'resolved_by' => auth()->id(),
            'notes' => $reason,
        ]);

        return true;
    }

    /**
     * Check if alert is overdue for acknowledgment
     */
    public function isOverdueForAcknowledgment(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $overdueThreshold = match($this->severity) {
            'emergency' => 5, // 5 minutes
            'critical' => 15, // 15 minutes
            'warning' => 60, // 1 hour
            'info' => 240, // 4 hours
            default => 60
        };

        return $this->triggered_at->addMinutes($overdueThreshold)->isPast();
    }

    /**
     * Scope for active alerts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for specific severity
     */
    public function scopeSeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for specific tank
     */
    public function scopeForTank($query, $tankId)
    {
        return $query->where('tank_id', $tankId);
    }

    /**
     * Scope for overdue alerts
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->where('severity', 'emergency')
                          ->where('triggered_at', '<=', now()->subMinutes(5))
                          ->orWhere('severity', 'critical')
                          ->where('triggered_at', '<=', now()->subMinutes(15))
                          ->orWhere('severity', 'warning')
                          ->where('triggered_at', '<=', now()->subHours(1))
                          ->orWhere('severity', 'info')
                          ->where('triggered_at', '<=', now()->subHours(4));
                    });
    }
}
