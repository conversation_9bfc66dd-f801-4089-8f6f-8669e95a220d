<?php
/**
 * Fix Admin User Email Verification
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;

echo "🔧 Fixing Admin User Email Verification\n";
echo "=======================================\n\n";

try {
    // Find and fix admin user
    $user = User::where('email', '<EMAIL>')->first();
    
    if ($user) {
        echo "📧 Current email verification status: " . ($user->email_verified_at ? 'Verified' : 'Not Verified') . "\n";
        
        // Verify email
        $user->email_verified_at = now();
        $user->save();
        
        echo "✅ Email verification fixed!\n";
        echo "   Email: {$user->email}\n";
        echo "   Verified at: {$user->email_verified_at}\n";
        
        // Test login capability
        if ($user->is_active && $user->email_verified_at) {
            echo "✅ User can now login successfully!\n";
        }
        
    } else {
        echo "❌ Admin user not found!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 Admin user is now ready for login!\n";
echo "=====================================\n";
echo "📧 Email: <EMAIL>\n";
echo "🔑 Password: password123\n";
echo "🌐 Login URL: http://localhost:8000/login\n";
?>
