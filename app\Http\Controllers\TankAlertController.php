<?php

namespace App\Http\Controllers;

use App\Models\TankAlert;
use App\Models\Tank;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TankAlertController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display tank alerts
     */
    public function index(Request $request)
    {
        $alerts = TankAlert::with(['tank'])
                          ->latest()
                          ->paginate(20);

        return view('tank-alerts.index', compact('alerts'));
    }

    /**
     * Mark alert as resolved
     */
    public function resolve(TankAlert $alert)
    {
        $alert->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Alert marked as resolved'
        ]);
    }
}
