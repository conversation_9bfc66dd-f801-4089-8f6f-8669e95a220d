<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('My Profile') }}
            </h2>
            <a href="{{ route('profile.edit') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Edit Profile
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Profile Information -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center space-x-6">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <div class="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-2xl font-medium text-gray-700">
                                    {{ substr($user->name, 0, 2) }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Basic Info -->
                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $user->name }}</h3>
                            <p class="text-gray-600 dark:text-gray-400">{{ $user->email }}</p>
                            @if($user->phone)
                                <p class="text-gray-600 dark:text-gray-400">{{ $user->phone }}</p>
                            @endif
                            <div class="mt-2">
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ $user->getRoleLabel() }}
                                </span>
                                @if($user->is_active)
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 ml-2">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 ml-2">
                                        Inactive
                                    </span>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Quick Stats -->
                        <div class="text-right">
                            <div class="text-sm text-gray-500 dark:text-gray-400">Member since</div>
                            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {{ $user->created_at->format('M Y') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Assigned Locations -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Assigned Locations</h4>
                            <a href="{{ route('profile.locations') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                View All
                            </a>
                        </div>
                        
                        @if($user->locations->count() > 0)
                            <div class="space-y-3">
                                @foreach($user->locations->take(3) as $location)
                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900 dark:text-gray-100">{{ $location->name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ $location->getTypeLabel() }}</div>
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Assigned
                                        </span>
                                    </div>
                                @endforeach
                                @if($user->locations->count() > 3)
                                    <div class="text-center text-sm text-gray-500 dark:text-gray-400">
                                        +{{ $user->locations->count() - 3 }} more locations
                                    </div>
                                @endif
                            </div>
                        @else
                            <p class="text-gray-500 dark:text-gray-400 text-center py-4">
                                No locations assigned
                            </p>
                        @endif

                        @if($user->managedLocations->count() > 0)
                            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                                <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-3">Managing Locations</h5>
                                <div class="space-y-2">
                                    @foreach($user->managedLocations as $location)
                                        <div class="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900 rounded">
                                            <div class="text-sm font-medium text-blue-900 dark:text-blue-100">{{ $location->name }}</div>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Manager
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Permissions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Permissions</h4>
                        
                        <div class="space-y-2 max-h-64 overflow-y-auto">
                            @foreach($user->getAllPermissions()->groupBy(function($permission) {
                                return explode('_', $permission->name)[0];
                            }) as $group => $permissions)
                                <div class="mb-3">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        {{ ucfirst($group) }}
                                    </div>
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($permissions as $permission)
                                            <span class="inline-flex px-2 py-1 text-xs rounded bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                                                {{ str_replace($group . '_', '', $permission->name) }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h4>
                        
                        <div class="space-y-3">
                            <a href="{{ route('profile.edit') }}" 
                               class="block w-full text-center bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Edit Profile
                            </a>
                            
                            <a href="{{ route('profile.activity') }}" 
                               class="block w-full text-center bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                View Activity Log
                            </a>
                            
                            <a href="{{ route('profile.preferences') }}" 
                               class="block w-full text-center bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Preferences
                            </a>
                            
                            <button onclick="document.getElementById('password-form').style.display='block'" 
                                    class="block w-full text-center bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                                Change Password
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Password Form (Hidden by default) -->
            <div id="password-form" style="display: none;" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Change Password</h4>
                    
                    <form method="POST" action="{{ route('profile.password') }}" class="space-y-4">
                        @csrf
                        @method('PATCH')
                        
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Current Password
                            </label>
                            <input type="password" name="current_password" id="current_password" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                New Password
                            </label>
                            <input type="password" name="password" id="password" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Confirm New Password
                            </label>
                            <input type="password" name="password_confirmation" id="password_confirmation" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="document.getElementById('password-form').style.display='none'"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
