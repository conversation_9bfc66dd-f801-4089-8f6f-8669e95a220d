@props([
    'title',
    'value',
    'icon',
    'trend' => null,
    'trendDirection' => 'up',
    'color' => 'blue',
    'href' => null
])

@php
$colorClasses = [
    'blue' => 'from-blue-500 to-blue-600',
    'green' => 'from-green-500 to-green-600',
    'yellow' => 'from-yellow-500 to-orange-500',
    'purple' => 'from-purple-500 to-pink-500',
    'red' => 'from-red-500 to-red-600',
];

$gradientClass = $colorClasses[$color] ?? $colorClasses['blue'];
$component = $href ? 'a' : 'div';
@endphp

<{{ $component }} 
    @if($href) href="{{ $href }}" @endif
    class="bg-gradient-to-r {{ $gradientClass }} rounded-xl shadow-lg p-6 text-white transform hover:scale-105 transition-transform duration-200 metric-card dashboard-card">
    
    <div class="flex items-center justify-between">
        <div>
            <p class="text-white text-opacity-80 text-sm font-medium">{{ $title }}</p>
            <p class="text-3xl font-bold animated-number">{{ $value }}</p>
            
            @if($trend)
                <p class="text-white text-opacity-80 text-xs mt-1">
                    <i class="fas fa-arrow-{{ $trendDirection === 'up' ? 'up' : 'down' }} mr-1"></i>
                    {{ $trend }}
                </p>
            @endif
        </div>
        
        <div class="bg-white bg-opacity-30 rounded-full p-3">
            <i class="{{ $icon }} text-2xl"></i>
        </div>
    </div>
    
    <!-- Real-time indicator -->
    <div class="real-time-indicator absolute top-2 right-2">
        <div class="w-2 h-2 bg-white rounded-full opacity-75"></div>
    </div>
</{{ $component }}>

<style>
.metric-card {
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.animated-number {
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.real-time-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}
</style>
