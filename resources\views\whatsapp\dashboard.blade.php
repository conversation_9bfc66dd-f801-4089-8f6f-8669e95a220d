<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('WhatsApp Communication Dashboard') }}
            </h2>
            <div class="flex space-x-2">
                @can('manage_whatsapp')
                    <a href="{{ route('whatsapp.messages.create') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        💬 Send Message
                    </a>
                    <a href="{{ route('whatsapp.templates.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        📝 New Template
                    </a>
                @endcan
                @can('view_whatsapp')
                    <a href="{{ route('whatsapp.messages.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        📋 All Messages
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- WhatsApp Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($statistics['total_messages']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Messages</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {{ number_format($statistics['sent_messages']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Sent</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                            {{ number_format($statistics['delivered_messages']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Delivered</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {{ number_format($statistics['read_messages']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Read</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ number_format($statistics['failed_messages']) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Failed</div>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-green-600 dark:text-green-400">
                            {{ number_format($statistics['delivery_rate'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Delivery Rate</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-purple-600 dark:text-purple-400">
                            {{ number_format($statistics['read_rate'], 1) }}%
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Read Rate</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-4 text-center">
                        <div class="text-xl font-bold text-emerald-600 dark:text-emerald-400">
                            ${{ number_format($statistics['total_cost'], 2) }}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Total Cost</div>
                    </div>
                </div>
            </div>

            <!-- Failed Messages Alert -->
            @if($failedMessages->count() > 0)
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Failed Messages Alert
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            {{ $failedMessages->count() }} messages failed to send and require attention
                        </div>
                    </div>
                </div>
                <div class="max-h-48 overflow-y-auto">
                    @foreach($failedMessages as $message)
                        <div class="flex justify-between items-center py-2 border-b border-red-200 dark:border-red-700 last:border-b-0">
                            <div>
                                <span class="font-medium text-red-800 dark:text-red-200">{{ $message->phone_number }}</span>
                                <span class="text-red-600 dark:text-red-400">- {{ $message->getMessageTypeLabel() }}</span>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-red-600 dark:text-red-400">
                                    {{ $message->failed_at->diffForHumans() }}
                                </div>
                                <div class="text-xs text-red-500 dark:text-red-500">
                                    {{ Str::limit($message->error_message, 30) }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="mt-4">
                    <a href="{{ route('whatsapp.messages.index', ['filter' => 'failed']) }}" 
                       class="text-red-600 hover:text-red-800 text-sm font-medium">
                        View all failed messages →
                    </a>
                </div>
            </div>
            @endif

            <!-- Dashboard Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Messages -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Recent Messages
                            </h3>
                            <a href="{{ route('whatsapp.messages.index') }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($recentMessages as $message)
                                @php $statusLabel = $message->getStatusLabel(); @endphp
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $message->phone_number }}
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ $message->customer->name ?? 'Unknown Contact' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-500">
                                            {{ $message->getMessageTypeLabel() }}
                                            @if($message->template)
                                                - {{ $message->template->name }}
                                            @endif
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                            {{ $statusLabel['label'] }}
                                        </span>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            {{ $message->created_at->diffForHumans() }}
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No recent messages
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Active Templates -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                Active Templates
                            </h3>
                            <a href="{{ route('whatsapp.templates.index') }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View All →
                            </a>
                        </div>
                        <div class="space-y-3">
                            @forelse($activeTemplates as $template)
                                @php $statusLabel = $template->getStatusLabel(); @endphp
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                            {{ $template->name }}
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ $template->getCategoryLabel() }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-500">
                                            {{ $template->getUseCaseLabel() }}
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                            {{ $statusLabel['label'] }}
                                        </span>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            {{ $template->messages_count ?? 0 }} sent
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                    No active templates
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        Quick Actions
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        @can('manage_whatsapp')
                            <button onclick="openSendMessageModal()" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">💬</div>
                                    <div class="text-sm font-medium text-green-800 dark:text-green-200">Send Message</div>
                                </div>
                            </button>
                            
                            <a href="{{ route('whatsapp.templates.create') }}" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">📝</div>
                                    <div class="text-sm font-medium text-blue-800 dark:text-blue-200">Create Template</div>
                                </div>
                            </a>
                        @endcan
                        
                        <a href="{{ route('whatsapp.contacts.index') }}" class="flex items-center justify-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors">
                            <div class="text-center">
                                <div class="text-2xl mb-2">👥</div>
                                <div class="text-sm font-medium text-purple-800 dark:text-purple-200">Manage Contacts</div>
                            </div>
                        </a>
                        
                        <a href="{{ route('whatsapp.messages.index', ['filter' => 'failed']) }}" class="flex items-center justify-center p-4 bg-red-50 dark:bg-red-900 rounded-lg hover:bg-red-100 dark:hover:bg-red-800 transition-colors">
                            <div class="text-center">
                                <div class="text-2xl mb-2">⚠️</div>
                                <div class="text-sm font-medium text-red-800 dark:text-red-200">Failed Messages</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Send Message Modal -->
    <div id="sendMessageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Send WhatsApp Message</h3>
                <form id="sendMessageForm">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number</label>
                        <input type="text" name="phone_number" required 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Message Type</label>
                        <select name="message_type" required onchange="toggleMessageFields()"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="text">Text Message</option>
                            <option value="template">Template Message</option>
                        </select>
                    </div>
                    <div id="textMessageField" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Message</label>
                        <textarea name="text_content" rows="3"
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                    </div>
                    <div id="templateMessageField" class="mb-4 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Template</label>
                        <select name="template_id"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">Select Template</option>
                            @foreach($activeTemplates as $template)
                                <option value="{{ $template->id }}">{{ $template->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeSendMessageModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openSendMessageModal() {
            document.getElementById('sendMessageModal').classList.remove('hidden');
        }

        function closeSendMessageModal() {
            document.getElementById('sendMessageModal').classList.add('hidden');
            document.getElementById('sendMessageForm').reset();
        }

        function toggleMessageFields() {
            const messageType = document.querySelector('select[name="message_type"]').value;
            const textField = document.getElementById('textMessageField');
            const templateField = document.getElementById('templateMessageField');
            
            if (messageType === 'template') {
                textField.classList.add('hidden');
                templateField.classList.remove('hidden');
            } else {
                textField.classList.remove('hidden');
                templateField.classList.add('hidden');
            }
        }

        document.getElementById('sendMessageForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/whatsapp/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('Message sent successfully!');
                    closeSendMessageModal();
                    location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            } catch (error) {
                console.error('Send message error:', error);
                alert('Failed to send message. Please try again.');
            }
        });

        // Auto-refresh dashboard every 2 minutes
        setInterval(function() {
            location.reload();
        }, 120000);
    </script>
</x-app-layout>
