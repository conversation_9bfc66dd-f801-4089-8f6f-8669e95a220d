@extends('installation.layout')

@section('title', 'System Requirements')

@section('content')
<div class="px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-server text-2xl text-blue-600"></i>
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">System Requirements Check</h2>
        <p class="text-gray-600">Verifying your server meets the minimum requirements for GCMS</p>
    </div>

    <!-- Alerts Container -->
    <div id="alerts-container"></div>

    <!-- Requirements Check Results -->
    <div class="space-y-6">
        <!-- PHP Requirements -->
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fab fa-php text-blue-600 mr-3"></i>
                    PHP Requirements
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @foreach($requirements['requirements'] as $key => $requirement)
                        <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 mr-4">
                                    @if($requirement['check'])
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-600"></i>
                                        </div>
                                    @else
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600"></i>
                                        </div>
                                    @endif
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">{{ $requirement['name'] }}</h4>
                                    <p class="text-sm text-gray-500">Current: {{ $requirement['current'] }}</p>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                @if($requirement['check'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Passed
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Failed
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Overall Status -->
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="p-6">
                @if($canProceed)
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-green-900">All Requirements Met!</h3>
                            <p class="text-green-700">Your server meets all the requirements for GCMS installation.</p>
                        </div>
                    </div>
                @else
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-circle text-red-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-red-900">Requirements Not Met</h3>
                            <p class="text-red-700">Please fix the failed requirements before proceeding with installation.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recommendations -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">
                <i class="fas fa-lightbulb mr-2"></i>
                Recommendations
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Performance:</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• PHP memory_limit: 256M or higher</li>
                        <li>• PHP max_execution_time: 300 seconds</li>
                        <li>• Enable OPcache for better performance</li>
                        <li>• Use Redis for caching and sessions</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Security:</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Keep PHP version updated</li>
                        <li>• Enable HTTPS in production</li>
                        <li>• Configure proper file permissions</li>
                        <li>• Use strong database passwords</li>
                    </ul>
                </div>
            </div>
        </div>

        @if(!$canProceed)
            <!-- Troubleshooting Guide -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-yellow-900 mb-4">
                    <i class="fas fa-tools mr-2"></i>
                    Troubleshooting Guide
                </h3>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-yellow-800 mb-2">Missing PHP Extensions:</h4>
                        <div class="bg-yellow-100 rounded p-3">
                            <code class="text-sm text-yellow-800">
                                # Ubuntu/Debian<br>
                                sudo apt-get install php-{extension-name}<br><br>
                                # CentOS/RHEL<br>
                                sudo yum install php-{extension-name}<br><br>
                                # After installation, restart your web server
                            </code>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-yellow-800 mb-2">PHP Version Update:</h4>
                        <div class="bg-yellow-100 rounded p-3">
                            <code class="text-sm text-yellow-800">
                                # Check current PHP version<br>
                                php -v<br><br>
                                # Update PHP (Ubuntu/Debian)<br>
                                sudo apt-get update && sudo apt-get upgrade php
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Navigation -->
    <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
        <a href="{{ route('install.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-lg transition duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <div class="flex items-center space-x-4">
            <button onclick="location.reload()" 
                    class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium rounded-lg transition duration-200">
                <i class="fas fa-sync-alt mr-2"></i>
                Recheck
            </button>
            
            @if($canProceed)
                <a href="{{ route('install.permissions') }}" 
                   class="inline-flex items-center px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    Continue
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            @else
                <button disabled 
                        class="inline-flex items-center px-6 py-2 bg-gray-300 text-gray-500 font-medium rounded-lg cursor-not-allowed">
                    Fix Requirements First
                    <i class="fas fa-lock ml-2"></i>
                </button>
            @endif
        </div>
    </div>
</div>
@endsection
