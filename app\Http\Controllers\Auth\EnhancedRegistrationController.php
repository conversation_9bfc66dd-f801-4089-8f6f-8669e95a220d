<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Spatie\Permission\Models\Role;

class EnhancedRegistrationController extends Controller
{
    /**
     * Show the enhanced registration form
     */
    public function create()
    {
        $roles = Role::whereNotIn('name', ['super_admin'])->get();
        $locations = Location::active()->get();

        return view('auth.enhanced-register', compact('roles', 'locations'));
    }

    /**
     * Handle enhanced registration
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'exists:roles,name'],
            'locations' => ['nullable', 'array'],
            'locations.*' => ['exists:locations,id'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        // Assign role
        $user->assignRole($request->role);

        // Assign locations if provided
        if ($request->filled('locations')) {
            $locationData = [];
            foreach ($request->locations as $locationId) {
                $locationData[$locationId] = ['assigned_at' => now()];
            }
            $user->locations()->attach($locationData);
        }

        // Log the user in
        auth()->login($user);

        return redirect()->route('dashboard')->with('success', 'Registration successful! Welcome to GCMS.');
    }

    /**
     * Show customer registration form (simplified)
     */
    public function createCustomer()
    {
        return view('auth.customer-register');
    }

    /**
     * Handle customer registration
     */
    public function storeCustomer(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'address' => ['required', 'string'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        // Assign customer role
        $user->assignRole('customer');

        // Create customer record
        \App\Models\Customer::create([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'address' => $request->address,
            'credit_limit' => 1000.00, // Default credit limit
            'is_active' => true,
        ]);

        // Log the user in
        auth()->login($user);

        return redirect()->route('dashboard')->with('success', 'Customer registration successful! Welcome to GCMS.');
    }
}
