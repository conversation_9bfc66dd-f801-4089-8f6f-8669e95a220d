<?php
/**
 * Check Tanks Table Structure
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "🔍 Checking Tanks Table Structure\n";
echo "=================================\n\n";

try {
    echo "1. Checking if tanks table exists:\n";
    echo "==================================\n";
    
    if (Schema::hasTable('tanks')) {
        echo "✅ tanks table EXISTS\n\n";
        
        echo "2. Current table structure:\n";
        echo "===========================\n";
        
        $columns = DB::select('DESCRIBE tanks');
        foreach($columns as $column) {
            echo "   {$column->Field} - {$column->Type}\n";
        }
        
        echo "\n3. Checking for required columns:\n";
        echo "=================================\n";
        
        $requiredColumns = [
            'name',
            'current_level',
            'capacity', 
            'min_level',
            'max_level',
            'location_id',
            'gas_type_id',
            'status'
        ];
        
        $existingColumns = array_map(function($col) {
            return $col->Field;
        }, $columns);
        
        $missingColumns = [];
        foreach($requiredColumns as $required) {
            if (in_array($required, $existingColumns)) {
                echo "   ✅ $required - EXISTS\n";
            } else {
                echo "   ❌ $required - MISSING\n";
                $missingColumns[] = $required;
            }
        }
        
        if (!empty($missingColumns)) {
            echo "\n4. Missing columns that need to be added:\n";
            echo "=========================================\n";
            foreach($missingColumns as $missing) {
                echo "   - $missing\n";
            }
            
            echo "\n💡 Solution: Create migration to add missing columns\n";
        } else {
            echo "\n✅ All required columns exist!\n";
        }
        
    } else {
        echo "❌ tanks table DOES NOT EXIST\n";
        echo "💡 Solution: Create tanks table migration\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Summary:\n";
echo "===========\n";
echo "This script checks the tanks table structure and identifies missing columns.\n";
?>
