<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tanks', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->string('tank_number')->unique();
            $table->foreignId('location_id')->constrained();
            $table->foreignId('gas_type_id')->constrained();
            //$table->unsignedBigInteger('supplier_id')->nullable();
            $table->enum('tank_type', ['storage', 'transport', 'bulk', 'cylinder_bank', 'manifold']);
            $table->decimal('capacity', 10, 2);
            $table->decimal('current_level', 10, 2)->default(0);
            $table->enum('unit_of_measurement', ['liters', 'gallons', 'cubic_meters', 'kilograms', 'pounds', 'percentage']);
            $table->decimal('min_level', 10, 2)->default(0);
            $table->decimal('max_level', 10, 2);
            $table->decimal('reorder_level', 10, 2);
            $table->decimal('critical_level', 10, 2);
            $table->enum('status', ['active', 'inactive', 'maintenance', 'inspection', 'empty', 'full', 'critical', 'out_of_service'])->default('active');
            $table->date('installation_date')->nullable();
            $table->date('last_refill_date')->nullable();
            $table->date('next_maintenance_date')->nullable();
            $table->date('last_inspection_date')->nullable();
            $table->decimal('pressure_rating', 8, 2)->nullable();
            $table->decimal('temperature', 8, 2)->nullable();
            $table->decimal('pressure', 8, 2)->nullable();
            $table->enum('valve_status', ['open', 'closed', 'partially_open', 'maintenance'])->nullable();
            $table->json('safety_systems')->nullable();
            $table->string('compliance_certificate')->nullable();
            $table->date('certificate_expiry')->nullable();
            $table->text('notes')->nullable();
            $table->string('sensor_id')->nullable();
            $table->boolean('auto_refill_enabled')->default(false);
            $table->decimal('refill_threshold', 10, 2)->nullable();
            $table->timestamps();

            $table->index(['location_id', 'status']);
            $table->index(['gas_type_id', 'status']);
            $table->index(['status', 'current_level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tanks');
    }
};
