<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CylinderController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\CustomerController;
use Illuminate\Support\Facades\Route;

// Installation Routes (accessible without authentication)
Route::prefix('install')->name('install.')->group(function () {
    Route::get('/', [\App\Http\Controllers\InstallationController::class, 'index'])->name('index');
    Route::get('/requirements', [\App\Http\Controllers\InstallationController::class, 'requirements'])->name('requirements');
    Route::get('/permissions', [\App\Http\Controllers\InstallationController::class, 'permissions'])->name('permissions');
    Route::get('/database', [\App\Http\Controllers\InstallationController::class, 'database'])->name('database');
    Route::post('/test-database', [\App\Http\Controllers\InstallationController::class, 'testDatabase'])->name('test.database');
    Route::get('/database-setup', [\App\Http\Controllers\InstallationController::class, 'databaseSetup'])->name('database.setup');
    Route::post('/run-migrations', [\App\Http\Controllers\InstallationController::class, 'runMigrations'])->name('run.migrations');
    Route::get('/admin-account', [\App\Http\Controllers\InstallationController::class, 'adminAccount'])->name('admin.account');
    Route::post('/create-admin', [\App\Http\Controllers\InstallationController::class, 'createAdmin'])->name('create.admin');
    Route::get('/company-info', [\App\Http\Controllers\InstallationController::class, 'companyInfo'])->name('company.info');
    Route::post('/save-company', [\App\Http\Controllers\InstallationController::class, 'saveCompanyInfo'])->name('save.company');
    Route::get('/location-setup', [\App\Http\Controllers\InstallationController::class, 'locationSetup'])->name('location.setup');
    Route::post('/save-location', [\App\Http\Controllers\InstallationController::class, 'saveLocation'])->name('save.location');
    Route::get('/gas-types', [\App\Http\Controllers\InstallationController::class, 'gasTypesSetup'])->name('gas.types');
    Route::post('/save-gas-types', [\App\Http\Controllers\InstallationController::class, 'saveGasTypes'])->name('save.gas.types');
    Route::get('/tank-setup', [\App\Http\Controllers\InstallationController::class, 'tankSetup'])->name('tank.setup');
    Route::post('/save-tanks', [\App\Http\Controllers\InstallationController::class, 'saveTanks'])->name('save.tanks');
    Route::get('/final-setup', [\App\Http\Controllers\InstallationController::class, 'finalSetup'])->name('final.setup');
    Route::post('/complete', [\App\Http\Controllers\InstallationController::class, 'completeInstallation'])->name('complete');
    Route::get('/finished', [\App\Http\Controllers\InstallationController::class, 'complete'])->name('finished');
});

// Public routes for testing (removed to avoid conflicts)

// Main application route
Route::get('/', function () {
    return view('welcome');
});

// Custom Landing Page with Login
Route::get('/', [\App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('welcome');
Route::post('/login', [\App\Http\Controllers\Auth\LoginController::class, 'login'])->name('login.post');
Route::post('/logout', [\App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

// Legacy welcome route for compatibility
Route::view('/welcome', 'welcome');

// Removed test-only dashboard/profile routes to let Volt handle via auth.php

// Enhanced Registration Routes
Route::get('/register/enhanced', [\App\Http\Controllers\Auth\EnhancedRegistrationController::class, 'create'])->name('register.enhanced');
Route::post('/register/enhanced', [\App\Http\Controllers\Auth\EnhancedRegistrationController::class, 'store']);
Route::get('/register/customer', [\App\Http\Controllers\Auth\EnhancedRegistrationController::class, 'createCustomer'])->name('register.customer');
Route::post('/register/customer', [\App\Http\Controllers\Auth\EnhancedRegistrationController::class, 'storeCustomer']);

// Dashboard Routes
Route::middleware(['auth', 'verified', 'role.dashboard'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [DashboardController::class, 'getData'])->name('dashboard.data');

    // Profile Routes (now handled publicly)
    Route::get('/profile', [\App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [\App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [\App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::patch('/profile/password', [\App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::get('/profile/activity', [\App\Http\Controllers\ProfileController::class, 'activity'])->name('profile.activity');
    Route::get('/profile/locations', [\App\Http\Controllers\ProfileController::class, 'locations'])->name('profile.locations');
    Route::post('/profile/request-location', [\App\Http\Controllers\ProfileController::class, 'requestLocationAccess'])->name('profile.request-location');
    Route::get('/profile/preferences', [\App\Http\Controllers\ProfileController::class, 'preferences'])->name('profile.preferences');
    Route::patch('/profile/preferences', [\App\Http\Controllers\ProfileController::class, 'updatePreferences'])->name('profile.preferences.update');

    // User Management Routes (Admin only)
    Route::resource('users', \App\Http\Controllers\UserController::class);

    // Cylinder Management Routes
    Route::resource('cylinders', CylinderController::class);
    Route::get('/cylinders/{cylinder}/qr', [CylinderController::class, 'generateQr'])->name('cylinders.qr');
    Route::post('/cylinders/scan', [CylinderController::class, 'scan'])->name('cylinders.scan');

    // Cylinder Status Management
    Route::post('/cylinders/{cylinder}/status', [\App\Http\Controllers\CylinderStatusController::class, 'changeStatus'])->name('cylinders.status');
    Route::post('/cylinders/{cylinder}/fill', [\App\Http\Controllers\CylinderStatusController::class, 'fill'])->name('cylinders.fill');
    Route::post('/cylinders/{cylinder}/empty', [\App\Http\Controllers\CylinderStatusController::class, 'empty'])->name('cylinders.empty');
    Route::post('/cylinders/{cylinder}/damage', [\App\Http\Controllers\CylinderStatusController::class, 'markDamaged'])->name('cylinders.damage');
    Route::post('/cylinders/{cylinder}/maintenance', [\App\Http\Controllers\CylinderStatusController::class, 'sendMaintenance'])->name('cylinders.maintenance');
    Route::post('/cylinders/{cylinder}/move', [\App\Http\Controllers\CylinderStatusController::class, 'move'])->name('cylinders.move');

    // Inventory Management Routes
    Route::get('/inventory', [\App\Http\Controllers\InventoryController::class, 'index'])->name('inventory.index');
    Route::get('/inventory/{location}', [\App\Http\Controllers\InventoryController::class, 'show'])->name('inventory.show');
    Route::post('/inventory/sync', [\App\Http\Controllers\InventoryController::class, 'sync'])->name('inventory.sync');
    Route::post('/inventory/adjust', [\App\Http\Controllers\InventoryController::class, 'adjust'])->name('inventory.adjust');
    Route::patch('/inventory/{inventory}/levels', [\App\Http\Controllers\InventoryController::class, 'updateLevels'])->name('inventory.levels');
    Route::get('/inventory/reorder/suggestions', [\App\Http\Controllers\InventoryController::class, 'reorderSuggestions'])->name('inventory.reorder');

    // Inventory Transfer Routes
    Route::resource('inventory.transfers', \App\Http\Controllers\InventoryTransferController::class);
    Route::post('/inventory/transfers/{transfer}/approve', [\App\Http\Controllers\InventoryTransferController::class, 'approve'])->name('inventory.transfers.approve');
    Route::post('/inventory/transfers/{transfer}/ship', [\App\Http\Controllers\InventoryTransferController::class, 'ship'])->name('inventory.transfers.ship');
    Route::post('/inventory/transfers/{transfer}/receive', [\App\Http\Controllers\InventoryTransferController::class, 'receive'])->name('inventory.transfers.receive');
    Route::post('/inventory/transfers/{transfer}/cancel', [\App\Http\Controllers\InventoryTransferController::class, 'cancel'])->name('inventory.transfers.cancel');
    Route::get('/inventory/transfers/cylinders/available', [\App\Http\Controllers\InventoryTransferController::class, 'getAvailableCylinders'])->name('inventory.transfers.cylinders');

    // Order Management Routes
    Route::resource('orders', \App\Http\Controllers\OrderController::class);
    Route::post('/orders/{order}/assign', [\App\Http\Controllers\OrderController::class, 'assign'])->name('orders.assign');
    Route::post('/orders/{order}/progress', [\App\Http\Controllers\OrderController::class, 'markInProgress'])->name('orders.progress');
    Route::post('/orders/{order}/deliver', [\App\Http\Controllers\OrderController::class, 'deliver'])->name('orders.deliver');
    Route::post('/orders/{order}/cancel', [\App\Http\Controllers\OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('/orders/{order}/otp', [\App\Http\Controllers\OrderController::class, 'generateOtp'])->name('orders.otp');
    Route::get('/orders/statistics/data', [\App\Http\Controllers\OrderController::class, 'statistics'])->name('orders.statistics');

    // Rental Management Routes
    Route::resource('rentals', \App\Http\Controllers\RentalController::class);
    Route::post('/rentals/{rental}/activate', [\App\Http\Controllers\RentalController::class, 'activate'])->name('rentals.activate');
    Route::post('/rentals/{rental}/return', [\App\Http\Controllers\RentalController::class, 'return'])->name('rentals.return');
    Route::post('/rentals/{rental}/extend', [\App\Http\Controllers\RentalController::class, 'extend'])->name('rentals.extend');
    Route::post('/rentals/{rental}/cancel', [\App\Http\Controllers\RentalController::class, 'cancel'])->name('rentals.cancel');
    Route::get('/rentals/statistics/data', [\App\Http\Controllers\RentalController::class, 'statistics'])->name('rentals.statistics');

    // Rental Billing Routes
    Route::get('/rentals/billing', [\App\Http\Controllers\RentalBillingController::class, 'index'])->name('rentals.billing.index');
    Route::get('/rentals/billing/{billing}', [\App\Http\Controllers\RentalBillingController::class, 'show'])->name('rentals.billing.show');
    Route::post('/rentals/billing/{billing}/pay', [\App\Http\Controllers\RentalBillingController::class, 'pay'])->name('rentals.billing.pay');
    Route::post('/rentals/billing/{billing}/cancel', [\App\Http\Controllers\RentalBillingController::class, 'cancel'])->name('rentals.billing.cancel');
    Route::post('/rentals/{rental}/billing/generate', [\App\Http\Controllers\RentalBillingController::class, 'generate'])->name('rentals.billing.generate');

    // Financial Management Routes
    Route::get('/financial', [\App\Http\Controllers\FinancialController::class, 'dashboard'])->name('financial.dashboard');
    Route::get('/financial/invoices', [\App\Http\Controllers\FinancialController::class, 'invoices'])->name('financial.invoices');
    Route::get('/financial/invoices/{invoice}', [\App\Http\Controllers\FinancialController::class, 'showInvoice'])->name('financial.invoices.show');
    Route::get('/financial/payments', [\App\Http\Controllers\FinancialController::class, 'payments'])->name('financial.payments');
    Route::post('/financial/invoices/{invoice}/payment', [\App\Http\Controllers\FinancialController::class, 'processPayment'])->name('financial.invoices.payment');
    Route::post('/financial/invoices/{invoice}/refund', [\App\Http\Controllers\FinancialController::class, 'processRefund'])->name('financial.invoices.refund');
    Route::post('/financial/invoices/{invoice}/sent', [\App\Http\Controllers\FinancialController::class, 'markInvoiceAsSent'])->name('financial.invoices.sent');
    Route::post('/financial/invoices/{invoice}/cancel', [\App\Http\Controllers\FinancialController::class, 'cancelInvoice'])->name('financial.invoices.cancel');
    Route::get('/financial/statistics', [\App\Http\Controllers\FinancialController::class, 'getStatistics'])->name('financial.statistics');

    // Tank & Refill Management Routes
    Route::get('/tanks', [\App\Http\Controllers\TankController::class, 'dashboard'])->name('tanks.dashboard');
    Route::get('/tanks/list', [\App\Http\Controllers\TankController::class, 'index'])->name('tanks.index');
    Route::get('/tanks/{tank}', [\App\Http\Controllers\TankController::class, 'show'])->name('tanks.show');
    Route::post('/tanks/{tank}/reading', [\App\Http\Controllers\TankController::class, 'updateReading'])->name('tanks.reading.update');
    Route::get('/tanks/{tank}/readings', [\App\Http\Controllers\TankController::class, 'readings'])->name('tanks.readings');
    Route::post('/tanks/{tank}/refill/schedule', [\App\Http\Controllers\TankController::class, 'scheduleRefill'])->name('tanks.refill.schedule');
    Route::post('/tanks/refills/{refill}/complete', [\App\Http\Controllers\TankController::class, 'completeRefill'])->name('tanks.refill.complete');
    Route::get('/tanks/alerts/check', [\App\Http\Controllers\TankController::class, 'checkAlerts'])->name('tanks.alerts.check');
    Route::get('/tanks/statistics/data', [\App\Http\Controllers\TankController::class, 'getStatistics'])->name('tanks.statistics');

    // WhatsApp Integration Routes
    Route::get('/whatsapp', [\App\Http\Controllers\WhatsAppController::class, 'dashboard'])->name('whatsapp.dashboard');
    Route::get('/whatsapp/messages', [\App\Http\Controllers\WhatsAppController::class, 'messages'])->name('whatsapp.messages.index');
    Route::get('/whatsapp/contacts', [\App\Http\Controllers\WhatsAppController::class, 'contacts'])->name('whatsapp.contacts.index');
    Route::get('/whatsapp/templates', [\App\Http\Controllers\WhatsAppController::class, 'templates'])->name('whatsapp.templates.index');
    Route::post('/whatsapp/send', [\App\Http\Controllers\WhatsAppController::class, 'sendMessage'])->name('whatsapp.send');
    Route::get('/whatsapp/statistics', [\App\Http\Controllers\WhatsAppController::class, 'getStatistics'])->name('whatsapp.statistics');

    // WhatsApp Webhook (no auth middleware)
    Route::match(['get', 'post'], '/whatsapp/webhook', [\App\Http\Controllers\WhatsAppController::class, 'webhook'])->name('whatsapp.webhook')->withoutMiddleware(['auth']);

    // Dashboard & Analytics Routes
    Route::get('/dashboard/data', [\App\Http\Controllers\DashboardController::class, 'getData'])->name('dashboard.data');
    Route::get('/dashboard/revenue', [\App\Http\Controllers\DashboardController::class, 'getRevenueAnalytics'])->name('dashboard.revenue');
    Route::get('/dashboard/operations', [\App\Http\Controllers\DashboardController::class, 'getOperationalMetrics'])->name('dashboard.operations');
    Route::get('/dashboard/customers', [\App\Http\Controllers\DashboardController::class, 'getCustomerAnalytics'])->name('dashboard.customers');
    Route::get('/dashboard/inventory', [\App\Http\Controllers\DashboardController::class, 'getInventoryAnalytics'])->name('dashboard.inventory');
    Route::get('/dashboard/performance', [\App\Http\Controllers\DashboardController::class, 'getPerformanceMetrics'])->name('dashboard.performance');
    Route::get('/dashboard/export', [\App\Http\Controllers\DashboardController::class, 'exportAnalytics'])->name('dashboard.export');

    // Reports & Analytics Routes
    Route::get('/reports', [\App\Http\Controllers\ReportController::class, 'index'])->name('reports.index');
    Route::post('/reports/business', [\App\Http\Controllers\ReportController::class, 'generateBusinessReport'])->name('reports.business');
    Route::post('/reports/sales', [\App\Http\Controllers\ReportController::class, 'generateSalesReport'])->name('reports.sales');
    Route::post('/reports/inventory', [\App\Http\Controllers\ReportController::class, 'generateInventoryReport'])->name('reports.inventory');
    Route::post('/reports/financial', [\App\Http\Controllers\ReportController::class, 'generateFinancialReport'])->name('reports.financial');
    Route::post('/reports/customer', [\App\Http\Controllers\ReportController::class, 'generateCustomerReport'])->name('reports.customer');
    Route::get('/reports/export', [\App\Http\Controllers\ReportController::class, 'exportReport'])->name('reports.export');

    // Mobile Interface Routes
    Route::prefix('mobile')->name('mobile.')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\MobileController::class, 'dashboard'])->name('dashboard');
        Route::get('/qr-scanner', [\App\Http\Controllers\MobileController::class, 'qrScanner'])->name('qr.scanner');
        Route::post('/qr/process', [\App\Http\Controllers\MobileController::class, 'processQRScan'])->name('qr.process');
        Route::get('/customers/search', [\App\Http\Controllers\MobileController::class, 'customerSearch'])->name('customers.search');
        Route::get('/customers/{customer}', [\App\Http\Controllers\MobileController::class, 'customerDetails'])->name('customers.details');
        Route::get('/tanks', [\App\Http\Controllers\MobileController::class, 'tankMonitoring'])->name('tanks');
        Route::match(['GET', 'POST'], '/orders/create', [\App\Http\Controllers\MobileController::class, 'createOrder'])->name('orders.create');
    });

    // Offline page (no auth required)
    Route::get('/offline', [\App\Http\Controllers\MobileController::class, 'offline'])->name('offline')->withoutMiddleware(['auth']);

    // Compliance & Security Routes
    Route::prefix('compliance')->name('compliance.')->group(function () {
        Route::get('/', [\App\Http\Controllers\ComplianceController::class, 'index'])->name('index');
        Route::post('/report', [\App\Http\Controllers\ComplianceController::class, 'generateComplianceReport'])->name('report');
        Route::post('/audit-report', [\App\Http\Controllers\ComplianceController::class, 'generateAuditReport'])->name('audit.report');
        Route::get('/alerts', [\App\Http\Controllers\ComplianceController::class, 'getAlerts'])->name('alerts');
        Route::get('/audit-trail', [\App\Http\Controllers\ComplianceController::class, 'getModelAuditTrail'])->name('audit.trail');
        Route::get('/export-compliance', [\App\Http\Controllers\ComplianceController::class, 'exportComplianceReport'])->name('export.compliance');
        Route::get('/export-audit', [\App\Http\Controllers\ComplianceController::class, 'exportAuditTrail'])->name('export.audit');
    });

    // System Administration Routes
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [\App\Http\Controllers\SystemController::class, 'index'])->name('index');
        Route::get('/performance-metrics', [\App\Http\Controllers\SystemController::class, 'getPerformanceMetrics'])->name('performance.metrics');
        Route::get('/real-time-metrics', [\App\Http\Controllers\SystemController::class, 'getRealTimeMetrics'])->name('realtime.metrics');
        Route::post('/optimize', [\App\Http\Controllers\SystemController::class, 'optimizeSystem'])->name('optimize');
        Route::post('/backup', [\App\Http\Controllers\SystemController::class, 'createBackup'])->name('backup.create');
        Route::get('/backups', [\App\Http\Controllers\SystemController::class, 'listBackups'])->name('backup.list');
        Route::post('/restore', [\App\Http\Controllers\SystemController::class, 'restoreBackup'])->name('backup.restore');
        Route::delete('/backup', [\App\Http\Controllers\SystemController::class, 'deleteBackup'])->name('backup.delete');
        Route::post('/security-report', [\App\Http\Controllers\SystemController::class, 'generateSecurityReport'])->name('security.report');
        Route::post('/clear-cache', [\App\Http\Controllers\SystemController::class, 'clearCache'])->name('cache.clear');
    });

    // Tank Management Routes
    Route::get('/tanks', [\App\Http\Controllers\TankController::class, 'dashboard'])->name('tanks.dashboard');
    Route::get('/tanks/list', [\App\Http\Controllers\TankController::class, 'index'])->name('tanks.index');
    Route::get('/tanks/{tank}', [\App\Http\Controllers\TankController::class, 'show'])->name('tanks.show');
    Route::post('/tanks/{tank}/reading', [\App\Http\Controllers\TankController::class, 'updateReading'])->name('tanks.reading.update');
    Route::get('/tanks/{tank}/readings', [\App\Http\Controllers\TankController::class, 'readings'])->name('tanks.readings');

    // Tank Refill Routes
    Route::get('/tanks/refills', [\App\Http\Controllers\TankRefillController::class, 'index'])->name('tanks.refills.index');
    Route::post('/tanks/{tank}/refill/schedule', [\App\Http\Controllers\TankRefillController::class, 'schedule'])->name('tanks.refills.schedule');
    Route::post('/tanks/refills/{refill}/complete', [\App\Http\Controllers\TankRefillController::class, 'complete'])->name('tanks.refills.complete');
    Route::post('/tanks/refills/{refill}/cancel', [\App\Http\Controllers\TankRefillController::class, 'cancel'])->name('tanks.refills.cancel');

    // Tank Alert Routes
    Route::get('/tanks/alerts', [\App\Http\Controllers\TankAlertController::class, 'index'])->name('tanks.alerts.index');
    Route::post('/tanks/alerts/{alert}/acknowledge', [\App\Http\Controllers\TankAlertController::class, 'acknowledge'])->name('tanks.alerts.acknowledge');
    Route::post('/tanks/alerts/{alert}/resolve', [\App\Http\Controllers\TankAlertController::class, 'resolve'])->name('tanks.alerts.resolve');
    Route::get('/tanks/alerts/check', [\App\Http\Controllers\TankAlertController::class, 'checkAlerts'])->name('tanks.alerts.check');

    // Additional Customer Management Routes
    Route::resource('customers', CustomerController::class);

    // Mobile Scanning Routes
    Route::get('/scan', function () {
        return view('scan.index');
    })->name('scan.index');

    Route::get('/scan/{qr_code}', function ($qr_code) {
        return view('scan.result', compact('qr_code'));
    })->name('scan.result');
});

require __DIR__.'/auth.php';
