<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whats_app_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->string('phone_number');
            $table->string('country_code', 10)->default('+91');
            $table->string('formatted_number')->unique();
            $table->string('name')->nullable();
            $table->string('profile_name')->nullable();
            $table->enum('status', ['active', 'inactive', 'blocked', 'invalid', 'unverified'])->default('active');
            $table->enum('opt_in_status', ['opted_in', 'opted_out', 'pending', 'unknown'])->default('unknown');
            $table->timestamp('opt_in_date')->nullable();
            $table->timestamp('opt_out_date')->nullable();
            $table->string('language_preference', 10)->default('en');
            $table->string('timezone')->nullable();
            $table->timestamp('last_message_at')->nullable();
            $table->timestamp('last_seen_at')->nullable();
            $table->integer('message_count')->default(0);
            $table->boolean('is_business_account')->default(false);
            $table->string('profile_picture_url')->nullable();
            $table->text('about')->nullable();
            $table->json('tags')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('blocked_at')->nullable();
            $table->text('blocked_reason')->nullable();
            $table->timestamps();

            $table->index(['formatted_number']);
            $table->index(['status', 'opt_in_status']);
            $table->index(['customer_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whats_app_contacts');
    }
};
