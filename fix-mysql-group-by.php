<?php
/**
 * Fix MySQL GROUP BY Issues
 * This script fixes MySQL ONLY_FULL_GROUP_BY mode issues
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "🔧 Fixing MySQL GROUP BY Issues\n";
echo "===============================\n\n";

try {
    echo "1. Checking current SQL mode:\n";
    echo "=============================\n";
    
    $sqlMode = DB::select("SELECT @@sql_mode as sql_mode")[0]->sql_mode;
    echo "   Current SQL Mode: {$sqlMode}\n";
    
    if (strpos($sqlMode, 'ONLY_FULL_GROUP_BY') !== false) {
        echo "   ⚠️  ONLY_FULL_GROUP_BY is enabled (causing GROUP BY issues)\n";
        
        echo "\n2. Disabling ONLY_FULL_GROUP_BY:\n";
        echo "=================================\n";
        
        // Remove ONLY_FULL_GROUP_BY from sql_mode
        $newSqlMode = str_replace('ONLY_FULL_GROUP_BY,', '', $sqlMode);
        $newSqlMode = str_replace(',ONLY_FULL_GROUP_BY', '', $newSqlMode);
        $newSqlMode = str_replace('ONLY_FULL_GROUP_BY', '', $newSqlMode);
        
        // Clean up any double commas
        $newSqlMode = str_replace(',,', ',', $newSqlMode);
        $newSqlMode = trim($newSqlMode, ',');
        
        DB::statement("SET sql_mode = '{$newSqlMode}'");
        
        echo "   ✅ ONLY_FULL_GROUP_BY disabled for this session\n";
        echo "   📝 New SQL Mode: {$newSqlMode}\n";
        
    } else {
        echo "   ✅ ONLY_FULL_GROUP_BY is not enabled\n";
    }
    
    echo "\n3. Testing problematic queries:\n";
    echo "===============================\n";
    
    // Test the query that was causing issues
    try {
        $result = DB::select("
            SELECT COUNT(*) as aggregate 
            FROM (
                SELECT gas_type_id, COUNT(*) as cylinder_count
                FROM cylinders 
                WHERE status = 'available' 
                GROUP BY gas_type_id 
                HAVING COUNT(*) < 10
            ) as temp_table
        ");
        
        echo "   ✅ Low stock query working: " . $result[0]->aggregate . " gas types with low stock\n";
        
    } catch (Exception $e) {
        echo "   ❌ Low stock query failed: " . $e->getMessage() . "\n";
    }
    
    // Test other GROUP BY queries
    try {
        $result = DB::select("
            SELECT status, COUNT(*) as count 
            FROM cylinders 
            GROUP BY status
        ");
        
        echo "   ✅ Cylinder status grouping working: " . count($result) . " status groups\n";
        
    } catch (Exception $e) {
        echo "   ❌ Cylinder status grouping failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Recommendations:\n";
    echo "===================\n";
    echo "   📝 To permanently fix this issue:\n";
    echo "   \n";
    echo "   Option 1: Update MySQL configuration (my.cnf or my.ini):\n";
    echo "   [mysqld]\n";
    echo "   sql_mode = \"STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO\"\n";
    echo "   \n";
    echo "   Option 2: Set in Laravel database config (config/database.php):\n";
    echo "   'mysql' => [\n";
    echo "       'strict' => false,\n";
    echo "       'modes' => [\n";
    echo "           'STRICT_TRANS_TABLES',\n";
    echo "           'NO_ZERO_DATE',\n";
    echo "           'NO_ZERO_IN_DATE',\n";
    echo "           'ERROR_FOR_DIVISION_BY_ZERO',\n";
    echo "       ],\n";
    echo "   ]\n";
    echo "   \n";
    echo "   Option 3: Run this command in MySQL:\n";
    echo "   SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';\n";
    
    echo "\n✅ MySQL GROUP BY issues have been analyzed and temporarily fixed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
