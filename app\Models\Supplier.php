<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_code',
        'name',
        'company_name',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'tax_id',
        'license_number',
        'license_expiry',
        'supplier_type',
        'gas_types_supplied',
        'service_areas',
        'credit_limit',
        'payment_terms',
        'rating',
        'is_active',
        'is_preferred',
        'notes',
        'certifications',
        'emergency_contacts',
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'gas_types_supplied' => 'array',
        'service_areas' => 'array',
        'credit_limit' => 'decimal:2',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
        'is_preferred' => 'boolean',
        'certifications' => 'array',
        'emergency_contacts' => 'array',
    ];

    /**
     * Supplier types
     */
    const SUPPLIER_TYPES = [
        'gas_supplier' => 'Gas Supplier',
        'equipment_supplier' => 'Equipment Supplier',
        'service_provider' => 'Service Provider',
        'transport' => 'Transport Company',
    ];

    /**
     * Payment terms
     */
    const PAYMENT_TERMS = [
        'immediate' => 'Immediate',
        'net_7' => 'Net 7 Days',
        'net_15' => 'Net 15 Days',
        'net_30' => 'Net 30 Days',
        'net_60' => 'Net 60 Days',
        'net_90' => 'Net 90 Days',
    ];

    /**
     * Get tanks supplied by this supplier
     */
    public function tanks(): HasMany
    {
        return $this->hasMany(Tank::class);
    }

    /**
     * Get refills from this supplier
     */
    public function refills(): HasMany
    {
        return $this->hasMany(TankRefill::class);
    }

    /**
     * Generate unique supplier code
     */
    public static function generateSupplierCode(): string
    {
        $prefix = 'SUP-' . date('Ymd') . '-';
        $lastSupplier = static::where('supplier_code', 'like', $prefix . '%')
                             ->orderBy('id', 'desc')
                             ->first();

        if ($lastSupplier) {
            $lastNumber = (int) substr($lastSupplier->supplier_code, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get supplier type label
     */
    public function getSupplierTypeLabel(): string
    {
        return self::SUPPLIER_TYPES[$this->supplier_type] ?? ucfirst(str_replace('_', ' ', $this->supplier_type));
    }

    /**
     * Get payment terms label
     */
    public function getPaymentTermsLabel(): string
    {
        return self::PAYMENT_TERMS[$this->payment_terms] ?? ucfirst(str_replace('_', ' ', $this->payment_terms));
    }

    /**
     * Get full address
     */
    public function getFullAddress(): string
    {
        $addressParts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country
        ]);

        return implode(', ', $addressParts);
    }

    /**
     * Check if license is expired
     */
    public function isLicenseExpired(): bool
    {
        return $this->license_expiry && $this->license_expiry->isPast();
    }

    /**
     * Get rating stars
     */
    public function getRatingStars(): string
    {
        $fullStars = floor($this->rating);
        $halfStar = ($this->rating - $fullStars) >= 0.5 ? 1 : 0;
        $emptyStars = 5 - $fullStars - $halfStar;

        return str_repeat('★', $fullStars) .
               str_repeat('☆', $halfStar) .
               str_repeat('☆', $emptyStars);
    }

    /**
     * Check if supplier can supply gas type
     */
    public function canSupplyGasType(int $gasTypeId): bool
    {
        if (!$this->gas_types_supplied) {
            return false;
        }

        return in_array($gasTypeId, $this->gas_types_supplied);
    }

    /**
     * Check if supplier serves location
     */
    public function servesLocation(int $locationId): bool
    {
        if (!$this->service_areas) {
            return true; // If no service areas specified, assume they serve all
        }

        return in_array($locationId, $this->service_areas);
    }

    /**
     * Scope for active suppliers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for preferred suppliers
     */
    public function scopePreferred($query)
    {
        return $query->where('is_preferred', true);
    }

    /**
     * Scope for specific supplier type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('supplier_type', $type);
    }

    /**
     * Scope for gas suppliers
     */
    public function scopeGasSuppliers($query)
    {
        return $query->where('supplier_type', 'gas_supplier');
    }

    /**
     * Auto-generate supplier code before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($supplier) {
            if (!$supplier->supplier_code) {
                $supplier->supplier_code = static::generateSupplierCode();
            }
        });
    }
}
