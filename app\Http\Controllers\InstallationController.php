<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\CompanySetting;
use App\Models\Location;
use App\Models\GasType;
use App\Models\Tank;
use Spatie\Permission\PermissionRegistrar;
use Exception;

class InstallationController extends Controller
{
    /**
     * Installation steps
     */
    private $steps = [
        1 => 'Welcome',
        2 => 'Requirements Check',
        3 => 'File Permissions',
        4 => 'Database Configuration',
        5 => 'Database Setup',
        6 => 'Admin Account',
        7 => 'Company Information',
        8 => 'Location Setup',
        9 => 'Gas Types Setup',
        10 => 'Tank Configuration',
        11 => 'Final Setup',
        12 => 'Complete'
    ];

    /**
     * Check if installation is already completed
     */
    private function isInstalled()
    {
        return File::exists(storage_path('installed'));
    }

    /**
     * Installation welcome page
     */
    public function index()
    {
        if ($this->isInstalled()) {
            return redirect('/')->with('error', 'GCMS is already installed.');
        }

        return view('installation.welcome', [
            'steps' => $this->steps,
            'currentStep' => 1
        ]);
    }

    /**
     * Step 2: Requirements Check
     */
    public function requirements()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        $requirements = $this->checkRequirements();

        return view('installation.requirements', [
            'steps' => $this->steps,
            'currentStep' => 2,
            'requirements' => $requirements,
            'canProceed' => $requirements['canProceed']
        ]);
    }

    /**
     * Step 3: File Permissions Check
     */
    public function permissions()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        $permissions = $this->checkPermissions();

        return view('installation.permissions', [
            'steps' => $this->steps,
            'currentStep' => 3,
            'permissions' => $permissions,
            'canProceed' => $permissions['canProceed']
        ]);
    }

    /**
     * Step 4: Database Configuration
     */
    public function database()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.database', [
            'steps' => $this->steps,
            'currentStep' => 4
        ]);
    }

    /**
     * Test database connection
     */
    public function testDatabase(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'db_host' => 'required|string',
            'db_port' => 'required|numeric',
            'db_name' => 'required|string',
            'db_username' => 'required|string',
            'db_password' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            // Test database connection
            $connection = $this->testDatabaseConnection(
                $request->db_host,
                $request->db_port,
                $request->db_name,
                $request->db_username,
                $request->db_password
            );

            if ($connection['success']) {
                // Save database configuration to .env
                $this->updateEnvFile([
                    'DB_HOST' => $request->db_host,
                    'DB_PORT' => $request->db_port,
                    'DB_DATABASE' => $request->db_name,
                    'DB_USERNAME' => $request->db_username,
                    'DB_PASSWORD' => $request->db_password,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Database connection successful!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $connection['message']
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 5: Database Setup (Run Migrations)
     */
    public function databaseSetup()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.database-setup', [
            'steps' => $this->steps,
            'currentStep' => 5
        ]);
    }

    /**
     * Run database migrations
     */
    public function runMigrations()
    {
        try {
            // Clear config cache
            Artisan::call('config:clear');

            // Install Spatie permissions (publishes migrations & config)
            Artisan::call('vendor:publish', [
                '--provider' => 'Spatie\Permission\PermissionServiceProvider',
                '--force' => true
            ]);

            // Run migrations (including Spatie tables)
            Artisan::call('migrate', ['--force' => true]);

            // Seed default roles and permissions
            Artisan::call('db:seed', ['--class' => 'RolePermissionSeeder']);

            // Clear Spatie permission cache
            app(PermissionRegistrar::class)->forgetCachedPermissions();

            return response()->json([
                'success' => true,
                'message' => 'Database setup completed successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Migration failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 6: Admin Account Setup
     */
    public function adminAccount()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.admin-account', [
            'steps' => $this->steps,
            'currentStep' => 6
        ]);
    }

    /**
     * Create admin account
     */
    public function createAdmin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            // Create admin user
            $admin = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'email_verified_at' => now(),
            ]);

            // Assign super admin role (will be created in seeder)
            $admin->assignRole('super_admin');

            return response()->json([
                'success' => true,
                'message' => 'Admin account created successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create admin account: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 7: Company Information
     */
    public function companyInfo()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.company-info', [
            'steps' => $this->steps,
            'currentStep' => 7
        ]);
    }

    /**
     * Save company information
     */
    public function saveCompanyInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email',
            'company_phone' => 'required|string|max:20',
            'company_address' => 'required|string',
            'company_city' => 'required|string|max:100',
            'company_state' => 'required|string|max:100',
            'company_country' => 'required|string|max:100',
            'company_postal_code' => 'required|string|max:20',
            'tax_number' => 'nullable|string|max:50',
            'license_number' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            CompanySetting::create([
                'company_name' => $request->company_name,
                'company_email' => $request->company_email,
                'company_phone' => $request->company_phone,
                'company_address' => $request->company_address,
                'company_city' => $request->company_city,
                'company_state' => $request->company_state,
                'company_country' => $request->company_country,
                'company_postal_code' => $request->company_postal_code,
                'tax_number' => $request->tax_number,
                'license_number' => $request->license_number,
                'currency' => $request->currency ?? 'USD',
                'timezone' => $request->timezone ?? 'UTC',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Company information saved successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save company information: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 8: Location Setup
     */
    public function locationSetup()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.location-setup', [
            'steps' => $this->steps,
            'currentStep' => 8
        ]);
    }

    /**
     * Save location information
     */
    public function saveLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email',
            'is_main' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            Location::create([
                'name' => $request->name,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'phone' => $request->phone,
                'email' => $request->email,
                'is_active' => true,
                'is_main' => $request->is_main ?? true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Location saved successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save location: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 9: Gas Types Setup
     */
    public function gasTypesSetup()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.gas-types', [
            'steps' => $this->steps,
            'currentStep' => 9
        ]);
    }

    /**
     * Save gas types
     */
    public function saveGasTypes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gas_types' => 'required|array|min:1',
            'gas_types.*.name' => 'required|string|max:255',
            'gas_types.*.code' => 'required|string|max:10',
            'gas_types.*.description' => 'nullable|string',
            'gas_types.*.is_hazardous' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            foreach ($request->gas_types as $gasType) {
                GasType::create([
                    'name' => $gasType['name'],
                    'code' => $gasType['code'],
                    'description' => $gasType['description'] ?? null,
                    'is_hazardous' => $gasType['is_hazardous'] ?? false,
                    'is_active' => true,
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Gas types saved successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save gas types: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 10: Tank Configuration
     */
    public function tankSetup()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        $locations = Location::all();
        $gasTypes = GasType::all();

        return view('installation.tank-setup', [
            'steps' => $this->steps,
            'currentStep' => 10,
            'locations' => $locations,
            'gasTypes' => $gasTypes
        ]);
    }

    /**
     * Save tank configuration
     */
    public function saveTanks(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tanks' => 'required|array|min:1',
            'tanks.*.name' => 'required|string|max:255',
            'tanks.*.location_id' => 'required|exists:locations,id',
            'tanks.*.gas_type_id' => 'required|exists:gas_types,id',
            'tanks.*.capacity' => 'required|numeric|min:1',
            'tanks.*.current_level' => 'required|numeric|min:0',
            'tanks.*.min_level' => 'required|numeric|min:0',
            'tanks.*.max_level' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            foreach ($request->tanks as $tank) {
                Tank::create([
                    'name' => $tank['name'],
                    'location_id' => $tank['location_id'],
                    'gas_type_id' => $tank['gas_type_id'],
                    'capacity' => $tank['capacity'],
                    'current_level' => $tank['current_level'],
                    'min_level' => $tank['min_level'],
                    'max_level' => $tank['max_level'],
                    'status' => 'active',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Tanks configured successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save tanks: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 11: Final Setup
     */
    public function finalSetup()
    {
        if ($this->isInstalled()) {
            return redirect('/');
        }

        return view('installation.final-setup', [
            'steps' => $this->steps,
            'currentStep' => 11
        ]);
    }

    /**
     * Complete installation
     */
    public function completeInstallation()
    {
        try {
            // Run seeders for roles and permissions
            Artisan::call('db:seed', ['--class' => 'RolePermissionSeeder']);

            // Generate application key if not exists
            if (empty(config('app.key'))) {
                Artisan::call('key:generate', ['--force' => true]);
            }

            // Clear and cache configurations
            Artisan::call('config:cache');
            Artisan::call('route:cache');

            // Create installation marker
            File::put(storage_path('installed'), now()->toDateTimeString());

            return response()->json([
                'success' => true,
                'message' => 'Installation completed successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Installation failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Step 12: Installation Complete
     */
    public function complete()
    {
        if (!$this->isInstalled()) {
            return redirect()->route('install.index');
        }

        return view('installation.complete', [
            'steps' => $this->steps,
            'currentStep' => 12
        ]);
    }

    /**
     * Helper Methods
     */

    /**
     * Check system requirements
     */
    private function checkRequirements()
    {
        $requirements = [
            'php_version' => [
                'name' => 'PHP Version (>= 8.1)',
                'check' => version_compare(PHP_VERSION, '8.1.0', '>='),
                'current' => PHP_VERSION
            ],
            'openssl' => [
                'name' => 'OpenSSL Extension',
                'check' => extension_loaded('openssl'),
                'current' => extension_loaded('openssl') ? 'Enabled' : 'Disabled'
            ],
            'pdo' => [
                'name' => 'PDO Extension',
                'check' => extension_loaded('pdo'),
                'current' => extension_loaded('pdo') ? 'Enabled' : 'Disabled'
            ],
            'mbstring' => [
                'name' => 'Mbstring Extension',
                'check' => extension_loaded('mbstring'),
                'current' => extension_loaded('mbstring') ? 'Enabled' : 'Disabled'
            ],
            'tokenizer' => [
                'name' => 'Tokenizer Extension',
                'check' => extension_loaded('tokenizer'),
                'current' => extension_loaded('tokenizer') ? 'Enabled' : 'Disabled'
            ],
            'xml' => [
                'name' => 'XML Extension',
                'check' => extension_loaded('xml'),
                'current' => extension_loaded('xml') ? 'Enabled' : 'Disabled'
            ],
            'ctype' => [
                'name' => 'Ctype Extension',
                'check' => extension_loaded('ctype'),
                'current' => extension_loaded('ctype') ? 'Enabled' : 'Disabled'
            ],
            'json' => [
                'name' => 'JSON Extension',
                'check' => extension_loaded('json'),
                'current' => extension_loaded('json') ? 'Enabled' : 'Disabled'
            ],
            'curl' => [
                'name' => 'cURL Extension',
                'check' => extension_loaded('curl'),
                'current' => extension_loaded('curl') ? 'Enabled' : 'Disabled'
            ],
            'gd' => [
                'name' => 'GD Extension (for QR codes)',
                'check' => extension_loaded('gd'),
                'current' => extension_loaded('gd') ? 'Enabled' : 'Disabled'
            ],
        ];

        $canProceed = true;
        foreach ($requirements as $requirement) {
            if (!$requirement['check']) {
                $canProceed = false;
                break;
            }
        }

        return [
            'requirements' => $requirements,
            'canProceed' => $canProceed
        ];
    }

    /**
     * Check file permissions
     */
    private function checkPermissions()
    {
        $permissions = [
            'storage' => [
                'name' => 'storage/',
                'path' => storage_path(),
                'check' => is_writable(storage_path()),
                'permission' => substr(sprintf('%o', fileperms(storage_path())), -4)
            ],
            'bootstrap_cache' => [
                'name' => 'bootstrap/cache/',
                'path' => base_path('bootstrap/cache'),
                'check' => is_writable(base_path('bootstrap/cache')),
                'permission' => substr(sprintf('%o', fileperms(base_path('bootstrap/cache'))), -4)
            ],
            'env_file' => [
                'name' => '.env file',
                'path' => base_path('.env'),
                'check' => is_writable(base_path('.env')),
                'permission' => file_exists(base_path('.env')) ? substr(sprintf('%o', fileperms(base_path('.env'))), -4) : 'Not Found'
            ],
        ];

        $canProceed = true;
        foreach ($permissions as $permission) {
            if (!$permission['check']) {
                $canProceed = false;
                break;
            }
        }

        return [
            'permissions' => $permissions,
            'canProceed' => $canProceed
        ];
    }

    /**
     * Test database connection
     */
    private function testDatabaseConnection($host, $port, $database, $username, $password)
    {
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database}";
            $pdo = new \PDO($dsn, $username, $password, [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
            ]);

            return [
                'success' => true,
                'message' => 'Database connection successful!'
            ];
        } catch (\PDOException $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update .env file
     */
    private function updateEnvFile($data)
    {
        $envFile = base_path('.env');
        $envContent = File::get($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        File::put($envFile, $envContent);
    }
}
