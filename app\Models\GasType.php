<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GasType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'color_code',
        'safety_info',
        'base_price',
        'rental_rate',
        'is_active',
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'rental_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get all cylinders of this gas type
     */
    public function cylinders(): HasMany
    {
        return $this->hasMany(Cylinder::class);
    }

    /**
     * Scope for active gas types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the color for UI display
     */
    public function getDisplayColor(): string
    {
        return $this->color_code ?? '#6B7280'; // Default gray
    }

    /**
     * Get cylinder count by status
     */
    public function getCylinderCountByStatus($status = null, $locationId = null)
    {
        $query = $this->cylinders();

        if ($status) {
            $query->where('status', $status);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        return $query->count();
    }

    /**
     * Get total cylinders for this gas type
     */
    public function getTotalCylindersAttribute(): int
    {
        return $this->cylinders()->count();
    }

    /**
     * Get available cylinders (empty + full)
     */
    public function getAvailableCylindersAttribute(): int
    {
        return $this->cylinders()
                    ->whereIn('status', ['empty', 'full'])
                    ->count();
    }

    /**
     * Get cylinders in use
     */
    public function getInUseCylindersAttribute(): int
    {
        return $this->cylinders()
                    ->where('status', 'in_use')
                    ->count();
    }

    /**
     * Get damaged cylinders
     */
    public function getDamagedCylindersAttribute(): int
    {
        return $this->cylinders()
                    ->where('status', 'damaged')
                    ->count();
    }
}
