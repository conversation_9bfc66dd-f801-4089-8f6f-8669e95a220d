<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->after('status');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('total_amount');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('tax_amount');
            $table->decimal('final_amount', 10, 2)->default(0)->after('discount_amount');
            $table->timestamp('scheduled_at')->nullable()->after('assigned_at');
            $table->text('delivery_address')->nullable()->after('scheduled_at');
            $table->text('special_instructions')->nullable()->after('delivery_notes');
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'overdue', 'refunded'])->default('pending')->after('special_instructions');
            $table->string('payment_method')->default('cash')->after('payment_status');
            $table->string('payment_reference')->nullable()->after('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'priority',
                'tax_amount',
                'discount_amount', 
                'final_amount',
                'scheduled_at',
                'delivery_address',
                'special_instructions',
                'payment_status',
                'payment_method',
                'payment_reference'
            ]);
        });
    }
};
