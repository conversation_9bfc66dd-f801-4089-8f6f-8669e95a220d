# 🗄️ **MySQL Database Setup for GCMS**

## 📋 **Prerequisites**

### **1. Install MySQL Server**

#### **Windows:**
- Download MySQL Installer from [mysql.com](https://dev.mysql.com/downloads/installer/)
- Run installer and select "MySQL Server" and "MySQL Workbench"
- Set root password during installation
- Start MySQL service

#### **macOS:**
```bash
# Using Homebrew
brew install mysql
brew services start mysql

# Secure installation
mysql_secure_installation
```

#### **Linux (Ubuntu/Debian):**
```bash
# Install MySQL
sudo apt update
sudo apt install mysql-server

# Secure installation
sudo mysql_secure_installation

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql
```

---

## 🚀 **Database Setup**

### **1. Create Database**

#### **Option A: Using MySQL Command Line**
```bash
# Connect to MySQL
mysql -u root -p

# Run the setup script
source setup-mysql.sql

# Or manually create database
CREATE DATABASE gcms_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gcms_database;
```

#### **Option B: Using MySQL Workbench**
1. Open MySQL Workbench
2. Connect to your MySQL server
3. Execute the `setup-mysql.sql` file
4. Or manually create database with UTF8MB4 charset

#### **Option C: Using phpMyAdmin**
1. Open phpMyAdmin in browser
2. Click "New" to create database
3. Name: `gcms_database`
4. Collation: `utf8mb4_unicode_ci`
5. Click "Create"

### **2. Verify Database Creation**
```sql
SHOW DATABASES;
USE gcms_database;
SHOW TABLES;
```

---

## ⚙️ **Laravel Configuration**

### **1. Environment Variables**
The `.env` file is already configured with MySQL settings:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=gcms_database
DB_USERNAME=root
DB_PASSWORD=your_mysql_password_here
```

### **2. Update Password**
Edit the `.env` file and set your MySQL password:
```env
DB_PASSWORD=your_actual_mysql_password
```

### **3. Test Database Connection**
```bash
# Test connection
php artisan tinker
DB::connection()->getPdo();
```

---

## 🔄 **Migration Process**

### **1. Fresh Migration (Recommended)**
```bash
# Clear any cached config
php artisan config:clear

# Run fresh migrations
php artisan migrate:fresh

# Seed initial data
php artisan db:seed
```

### **2. If Migrating from SQLite**
```bash
# Reset migrations
php artisan migrate:reset

# Clear config cache
php artisan config:clear

# Run migrations on MySQL
php artisan migrate

# Seed data
php artisan db:seed
```

---

## 🔧 **MySQL Optimization for GCMS**

### **1. MySQL Configuration (my.cnf or my.ini)**
```ini
[mysqld]
# Basic Settings
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance Settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
max_connections = 200
query_cache_size = 32M
query_cache_type = 1

# For development
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
```

### **2. Create Database User (Optional)**
```sql
-- Create dedicated user for GCMS
CREATE USER 'gcms_user'@'localhost' IDENTIFIED BY 'secure_password_123';

-- Grant privileges
GRANT ALL PRIVILEGES ON gcms_database.* TO 'gcms_user'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;
```

Then update `.env`:
```env
DB_USERNAME=gcms_user
DB_PASSWORD=secure_password_123
```

---

## 📊 **Database Schema Overview**

### **Core Tables:**
- `users` - System users and authentication
- `customers` - Customer information
- `locations` - Business locations
- `gas_types` - Types of gases
- `cylinders` - Cylinder inventory
- `orders` - Customer orders
- `rentals` - Rental agreements
- `invoices` - Billing and invoicing
- `tanks` - Tank monitoring
- `audit_trails` - System audit logs

### **Integration Tables:**
- `whats_app_messages` - WhatsApp integration
- `whats_app_templates` - Message templates
- `permissions` - Role-based access control
- `stock_movements` - Inventory tracking
- `tank_readings` - Tank level monitoring

---

## 🔍 **Verification Steps**

### **1. Check Database Connection**
```bash
php artisan tinker
```
```php
// Test connection
DB::connection()->getPdo();

// Check tables
Schema::hasTable('users');
Schema::hasTable('cylinders');
```

### **2. Verify Data Structure**
```sql
-- Show all tables
SHOW TABLES;

-- Check specific table structure
DESCRIBE cylinders;
DESCRIBE customers;
DESCRIBE orders;
```

### **3. Test Basic Operations**
```bash
php artisan tinker
```
```php
// Create test user
$user = App\Models\User::create([
    'name' => 'Test Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('password')
]);

// Check if created
App\Models\User::count();
```

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **1. Connection Refused**
```bash
# Check MySQL service status
sudo systemctl status mysql

# Start MySQL if stopped
sudo systemctl start mysql
```

#### **2. Access Denied**
- Verify username/password in `.env`
- Check user privileges in MySQL
- Reset MySQL root password if needed

#### **3. Database Not Found**
```sql
-- Create database if missing
CREATE DATABASE gcms_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### **4. Migration Errors**
```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Try migration again
php artisan migrate
```

---

## ✅ **Final Verification**

### **1. Run System Check**
```bash
# Check system status
php artisan about

# Test database
php artisan migrate:status

# Verify tables
php artisan tinker
```

### **2. Access Application**
```bash
# Start development server
php artisan serve

# Visit: http://localhost:8000
```

---

## 🎯 **Production Considerations**

### **1. Security**
- Use strong passwords
- Create dedicated database user
- Enable SSL connections
- Regular backups

### **2. Performance**
- Configure MySQL buffer pool
- Enable query cache
- Set up proper indexing
- Monitor slow queries

### **3. Backup Strategy**
```bash
# Daily backup script
mysqldump -u root -p gcms_database > backup_$(date +%Y%m%d).sql

# Automated backup with Laravel
php artisan backup:run
```

---

## 🎉 **Success!**

Your GCMS system is now configured to use MySQL database with:

✅ **Proper UTF8MB4 charset for international support**  
✅ **Optimized MySQL configuration**  
✅ **All migrations MySQL-compatible**  
✅ **Redis caching for performance**  
✅ **Production-ready database setup**  

**The system is ready for production deployment with MySQL! 🚀**
