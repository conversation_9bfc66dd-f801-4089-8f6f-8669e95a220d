<?php

namespace App\Http\Controllers;

use App\Models\Tank;
use App\Models\RefillLog;
use App\Services\RefillService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TankRefillController extends Controller
{
    protected $refillService;

    public function __construct(RefillService $refillService)
    {
        $this->middleware('auth');
        $this->refillService = $refillService;
    }

    /**
     * Display refill logs
     */
    public function index(Request $request)
    {
        $refillLogs = RefillLog::with(['tank', 'user'])
                             ->latest()
                             ->paginate(20);

        return view('tank-refills.index', compact('refillLogs'));
    }

    /**
     * Create refill log
     */
    public function store(Request $request)
    {
        $request->validate([
            'tank_id' => 'required|exists:tanks,id',
            'refill_amount' => 'required|numeric|min:0',
            'refill_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $refillLog = RefillLog::create([
            'tank_id' => $request->tank_id,
            'refill_amount' => $request->refill_amount,
            'refill_date' => $request->refill_date,
            'notes' => $request->notes,
            'user_id' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Refill logged successfully',
            'refill_log' => $refillLog->load(['tank', 'user'])
        ]);
    }
}
