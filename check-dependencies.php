<?php
/**
 * GCMS Dependencies Checker
 * This script checks if all Laravel dependencies are properly installed
 */

echo "🔍 GCMS Dependencies Checker\n";
echo "============================\n\n";

// Check if we're in the right directory
echo "1. Checking current directory:\n";
echo "==============================\n";
echo "   Current directory: " . getcwd() . "\n";

if (file_exists('composer.json')) {
    echo "   ✅ composer.json found\n";
} else {
    echo "   ❌ composer.json not found\n";
    exit(1);
}

if (file_exists('artisan')) {
    echo "   ✅ artisan file found\n";
} else {
    echo "   ❌ artisan file not found\n";
    exit(1);
}

// Check vendor directory
echo "\n2. Checking vendor directory:\n";
echo "=============================\n";

if (file_exists('vendor')) {
    echo "   ✅ vendor directory exists\n";
} else {
    echo "   ❌ vendor directory missing\n";
    echo "   💡 Run: composer install\n";
    exit(1);
}

if (file_exists('vendor/autoload.php')) {
    echo "   ✅ vendor/autoload.php exists\n";
} else {
    echo "   ❌ vendor/autoload.php missing\n";
    echo "   💡 Run: composer dump-autoload\n";
    exit(1);
}

// Try to load Laravel
echo "\n3. Testing Laravel bootstrap:\n";
echo "=============================\n";

try {
    require_once 'vendor/autoload.php';
    echo "   ✅ Autoloader loaded successfully\n";
} catch (Exception $e) {
    echo "   ❌ Autoloader failed: " . $e->getMessage() . "\n";
    exit(1);
}

try {
    $app = require_once 'bootstrap/app.php';
    echo "   ✅ Laravel app bootstrapped\n";
} catch (Exception $e) {
    echo "   ❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Check environment
echo "\n4. Checking environment:\n";
echo "========================\n";

if (file_exists('.env')) {
    echo "   ✅ .env file exists\n";
} else {
    echo "   ⚠️  .env file missing\n";
    if (file_exists('.env.example')) {
        echo "   💡 Copy .env.example to .env\n";
    }
}

// Check key packages
echo "\n5. Checking key packages:\n";
echo "=========================\n";

$packages = [
    'laravel/framework' => 'vendor/laravel/framework',
    'spatie/laravel-permission' => 'vendor/spatie/laravel-permission',
    'livewire/livewire' => 'vendor/livewire/livewire',
    'maatwebsite/excel' => 'vendor/maatwebsite/excel',
    'barryvdh/laravel-dompdf' => 'vendor/barryvdh/laravel-dompdf',
];

foreach ($packages as $package => $path) {
    if (file_exists($path)) {
        echo "   ✅ {$package}\n";
    } else {
        echo "   ❌ {$package} missing\n";
    }
}

// Test basic Laravel functionality
echo "\n6. Testing Laravel functionality:\n";
echo "=================================\n";

try {
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "   ✅ Laravel kernel bootstrapped\n";
} catch (Exception $e) {
    echo "   ❌ Laravel kernel failed: " . $e->getMessage() . "\n";
}

try {
    $config = config('app.name');
    echo "   ✅ Configuration accessible: {$config}\n";
} catch (Exception $e) {
    echo "   ❌ Configuration failed: " . $e->getMessage() . "\n";
}

// Check database connection
echo "\n7. Testing database connection:\n";
echo "===============================\n";

try {
    $pdo = new PDO(
        'mysql:host=' . env('DB_HOST', 'localhost') . ';dbname=' . env('DB_DATABASE'),
        env('DB_USERNAME'),
        env('DB_PASSWORD')
    );
    echo "   ✅ Database connection successful\n";
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Check web server
echo "\n8. Web server recommendations:\n";
echo "==============================\n";
echo "   💡 To start the server, run one of:\n";
echo "      php artisan serve\n";
echo "      php artisan serve --host=127.0.0.1 --port=8000\n";
echo "      php -S localhost:8000 -t public\n";

echo "\n✅ Dependencies check completed!\n";
echo "🚀 GCMS should be ready to run!\n";
?>
