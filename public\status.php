<?php
/**
 * GCMS Status Checker
 * Quick status check for the GCMS application
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>GCMS Status Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛢️ GCMS Status Check</h1>
        
        <h2>📋 System Information</h2>
        <div class="status info">
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
            <strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>

        <h2>🔧 PHP Extensions</h2>
        <?php
        $required_extensions = ['openssl', 'pdo', 'mbstring', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath', 'curl'];
        foreach ($required_extensions as $ext) {
            $loaded = extension_loaded($ext);
            $class = $loaded ? 'success' : 'error';
            $icon = $loaded ? '✅' : '❌';
            echo "<div class='status $class'>$icon $ext</div>";
        }
        ?>

        <h2>📁 File System</h2>
        <?php
        $paths = [
            'Laravel App' => '../bootstrap/app.php',
            'Vendor Autoload' => '../vendor/autoload.php',
            'Environment File' => '../.env',
            'Storage Directory' => '../storage',
            'Bootstrap Cache' => '../bootstrap/cache',
        ];

        foreach ($paths as $name => $path) {
            $exists = file_exists($path);
            $writable = $exists && is_writable($path);
            
            if ($exists) {
                $class = $writable ? 'success' : 'warning';
                $icon = $writable ? '✅' : '⚠️';
                $status = $writable ? 'Exists & Writable' : 'Exists but Not Writable';
            } else {
                $class = 'error';
                $icon = '❌';
                $status = 'Not Found';
            }
            
            echo "<div class='status $class'>$icon $name: $status</div>";
        }
        ?>

        <h2>🗄️ Database Connection</h2>
        <?php
        try {
            // Try to load Laravel environment
            if (file_exists('../.env')) {
                $env = file_get_contents('../.env');
                preg_match('/DB_HOST=(.*)/', $env, $host_match);
                preg_match('/DB_DATABASE=(.*)/', $env, $db_match);
                preg_match('/DB_USERNAME=(.*)/', $env, $user_match);
                preg_match('/DB_PASSWORD=(.*)/', $env, $pass_match);
                
                $host = trim($host_match[1] ?? '127.0.0.1');
                $database = trim($db_match[1] ?? 'gcms_database');
                $username = trim($user_match[1] ?? 'root');
                $password = trim($pass_match[1] ?? '');
                
                // Remove quotes if present
                $database = trim($database, '"\'');
                $username = trim($username, '"\'');
                $password = trim($password, '"\'');
                
                $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
                echo "<div class='status success'>✅ Database connection successful</div>";
                echo "<div class='status info'>📊 Connected to: $database on $host</div>";
            } else {
                echo "<div class='status warning'>⚠️ .env file not found</div>";
            }
        } catch (Exception $e) {
            echo "<div class='status error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
        }
        ?>

        <h2>🚀 Laravel Status</h2>
        <?php
        try {
            if (file_exists('../vendor/autoload.php')) {
                require_once '../vendor/autoload.php';
                
                if (file_exists('../bootstrap/app.php')) {
                    echo "<div class='status success'>✅ Laravel application files found</div>";
                    
                    // Check if installed
                    if (file_exists('../storage/installed')) {
                        echo "<div class='status success'>✅ GCMS is installed and ready</div>";
                    } else {
                        echo "<div class='status warning'>⚠️ GCMS not installed yet</div>";
                    }
                } else {
                    echo "<div class='status error'>❌ Laravel bootstrap file missing</div>";
                }
            } else {
                echo "<div class='status error'>❌ Composer dependencies not installed</div>";
            }
        } catch (Exception $e) {
            echo "<div class='status error'>❌ Laravel error: " . $e->getMessage() . "</div>";
        }
        ?>

        <h2>🌐 Quick Actions</h2>
        <a href="/" class="btn">🏠 Go to Application</a>
        <a href="/install" class="btn">⚙️ Installation Wizard</a>
        <a href="/dashboard" class="btn">📊 Dashboard</a>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>🔧 Troubleshooting Tips</h3>
            <ul>
                <li><strong>Redis Error:</strong> Change CACHE_STORE, SESSION_DRIVER, and QUEUE_CONNECTION to 'file' in .env</li>
                <li><strong>Permission Error:</strong> Run: <code>chmod -R 755 storage bootstrap/cache</code></li>
                <li><strong>Database Error:</strong> Check your database credentials in .env file</li>
                <li><strong>500 Error:</strong> Check storage/logs/laravel.log for detailed error messages</li>
            </ul>
        </div>
    </div>
</body>
</html>
