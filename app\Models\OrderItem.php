<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'gas_type_id',
        'quantity',
        'rate',
        'rental_days',
        'cylinder_ids',
        'allocated_cylinders',
        'subtotal',
        'discount_amount',
        'tax_amount',
        'final_amount',
        'status',
        'notes',
    ];

    protected $casts = [
        'rate' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'cylinder_ids' => 'array',
        'allocated_cylinders' => 'array',
    ];

    /**
     * Get the order for this item
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the gas type for this item
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get assigned cylinders
     */
    public function getAssignedCylinders()
    {
        if (!$this->cylinder_ids) {
            return collect();
        }

        return Cylinder::whereIn('id', $this->cylinder_ids)->get();
    }

    /**
     * Calculate subtotal
     */
    public function calculateSubtotal(): float
    {
        $baseAmount = $this->quantity * $this->rate;

        if ($this->rental_days) {
            $rentalAmount = $this->quantity * $this->gasType->rental_rate * $this->rental_days;
            return $baseAmount + $rentalAmount;
        }

        return $baseAmount;
    }

    /**
     * Get allocated cylinders
     */
    public function getAllocatedCylinders()
    {
        if (!$this->allocated_cylinders) {
            return collect();
        }

        return Cylinder::whereIn('id', $this->allocated_cylinders)->get();
    }

    /**
     * Get item status label
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'allocated' => ['label' => 'Allocated', 'color' => 'blue'],
            'fulfilled' => ['label' => 'Fulfilled', 'color' => 'green'],
            'cancelled' => ['label' => 'Cancelled', 'color' => 'red'],
            default => ['label' => 'Pending', 'color' => 'yellow']
        };
    }

    /**
     * Check if item is fully allocated
     */
    public function isFullyAllocated(): bool
    {
        return $this->allocated_cylinders && count($this->allocated_cylinders) >= $this->quantity;
    }

    /**
     * Get remaining quantity to allocate
     */
    public function getRemainingQuantity(): int
    {
        $allocatedCount = $this->allocated_cylinders ? count($this->allocated_cylinders) : 0;
        return max(0, $this->quantity - $allocatedCount);
    }

    /**
     * Allocate cylinders to this item
     */
    public function allocateCylinders(array $cylinderIds): bool
    {
        if (count($cylinderIds) > $this->getRemainingQuantity()) {
            return false;
        }

        // Verify cylinders are available and correct gas type
        $cylinders = Cylinder::whereIn('id', $cylinderIds)
                            ->where('gas_type_id', $this->gas_type_id)
                            ->where('status', 'full')
                            ->get();

        if ($cylinders->count() !== count($cylinderIds)) {
            return false;
        }

        // Update allocated cylinders
        $currentAllocated = $this->allocated_cylinders ?? [];
        $newAllocated = array_merge($currentAllocated, $cylinderIds);

        $this->update([
            'allocated_cylinders' => $newAllocated,
            'status' => $this->isFullyAllocated() ? 'allocated' : 'pending',
        ]);

        // Mark cylinders as allocated
        Cylinder::whereIn('id', $cylinderIds)->update(['status' => 'in_transit']);

        return true;
    }

    /**
     * Release allocated cylinders
     */
    public function releaseAllocatedCylinders(): bool
    {
        if (!$this->allocated_cylinders) {
            return true;
        }

        // Mark cylinders as available again
        Cylinder::whereIn('id', $this->allocated_cylinders)
               ->update(['status' => 'full']);

        $this->update([
            'allocated_cylinders' => null,
            'status' => 'pending',
        ]);

        return true;
    }

    /**
     * Calculate final amount with tax and discount
     */
    public function calculateFinalAmount(): float
    {
        $subtotal = $this->calculateSubtotal();
        $discountAmount = $this->discount_amount ?? 0;
        $taxAmount = $this->tax_amount ?? 0;

        return $subtotal - $discountAmount + $taxAmount;
    }

    /**
     * Auto-calculate amounts before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($orderItem) {
            if (!$orderItem->subtotal) {
                $orderItem->subtotal = $orderItem->calculateSubtotal();
            }

            if (!$orderItem->final_amount) {
                $orderItem->final_amount = $orderItem->calculateFinalAmount();
            }
        });
    }
}
