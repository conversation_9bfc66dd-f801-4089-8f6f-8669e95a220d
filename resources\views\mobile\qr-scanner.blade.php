<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">

    <title>QR Scanner - {{ config('app.name', 'GCMS') }}</title>

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        #scanner-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
        }
        
        #scanner-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .scanner-frame {
            width: 250px;
            height: 250px;
            border: 2px solid #fff;
            border-radius: 12px;
            position: relative;
            background: transparent;
        }
        
        .scanner-corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #3b82f6;
        }
        
        .scanner-corner.top-left {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .scanner-corner.top-right {
            top: -3px;
            right: -3px;
            border-left: none;
            border-bottom: none;
        }
        
        .scanner-corner.bottom-left {
            bottom: -3px;
            left: -3px;
            border-right: none;
            border-top: none;
        }
        
        .scanner-corner.bottom-right {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        .scanner-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3b82f6, transparent);
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { transform: translateY(0); }
            100% { transform: translateY(246px); }
        }
        
        .scanner-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            padding: 20px;
        }
        
        .result-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .result-content {
            background: white;
            margin: 20px;
            padding: 20px;
            border-radius: 12px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
    </style>
</head>

<body class="bg-black">
    <!-- Scanner Container -->
    <div id="scanner-container">
        <video id="scanner-video" autoplay muted playsinline></video>
        
        <!-- Scanner Overlay -->
        <div class="scanner-overlay">
            <div class="scanner-frame">
                <div class="scanner-corner top-left"></div>
                <div class="scanner-corner top-right"></div>
                <div class="scanner-corner bottom-left"></div>
                <div class="scanner-corner bottom-right"></div>
                <div class="scanner-line"></div>
            </div>
        </div>
        
        <!-- Scanner Controls -->
        <div class="scanner-controls">
            <div class="text-center mb-4">
                <p class="text-white text-lg font-medium mb-2">Scan QR Code</p>
                <p class="text-gray-300 text-sm">Position the QR code within the frame</p>
            </div>
            
            <div class="flex justify-center space-x-4 mb-4">
                <button id="torchBtn" class="bg-white bg-opacity-20 text-white p-3 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                    </svg>
                </button>
                
                <button id="switchCameraBtn" class="bg-white bg-opacity-20 text-white p-3 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                </button>
                
                <button id="closeBtn" class="bg-red-500 text-white p-3 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <div class="text-center">
                <p class="text-gray-400 text-xs">Tap torch to toggle flashlight • Tap switch to change camera</p>
            </div>
        </div>
    </div>

    <!-- Result Modal -->
    <div id="resultModal" class="result-modal hidden">
        <div class="result-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Scan Result</h3>
                <button id="closeResultBtn" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <div id="resultContent">
                <!-- Result content will be populated here -->
            </div>
            
            <div id="resultActions" class="mt-4 space-y-2">
                <!-- Action buttons will be populated here -->
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p class="text-gray-700">Processing scan...</p>
        </div>
    </div>

    <!-- QR Scanner Library -->
    <script src="https://unpkg.com/@zxing/library@latest/umd/index.min.js"></script>
    
    <script>
        class MobileQRScanner {
            constructor() {
                this.codeReader = new ZXing.BrowserMultiFormatReader();
                this.currentStream = null;
                this.currentDeviceId = null;
                this.torchEnabled = false;
                this.scanning = false;
                this.devices = [];
                this.currentDeviceIndex = 0;
                
                this.initializeScanner();
                this.bindEvents();
            }
            
            async initializeScanner() {
                try {
                    // Get available video devices
                    this.devices = await this.codeReader.listVideoInputDevices();
                    console.log('Available cameras:', this.devices);
                    
                    // Prefer back camera
                    const backCamera = this.devices.find(device => 
                        device.label.toLowerCase().includes('back') || 
                        device.label.toLowerCase().includes('rear') ||
                        device.label.toLowerCase().includes('environment')
                    );
                    
                    this.currentDeviceId = backCamera ? backCamera.deviceId : this.devices[0]?.deviceId;
                    
                    if (this.currentDeviceId) {
                        await this.startScanning();
                    } else {
                        this.showError('No camera found');
                    }
                } catch (error) {
                    console.error('Scanner initialization failed:', error);
                    this.showError('Camera access denied or not available');
                }
            }
            
            async startScanning() {
                try {
                    this.scanning = true;
                    
                    const constraints = {
                        video: {
                            deviceId: this.currentDeviceId,
                            facingMode: 'environment',
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    };
                    
                    this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                    const video = document.getElementById('scanner-video');
                    video.srcObject = this.currentStream;
                    
                    // Start QR code detection
                    this.codeReader.decodeFromVideoDevice(
                        this.currentDeviceId,
                        'scanner-video',
                        (result, error) => {
                            if (result && this.scanning) {
                                this.handleScanResult(result.text);
                            }
                        }
                    );
                    
                } catch (error) {
                    console.error('Failed to start scanning:', error);
                    this.showError('Failed to access camera');
                }
            }
            
            stopScanning() {
                this.scanning = false;
                
                if (this.currentStream) {
                    this.currentStream.getTracks().forEach(track => track.stop());
                    this.currentStream = null;
                }
                
                this.codeReader.reset();
            }
            
            async handleScanResult(qrData) {
                if (!this.scanning) return;
                
                this.scanning = false;
                this.showLoading();
                
                try {
                    const response = await fetch('/mobile/qr/process', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            qr_data: qrData,
                            scan_type: 'auto'
                        })
                    });
                    
                    const result = await response.json();
                    this.hideLoading();
                    
                    if (result.success) {
                        this.showResult(result.data, result.actions);
                    } else {
                        this.showError(result.message || 'Invalid QR code');
                    }
                } catch (error) {
                    this.hideLoading();
                    console.error('QR processing failed:', error);
                    this.showError('Failed to process QR code');
                }
            }
            
            showResult(data, actions) {
                const modal = document.getElementById('resultModal');
                const content = document.getElementById('resultContent');
                const actionsContainer = document.getElementById('resultActions');
                
                // Populate result content
                content.innerHTML = `
                    <div class="space-y-3">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <span class="text-green-800 font-medium">QR Code Scanned Successfully</span>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-3">
                            <h4 class="font-medium text-gray-900 mb-2">${data.type.charAt(0).toUpperCase() + data.type.slice(1)} Information</h4>
                            ${this.formatResultData(data)}
                        </div>
                    </div>
                `;
                
                // Populate actions
                actionsContainer.innerHTML = actions.map(action => `
                    <button onclick="performAction('${action.action}', ${JSON.stringify(data).replace(/"/g, '&quot;')})" 
                            class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-600 flex items-center justify-center">
                        <span class="mr-2">${action.icon}</span>
                        ${action.label}
                    </button>
                `).join('');
                
                modal.classList.remove('hidden');
            }
            
            formatResultData(data) {
                let html = '';
                Object.keys(data).forEach(key => {
                    if (key !== 'type' && data[key]) {
                        html += `
                            <div class="flex justify-between py-1">
                                <span class="text-gray-600 text-sm">${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span>
                                <span class="text-gray-900 text-sm font-medium">${data[key]}</span>
                            </div>
                        `;
                    }
                });
                return html;
            }
            
            showError(message) {
                const modal = document.getElementById('resultModal');
                const content = document.getElementById('resultContent');
                const actionsContainer = document.getElementById('resultActions');
                
                content.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-red-800 font-medium">Scan Failed</span>
                        </div>
                        <p class="text-red-700 text-sm mt-2">${message}</p>
                    </div>
                `;
                
                actionsContainer.innerHTML = `
                    <button onclick="scanner.resumeScanning()" 
                            class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-600">
                        Try Again
                    </button>
                `;
                
                modal.classList.remove('hidden');
            }
            
            resumeScanning() {
                document.getElementById('resultModal').classList.add('hidden');
                this.scanning = true;
            }
            
            async toggleTorch() {
                if (!this.currentStream) return;
                
                const track = this.currentStream.getVideoTracks()[0];
                const capabilities = track.getCapabilities();
                
                if (capabilities.torch) {
                    this.torchEnabled = !this.torchEnabled;
                    await track.applyConstraints({
                        advanced: [{ torch: this.torchEnabled }]
                    });
                    
                    const torchBtn = document.getElementById('torchBtn');
                    torchBtn.classList.toggle('bg-yellow-500', this.torchEnabled);
                    torchBtn.classList.toggle('bg-white', !this.torchEnabled);
                }
            }
            
            async switchCamera() {
                if (this.devices.length <= 1) return;
                
                this.stopScanning();
                this.currentDeviceIndex = (this.currentDeviceIndex + 1) % this.devices.length;
                this.currentDeviceId = this.devices[this.currentDeviceIndex].deviceId;
                await this.startScanning();
            }
            
            showLoading() {
                document.getElementById('loadingIndicator').classList.remove('hidden');
            }
            
            hideLoading() {
                document.getElementById('loadingIndicator').classList.add('hidden');
            }
            
            bindEvents() {
                document.getElementById('torchBtn').addEventListener('click', () => this.toggleTorch());
                document.getElementById('switchCameraBtn').addEventListener('click', () => this.switchCamera());
                document.getElementById('closeBtn').addEventListener('click', () => this.close());
                document.getElementById('closeResultBtn').addEventListener('click', () => this.resumeScanning());
            }
            
            close() {
                this.stopScanning();
                window.history.back();
            }
        }
        
        // Global functions
        function performAction(action, data) {
            console.log('Performing action:', action, data);
            
            switch (action) {
                case 'view_cylinder':
                    window.location.href = `/cylinders/${data.id}`;
                    break;
                case 'view_customer':
                    window.location.href = `/mobile/customers/${data.id}`;
                    break;
                case 'create_order':
                    window.location.href = `/mobile/orders/create?customer_id=${data.id}`;
                    break;
                default:
                    alert(`Action ${action} not implemented yet`);
            }
        }
        
        // Initialize scanner when page loads
        let scanner;
        document.addEventListener('DOMContentLoaded', () => {
            scanner = new MobileQRScanner();
        });
        
        // Cleanup when page unloads
        window.addEventListener('beforeunload', () => {
            if (scanner) {
                scanner.stopScanning();
            }
        });
    </script>
</body>
</html>
