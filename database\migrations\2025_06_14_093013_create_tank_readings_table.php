<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tank_readings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tank_id')->constrained();
            $table->decimal('reading_value', 10, 2);
            $table->enum('reading_type', ['level', 'temperature', 'pressure', 'flow_rate', 'consumption', 'refill']);
            $table->string('unit', 50);
            $table->enum('source', ['manual', 'sensor', 'automatic', 'inspection', 'maintenance']);
            $table->timestamp('recorded_at');
            $table->foreignId('recorded_by')->nullable()->constrained('users');
            $table->text('notes')->nullable();
            $table->decimal('temperature', 8, 2)->nullable();
            $table->decimal('pressure', 8, 2)->nullable();
            $table->decimal('humidity', 8, 2)->nullable();
            $table->json('sensor_data')->nullable();
            $table->timestamps();

            $table->index(['tank_id', 'reading_type']);
            $table->index(['tank_id', 'recorded_at']);
            $table->index(['reading_type', 'recorded_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tank_readings');
    }
};
