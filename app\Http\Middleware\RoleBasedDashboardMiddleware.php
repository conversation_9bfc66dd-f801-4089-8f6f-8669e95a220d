<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleBasedDashboardMiddleware
{
    /**
     * Handle an incoming request.
     * Redirect users to appropriate dashboard based on their role
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Check if user has permission to view dashboard
        if (!$user->can('view_dashboard') && !$user->can('view_location_dashboard')) {
            abort(403, 'You do not have permission to access the dashboard.');
        }

        // Add user role information to the request
        $request->merge([
            'user_role' => $user->getPrimaryRole(),
            'user_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
            'accessible_locations' => $this->getAccessibleLocations($user),
        ]);

        return $next($request);
    }

    /**
     * Get locations accessible to the user
     */
    private function getAccessibleLocations($user)
    {
        if ($user->hasAnyRole(['super_admin', 'admin', 'auditor'])) {
            return \App\Models\Location::active()->get();
        }

        if ($user->hasRole('location_manager')) {
            return $user->managedLocations()->active()->get()
                       ->merge($user->locations()->active()->get())
                       ->unique('id');
        }

        return $user->locations()->active()->get();
    }
}
