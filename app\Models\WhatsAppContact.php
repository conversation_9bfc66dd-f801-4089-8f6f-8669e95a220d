<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsAppContact extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'phone_number',
        'country_code',
        'formatted_number',
        'name',
        'profile_name',
        'status',
        'opt_in_status',
        'opt_in_date',
        'opt_out_date',
        'language_preference',
        'timezone',
        'last_message_at',
        'last_seen_at',
        'message_count',
        'is_business_account',
        'profile_picture_url',
        'about',
        'tags',
        'notes',
        'blocked_at',
        'blocked_reason',
    ];

    protected $casts = [
        'opt_in_date' => 'datetime',
        'opt_out_date' => 'datetime',
        'last_message_at' => 'datetime',
        'last_seen_at' => 'datetime',
        'blocked_at' => 'datetime',
        'is_business_account' => 'boolean',
        'tags' => 'array',
    ];

    /**
     * Contact statuses
     */
    const STATUSES = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'blocked' => 'Blocked',
        'invalid' => 'Invalid Number',
        'unverified' => 'Unverified',
    ];

    /**
     * Opt-in statuses
     */
    const OPT_IN_STATUSES = [
        'opted_in' => 'Opted In',
        'opted_out' => 'Opted Out',
        'pending' => 'Pending Confirmation',
        'unknown' => 'Unknown',
    ];

    /**
     * Language preferences
     */
    const LANGUAGES = [
        'en' => 'English',
        'hi' => 'Hindi',
        'es' => 'Spanish',
        'fr' => 'French',
        'de' => 'German',
        'pt' => 'Portuguese',
        'ar' => 'Arabic',
    ];

    /**
     * Get the customer for this contact
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get messages sent to this contact
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'contact_id');
    }

    /**
     * Format phone number for WhatsApp
     */
    public static function formatPhoneNumber(string $phoneNumber, string $countryCode = '+91'): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Remove leading zeros
        $cleaned = ltrim($cleaned, '0');

        // Add country code if not present
        if (!str_starts_with($cleaned, substr($countryCode, 1))) {
            $cleaned = substr($countryCode, 1) . $cleaned;
        }

        return $cleaned;
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel(): array
    {
        return match($this->status) {
            'active' => ['label' => 'Active', 'color' => 'green'],
            'inactive' => ['label' => 'Inactive', 'color' => 'gray'],
            'blocked' => ['label' => 'Blocked', 'color' => 'red'],
            'invalid' => ['label' => 'Invalid Number', 'color' => 'red'],
            'unverified' => ['label' => 'Unverified', 'color' => 'yellow'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Get opt-in status label with color
     */
    public function getOptInStatusLabel(): array
    {
        return match($this->opt_in_status) {
            'opted_in' => ['label' => 'Opted In', 'color' => 'green'],
            'opted_out' => ['label' => 'Opted Out', 'color' => 'red'],
            'pending' => ['label' => 'Pending', 'color' => 'yellow'],
            'unknown' => ['label' => 'Unknown', 'color' => 'gray'],
            default => ['label' => ucfirst($this->opt_in_status), 'color' => 'gray']
        };
    }

    /**
     * Check if contact can receive messages
     */
    public function canReceiveMessages(): bool
    {
        return $this->status === 'active' &&
               $this->opt_in_status === 'opted_in' &&
               !$this->blocked_at;
    }

    /**
     * Check if contact is blocked
     */
    public function isBlocked(): bool
    {
        return $this->status === 'blocked' || $this->blocked_at;
    }

    /**
     * Block contact
     */
    public function block(string $reason = null): bool
    {
        return $this->update([
            'status' => 'blocked',
            'blocked_at' => now(),
            'blocked_reason' => $reason,
        ]);
    }

    /**
     * Unblock contact
     */
    public function unblock(): bool
    {
        return $this->update([
            'status' => 'active',
            'blocked_at' => null,
            'blocked_reason' => null,
        ]);
    }

    /**
     * Opt in contact
     */
    public function optIn(): bool
    {
        return $this->update([
            'opt_in_status' => 'opted_in',
            'opt_in_date' => now(),
            'opt_out_date' => null,
        ]);
    }

    /**
     * Opt out contact
     */
    public function optOut(): bool
    {
        return $this->update([
            'opt_in_status' => 'opted_out',
            'opt_out_date' => now(),
        ]);
    }

    /**
     * Update last message timestamp
     */
    public function updateLastMessage(): bool
    {
        return $this->update([
            'last_message_at' => now(),
            'message_count' => $this->message_count + 1,
        ]);
    }

    /**
     * Add tag to contact
     */
    public function addTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            return $this->update(['tags' => $tags]);
        }
        return true;
    }

    /**
     * Remove tag from contact
     */
    public function removeTag(string $tag): bool
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        return $this->update(['tags' => array_values($tags)]);
    }

    /**
     * Check if contact has tag
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    /**
     * Scope for active contacts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for opted-in contacts
     */
    public function scopeOptedIn($query)
    {
        return $query->where('opt_in_status', 'opted_in');
    }

    /**
     * Scope for contacts that can receive messages
     */
    public function scopeCanReceiveMessages($query)
    {
        return $query->where('status', 'active')
                    ->where('opt_in_status', 'opted_in')
                    ->whereNull('blocked_at');
    }

    /**
     * Scope for blocked contacts
     */
    public function scopeBlocked($query)
    {
        return $query->where('status', 'blocked')
                    ->orWhereNotNull('blocked_at');
    }

    /**
     * Scope for contacts with specific tag
     */
    public function scopeWithTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * Auto-format phone number before saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($contact) {
            if ($contact->phone_number) {
                $contact->formatted_number = static::formatPhoneNumber(
                    $contact->phone_number,
                    $contact->country_code ?? '+91'
                );
            }
        });
    }
}
