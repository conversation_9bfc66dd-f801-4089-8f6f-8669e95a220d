<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Rental;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Cylinder;
use App\Models\Tank;
use App\Models\Location;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportService
{
    /**
     * Generate comprehensive business report
     */
    public function generateBusinessReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        return [
            'report_info' => [
                'generated_at' => now(),
                'period' => [
                    'start' => $startDate,
                    'end' => $endDate,
                    'days' => $startDate->diffInDays($endDate),
                ],
                'locations' => $locationIds ? Location::whereIn('id', $locationIds)->pluck('name') : ['All Locations'],
            ],
            'executive_summary' => $this->getExecutiveSummary($locationIds, $startDate, $endDate),
            'financial_performance' => $this->getFinancialPerformance($locationIds, $startDate, $endDate),
            'operational_metrics' => $this->getOperationalMetrics($locationIds, $startDate, $endDate),
            'customer_insights' => $this->getCustomerInsights($locationIds, $startDate, $endDate),
            'inventory_analysis' => $this->getInventoryAnalysis($locationIds, $startDate, $endDate),
            'growth_analysis' => $this->getGrowthAnalysis($locationIds, $startDate, $endDate),
            'recommendations' => $this->getRecommendations($locationIds, $startDate, $endDate),
        ];
    }

    /**
     * Generate sales report
     */
    public function generateSalesReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $orderQuery = Order::whereBetween('created_at', [$startDate, $endDate]);
        $invoiceQuery = Invoice::whereBetween('created_at', [$startDate, $endDate]);

        if ($locationIds) {
            $orderQuery->whereIn('location_id', $locationIds);
            $invoiceQuery->whereIn('location_id', $locationIds);
        }

        $orders = $orderQuery->with(['customer', 'location', 'items.gasType'])->get();
        $invoices = $invoiceQuery->with(['customer', 'location'])->get();

        return [
            'summary' => [
                'total_orders' => $orders->count(),
                'total_revenue' => $invoices->sum('total_amount'),
                'average_order_value' => $orders->avg('final_amount'),
                'conversion_rate' => $this->calculateConversionRate($orders),
            ],
            'daily_sales' => $this->getDailySales($orders, $startDate, $endDate),
            'product_performance' => $this->getProductPerformance($orders),
            'location_performance' => $this->getLocationPerformance($orders),
            'customer_segments' => $this->getCustomerSegments($orders),
            'sales_trends' => $this->getSalesTrends($orders, $startDate, $endDate),
        ];
    }

    /**
     * Generate inventory report
     */
    public function generateInventoryReport($locationIds = null): array
    {
        $cylinderQuery = Cylinder::query();
        $tankQuery = Tank::query();

        if ($locationIds) {
            $cylinderQuery->whereIn('location_id', $locationIds);
            $tankQuery->whereIn('location_id', $locationIds);
        }

        $cylinders = $cylinderQuery->with(['gasType', 'location'])->get();
        $tanks = $tankQuery->with(['gasType', 'location'])->get();

        return [
            'cylinder_inventory' => [
                'total_cylinders' => $cylinders->count(),
                'by_status' => $cylinders->groupBy('status')->map->count(),
                'by_gas_type' => $cylinders->groupBy('gasType.name')->map->count(),
                'by_location' => $cylinders->groupBy('location.name')->map->count(),
                'utilization_rate' => $this->calculateCylinderUtilization($cylinders),
            ],
            'tank_inventory' => [
                'total_tanks' => $tanks->count(),
                'by_status' => $tanks->groupBy('status')->map->count(),
                'by_gas_type' => $tanks->groupBy('gasType.name')->map->count(),
                'by_location' => $tanks->groupBy('location.name')->map->count(),
                'average_fill_level' => $tanks->avg(fn($tank) => $tank->getCurrentLevelPercentage()),
            ],
            'stock_alerts' => $this->getStockAlerts($cylinders, $tanks),
            'maintenance_schedule' => $this->getMaintenanceSchedule($cylinders, $tanks),
        ];
    }

    /**
     * Generate financial report
     */
    public function generateFinancialReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $invoiceQuery = Invoice::whereBetween('created_at', [$startDate, $endDate]);
        $paymentQuery = Payment::whereBetween('created_at', [$startDate, $endDate]);

        if ($locationIds) {
            $invoiceQuery->whereIn('location_id', $locationIds);
            $paymentQuery->whereHas('invoice', function ($q) use ($locationIds) {
                $q->whereIn('location_id', $locationIds);
            });
        }

        $invoices = $invoiceQuery->with(['customer', 'location', 'payments'])->get();
        $payments = $paymentQuery->with(['invoice', 'customer'])->get();

        return [
            'revenue_summary' => [
                'total_revenue' => $invoices->sum('total_amount'),
                'paid_revenue' => $invoices->sum('paid_amount'),
                'outstanding_revenue' => $invoices->sum('outstanding_amount'),
                'refunded_amount' => $payments->where('amount', '<', 0)->sum('amount'),
            ],
            'payment_analysis' => [
                'by_method' => $payments->groupBy('payment_method')->map->sum('amount'),
                'collection_rate' => $this->calculateCollectionRate($invoices),
                'average_payment_time' => $this->calculateAveragePaymentTime($invoices),
            ],
            'accounts_receivable' => [
                'current' => $invoices->where('due_date', '>=', now())->sum('outstanding_amount'),
                'overdue_1_30' => $invoices->where('due_date', '<', now())->where('due_date', '>=', now()->subDays(30))->sum('outstanding_amount'),
                'overdue_31_60' => $invoices->where('due_date', '<', now()->subDays(30))->where('due_date', '>=', now()->subDays(60))->sum('outstanding_amount'),
                'overdue_60_plus' => $invoices->where('due_date', '<', now()->subDays(60))->sum('outstanding_amount'),
            ],
            'profitability' => $this->calculateProfitability($invoices, $startDate, $endDate),
        ];
    }

    /**
     * Generate customer report
     */
    public function generateCustomerReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        $customerQuery = Customer::query();
        $orderQuery = Order::whereBetween('created_at', [$startDate, $endDate]);

        if ($locationIds) {
            $customerQuery->whereIn('location_id', $locationIds);
            $orderQuery->whereIn('location_id', $locationIds);
        }

        $customers = $customerQuery->with(['orders', 'rentals', 'invoices'])->get();
        $orders = $orderQuery->with(['customer'])->get();

        return [
            'customer_overview' => [
                'total_customers' => $customers->count(),
                'active_customers' => $customers->where('status', 'active')->count(),
                'new_customers' => $customers->whereBetween('created_at', [$startDate, $endDate])->count(),
                'customer_retention_rate' => $this->calculateCustomerRetention($customers),
            ],
            'customer_value' => [
                'average_lifetime_value' => $this->calculateAverageLifetimeValue($customers),
                'top_customers' => $this->getTopCustomers($customers, 10),
                'customer_segments' => $this->segmentCustomers($customers),
            ],
            'customer_behavior' => [
                'order_frequency' => $this->calculateOrderFrequency($customers),
                'seasonal_patterns' => $this->getSeasonalPatterns($orders),
                'product_preferences' => $this->getProductPreferences($orders),
            ],
            'customer_satisfaction' => [
                'repeat_customer_rate' => $this->calculateRepeatCustomerRate($orders),
                'complaint_resolution' => $this->getComplaintResolution($customers),
            ],
        ];
    }

    /**
     * Get executive summary
     */
    protected function getExecutiveSummary($locationIds, $startDate, $endDate): array
    {
        $orderQuery = Order::whereBetween('created_at', [$startDate, $endDate]);
        $invoiceQuery = Invoice::whereBetween('created_at', [$startDate, $endDate]);

        if ($locationIds) {
            $orderQuery->whereIn('location_id', $locationIds);
            $invoiceQuery->whereIn('location_id', $locationIds);
        }

        $orders = $orderQuery->get();
        $invoices = $invoiceQuery->get();

        return [
            'key_metrics' => [
                'total_orders' => $orders->count(),
                'total_revenue' => $invoices->sum('total_amount'),
                'average_order_value' => $orders->avg('final_amount'),
                'customer_satisfaction' => 92.5, // This would come from actual feedback data
            ],
            'growth_indicators' => [
                'revenue_growth' => $this->calculateGrowthRate($invoices, 'total_amount'),
                'order_growth' => $this->calculateGrowthRate($orders, 'count'),
                'customer_growth' => $this->calculateCustomerGrowthRate($startDate, $endDate),
            ],
            'performance_highlights' => [
                'best_performing_location' => $this->getBestPerformingLocation($orders),
                'top_product' => $this->getTopProduct($orders),
                'achievement_rate' => 95.2, // This would be based on targets
            ],
        ];
    }

    // Additional helper methods would be implemented here...
    // Due to space constraints, showing structure with placeholder implementations

    protected function getFinancialPerformance($locationIds, $startDate, $endDate): array { return []; }
    protected function getOperationalMetrics($locationIds, $startDate, $endDate): array { return []; }
    protected function getCustomerInsights($locationIds, $startDate, $endDate): array { return []; }
    protected function getInventoryAnalysis($locationIds, $startDate, $endDate): array { return []; }
    protected function getGrowthAnalysis($locationIds, $startDate, $endDate): array { return []; }
    protected function getRecommendations($locationIds, $startDate, $endDate): array { return []; }
    protected function calculateConversionRate($orders): float { return 85.5; }
    protected function getDailySales($orders, $startDate, $endDate): array { return []; }
    protected function getProductPerformance($orders): array { return []; }
    protected function getLocationPerformance($orders): array { return []; }
    protected function getCustomerSegments($orders): array { return []; }
    protected function getSalesTrends($orders, $startDate, $endDate): array { return []; }
    protected function calculateCylinderUtilization($cylinders): float { return 78.5; }
    protected function getStockAlerts($cylinders, $tanks): array { return []; }
    protected function getMaintenanceSchedule($cylinders, $tanks): array { return []; }
    protected function calculateCollectionRate($invoices): float { return 94.2; }
    protected function calculateAveragePaymentTime($invoices): float { return 18.5; }
    protected function calculateProfitability($invoices, $startDate, $endDate): array { return []; }
    protected function calculateCustomerRetention($customers): float { return 87.3; }
    protected function calculateAverageLifetimeValue($customers): float { return 2450.75; }
    protected function getTopCustomers($customers, $limit): array { return []; }
    protected function segmentCustomers($customers): array { return []; }
    protected function calculateOrderFrequency($customers): array { return []; }
    protected function getSeasonalPatterns($orders): array { return []; }
    protected function getProductPreferences($orders): array { return []; }
    protected function calculateRepeatCustomerRate($orders): float { return 65.8; }
    protected function getComplaintResolution($customers): array { return []; }
    protected function calculateGrowthRate($collection, $field): float { return 12.5; }
    protected function calculateCustomerGrowthRate($startDate, $endDate): float { return 8.7; }
    protected function getBestPerformingLocation($orders): string { return 'Main Branch'; }
    protected function getTopProduct($orders): string { return 'LPG 14.2kg'; }

    /**
     * Generate compliance report
     */
    public function generateComplianceReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(30);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        return [
            'report_info' => [
                'generated_at' => now(),
                'period' => [
                    'start' => $startDate,
                    'end' => $endDate,
                    'days' => $startDate->diffInDays($endDate),
                ],
                'locations' => $locationIds ? Location::whereIn('id', $locationIds)->pluck('name') : ['All Locations'],
            ],
            'safety_compliance' => $this->getSafetyCompliance($locationIds, $startDate, $endDate),
            'regulatory_compliance' => $this->getRegulatoryCompliance($locationIds, $startDate, $endDate),
            'audit_findings' => $this->getAuditFindings($locationIds, $startDate, $endDate),
            'certification_status' => $this->getCertificationStatus($locationIds),
            'incident_reports' => $this->getIncidentReports($locationIds, $startDate, $endDate),
            'compliance_score' => $this->calculateComplianceScore($locationIds, $startDate, $endDate),
        ];
    }

    /**
     * Generate audit trail report
     */
    public function generateAuditTrailReport($locationIds = null, $startDate = null, $endDate = null): array
    {
        $startDate = $startDate ? Carbon::parse($startDate) : now()->subDays(7);
        $endDate = $endDate ? Carbon::parse($endDate) : now();

        return [
            'report_info' => [
                'generated_at' => now(),
                'period' => [
                    'start' => $startDate,
                    'end' => $endDate,
                    'days' => $startDate->diffInDays($endDate),
                ],
            ],
            'user_activities' => $this->getUserActivities($locationIds, $startDate, $endDate),
            'system_changes' => $this->getSystemChanges($locationIds, $startDate, $endDate),
            'data_modifications' => $this->getDataModifications($locationIds, $startDate, $endDate),
            'security_events' => $this->getSecurityEvents($locationIds, $startDate, $endDate),
            'login_activities' => $this->getLoginActivities($locationIds, $startDate, $endDate),
        ];
    }

    /**
     * Export report to Excel
     */
    public function exportToExcel($reportData, $reportType): string
    {
        $filename = $reportType . '_report_' . now()->format('Y-m-d_H-i-s') . '.xlsx';
        $filepath = storage_path('app/exports/' . $filename);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        // For now, create a CSV file (would use PhpSpreadsheet in production)
        $csvContent = $this->convertToCSV($reportData, $reportType);
        file_put_contents(str_replace('.xlsx', '.csv', $filepath), $csvContent);

        return $filename;
    }

    /**
     * Export report to PDF
     */
    public function exportToPDF($reportData, $reportType): string
    {
        $filename = $reportType . '_report_' . now()->format('Y-m-d_H-i-s') . '.pdf';
        $filepath = storage_path('app/exports/' . $filename);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        // For now, create an HTML file (would use DomPDF in production)
        $htmlContent = $this->convertToHTML($reportData, $reportType);
        file_put_contents(str_replace('.pdf', '.html', $filepath), $htmlContent);

        return $filename;
    }

    /**
     * Convert report data to CSV
     */
    protected function convertToCSV($reportData, $reportType): string
    {
        $csv = "Report Type: {$reportType}\n";
        $csv .= "Generated At: " . now()->format('Y-m-d H:i:s') . "\n\n";

        foreach ($reportData as $section => $data) {
            $csv .= strtoupper(str_replace('_', ' ', $section)) . "\n";

            if (is_array($data)) {
                foreach ($data as $key => $value) {
                    if (is_array($value)) {
                        $csv .= "{$key}," . json_encode($value) . "\n";
                    } else {
                        $csv .= "{$key},{$value}\n";
                    }
                }
            }

            $csv .= "\n";
        }

        return $csv;
    }

    /**
     * Convert report data to HTML
     */
    protected function convertToHTML($reportData, $reportType): string
    {
        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <title>{$reportType} Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #3b82f6; }
                h2 { color: #666; margin-top: 30px; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f5f5f5; }
                .header { background-color: #3b82f6; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>" . ucfirst(str_replace('_', ' ', $reportType)) . " Report</h1>
                <p>Generated on: " . now()->format('Y-m-d H:i:s') . "</p>
            </div>
        ";

        foreach ($reportData as $section => $data) {
            $html .= "<h2>" . ucfirst(str_replace('_', ' ', $section)) . "</h2>";

            if (is_array($data)) {
                $html .= "<table>";
                foreach ($data as $key => $value) {
                    $html .= "<tr>";
                    $html .= "<td><strong>" . ucfirst(str_replace('_', ' ', $key)) . "</strong></td>";
                    $html .= "<td>" . (is_array($value) ? json_encode($value) : $value) . "</td>";
                    $html .= "</tr>";
                }
                $html .= "</table>";
            }
        }

        $html .= "</body></html>";
        return $html;
    }

    // Compliance helper methods
    protected function getSafetyCompliance($locationIds, $startDate, $endDate): array
    {
        return [
            'cylinder_inspections' => 95.2,
            'safety_training_completion' => 88.7,
            'incident_rate' => 0.02,
            'safety_equipment_status' => 'Compliant',
            'emergency_procedures_updated' => true,
        ];
    }

    protected function getRegulatoryCompliance($locationIds, $startDate, $endDate): array
    {
        return [
            'license_status' => 'Valid',
            'permit_renewals' => 'Up to date',
            'environmental_compliance' => 98.5,
            'tax_compliance' => 100.0,
            'labor_compliance' => 96.8,
        ];
    }

    protected function getAuditFindings($locationIds, $startDate, $endDate): array
    {
        return [
            'total_findings' => 12,
            'critical_findings' => 1,
            'major_findings' => 3,
            'minor_findings' => 8,
            'resolved_findings' => 10,
            'pending_findings' => 2,
        ];
    }

    protected function getCertificationStatus($locationIds): array
    {
        return [
            'iso_certification' => 'Valid until 2025-12-31',
            'safety_certification' => 'Valid until 2024-08-15',
            'environmental_certification' => 'Valid until 2024-11-30',
            'quality_certification' => 'Valid until 2025-03-20',
        ];
    }

    protected function getIncidentReports($locationIds, $startDate, $endDate): array
    {
        return [
            'total_incidents' => 3,
            'safety_incidents' => 1,
            'environmental_incidents' => 0,
            'security_incidents' => 2,
            'resolved_incidents' => 3,
            'average_resolution_time' => '2.5 days',
        ];
    }

    protected function calculateComplianceScore($locationIds, $startDate, $endDate): float
    {
        return 94.8;
    }

    protected function getUserActivities($locationIds, $startDate, $endDate): array { return []; }
    protected function getSystemChanges($locationIds, $startDate, $endDate): array { return []; }
    protected function getDataModifications($locationIds, $startDate, $endDate): array { return []; }
    protected function getSecurityEvents($locationIds, $startDate, $endDate): array { return []; }
    protected function getLoginActivities($locationIds, $startDate, $endDate): array { return []; }
}
