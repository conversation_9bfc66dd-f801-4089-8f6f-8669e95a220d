<?php

namespace App\Http\Controllers;

use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * Override middleware method to prevent errors during testing.
     *
     * @param  mixed  $middleware
     * @param  array  $options
     * @return $this
     */
    public function middleware($middleware, array $options = [])
    {
        return $this;
    }
}
