<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Cylinder extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'unique_id',
        'qr_code',
        'gas_type_id',
        'location_id',
        'status',
        'capacity',
        'tare_weight',
        'current_weight',
        'last_filled_at',
        'expiry_date',
        'last_inspection_date',
        'next_inspection_date',
        'notes',
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'tare_weight' => 'decimal:2',
        'current_weight' => 'decimal:2',
        'last_filled_at' => 'datetime',
        'expiry_date' => 'date',
        'last_inspection_date' => 'date',
        'next_inspection_date' => 'date',
    ];

    /**
     * Activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'location_id', 'current_weight'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the gas type for this cylinder
     */
    public function gasType(): BelongsTo
    {
        return $this->belongsTo(GasType::class);
    }

    /**
     * Get the current location of this cylinder
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get all logs for this cylinder
     */
    public function logs(): HasMany
    {
        return $this->hasMany(CylinderLog::class);
    }

    /**
     * Scope for cylinders with specific status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for cylinders at specific location
     */
    public function scopeAtLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope for expired cylinders
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * Scope for cylinders due for inspection
     */
    public function scopeDueForInspection($query)
    {
        return $query->where('next_inspection_date', '<=', now());
    }

    /**
     * Get status label with color
     */
    public function getStatusLabel()
    {
        return match($this->status) {
            'empty' => ['label' => 'Empty', 'color' => 'gray'],
            'full' => ['label' => 'Full', 'color' => 'green'],
            'in_use' => ['label' => 'In Use', 'color' => 'blue'],
            'damaged' => ['label' => 'Damaged', 'color' => 'red'],
            'expired' => ['label' => 'Expired', 'color' => 'orange'],
            'maintenance' => ['label' => 'Maintenance', 'color' => 'yellow'],
            'in_transit' => ['label' => 'In Transit', 'color' => 'purple'],
            default => ['label' => ucfirst($this->status), 'color' => 'gray']
        };
    }

    /**
     * Check if cylinder is available for use
     */
    public function isAvailable(): bool
    {
        return in_array($this->status, ['empty', 'full']);
    }

    /**
     * Check if cylinder needs inspection
     */
    public function needsInspection(): bool
    {
        return $this->next_inspection_date && $this->next_inspection_date <= now();
    }

    /**
     * Check if cylinder is expired
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    /**
     * Generate QR code for cylinder
     */
    public function generateQrCode(): string
    {
        if (!$this->qr_code) {
            $this->qr_code = 'CYL-' . strtoupper(uniqid());
            $this->save();
        }
        return $this->qr_code;
    }
}
