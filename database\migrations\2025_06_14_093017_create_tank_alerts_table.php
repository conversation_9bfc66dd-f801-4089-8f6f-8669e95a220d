<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tank_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tank_id')->constrained();
            $table->enum('alert_type', [
                'low_level', 'critical_level', 'high_pressure', 'low_pressure',
                'temperature_high', 'temperature_low', 'maintenance_due', 'inspection_due',
                'certificate_expiry', 'sensor_failure', 'leak_detected', 'valve_malfunction',
                'safety_system_failure', 'refill_scheduled', 'refill_completed'
            ]);
            $table->enum('severity', ['info', 'warning', 'critical', 'emergency']);
            $table->text('message');
            $table->timestamp('triggered_at');
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->enum('status', ['active', 'acknowledged', 'resolved', 'dismissed'])->default('active');
            $table->foreignId('acknowledged_by')->nullable()->constrained('users');
            $table->foreignId('resolved_by')->nullable()->constrained('users');
            $table->text('action_taken')->nullable();
            $table->text('notes')->nullable();
            $table->json('alert_data')->nullable();
            $table->timestamps();

            $table->index(['tank_id', 'status']);
            $table->index(['alert_type', 'status']);
            $table->index(['severity', 'status']);
            $table->index(['status', 'triggered_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tank_alerts');
    }
};
