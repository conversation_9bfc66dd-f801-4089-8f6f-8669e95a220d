<?php
/**
 * GCMS Safe Database Seeder
 * This script safely seeds the database without conflicts
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;

echo "🌱 GCMS Safe Database Seeder\n";
echo "============================\n\n";

try {
    echo "1. Checking existing data...\n";
    $userCount = User::count();
    $roleCount = Role::count();
    $permissionCount = Permission::count();
    
    echo "   Users: $userCount\n";
    echo "   Roles: $roleCount\n";
    echo "   Permissions: $permissionCount\n\n";

    // Create roles if they don't exist
    echo "2. Creating roles...\n";
    $roles = [
        'super_admin' => 'Super Administrator',
        'admin' => 'Administrator', 
        'manager' => 'Manager',
        'operator' => 'Operator',
        'driver' => 'Driver',
        'customer' => 'Customer'
    ];

    foreach ($roles as $name => $displayName) {
        $role = Role::firstOrCreate(['name' => $name], ['display_name' => $displayName]);
        echo "   ✅ Role: $name\n";
    }

    echo "\n3. Assigning permissions to roles...\n";
    
    // Get all permissions
    $permissions = Permission::all();
    
    // Super Admin gets all permissions
    $superAdmin = Role::where('name', 'super_admin')->first();
    if ($superAdmin && $permissions->count() > 0) {
        $superAdmin->syncPermissions($permissions);
        echo "   ✅ Super Admin: All permissions assigned\n";
    }

    // Admin gets most permissions (excluding super admin specific ones)
    $admin = Role::where('name', 'admin')->first();
    if ($admin) {
        $adminPermissions = $permissions->filter(function($permission) {
            return !in_array($permission->name, ['manage_system', 'view_system_logs']);
        });
        $admin->syncPermissions($adminPermissions);
        echo "   ✅ Admin: " . $adminPermissions->count() . " permissions assigned\n";
    }

    // Manager gets operational permissions
    $manager = Role::where('name', 'manager')->first();
    if ($manager) {
        $managerPermissions = $permissions->filter(function($permission) {
            return str_contains($permission->name, 'view_') || 
                   str_contains($permission->name, 'create_') ||
                   str_contains($permission->name, 'edit_') ||
                   in_array($permission->name, ['manage_orders', 'manage_rentals', 'view_reports']);
        });
        $manager->syncPermissions($managerPermissions);
        echo "   ✅ Manager: " . $managerPermissions->count() . " permissions assigned\n";
    }

    // Operator gets basic operational permissions
    $operator = Role::where('name', 'operator')->first();
    if ($operator) {
        $operatorPermissions = $permissions->filter(function($permission) {
            return in_array($permission->name, [
                'view_dashboard', 'view_orders', 'create_orders', 'edit_orders',
                'view_customers', 'create_customers', 'edit_customers',
                'view_cylinders', 'edit_cylinders', 'view_rentals'
            ]);
        });
        $operator->syncPermissions($operatorPermissions);
        echo "   ✅ Operator: " . $operatorPermissions->count() . " permissions assigned\n";
    }

    echo "\n4. Creating default admin user...\n";
    
    // Create default admin user if no users exist
    if ($userCount == 0) {
        $adminUser = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $adminUser->assignRole('super_admin');
        echo "   ✅ Admin user created: <EMAIL> / password123\n";
    } else {
        echo "   ℹ️  Users already exist, skipping admin creation\n";
    }

    echo "\n5. Creating sample data...\n";
    
    // Create sample company settings
    $companySetting = \App\Models\CompanySetting::firstOrCreate(
        ['key' => 'company_name'],
        ['value' => 'GCMS Demo Company', 'type' => 'string']
    );
    echo "   ✅ Company settings created\n";

    // Create sample location
    $location = \App\Models\Location::firstOrCreate(
        ['name' => 'Main Warehouse'],
        [
            'address' => '123 Industrial Street',
            'city' => 'Demo City',
            'state' => 'Demo State',
            'postal_code' => '12345',
            'country' => 'Demo Country',
            'phone' => '******-0123',
            'email' => '<EMAIL>',
            'is_main' => true,
            'is_active' => true,
        ]
    );
    echo "   ✅ Main location created\n";

    // Create sample gas types
    $gasTypes = [
        ['name' => 'Oxygen', 'code' => 'O2', 'color' => '#00ff00'],
        ['name' => 'Nitrogen', 'code' => 'N2', 'color' => '#0000ff'],
        ['name' => 'Argon', 'code' => 'AR', 'color' => '#ff0000'],
        ['name' => 'Carbon Dioxide', 'code' => 'CO2', 'color' => '#ffff00'],
    ];

    foreach ($gasTypes as $gasType) {
        \App\Models\GasType::firstOrCreate(
            ['code' => $gasType['code']],
            array_merge($gasType, ['is_active' => true])
        );
    }
    echo "   ✅ Gas types created\n";

    echo "\n🎉 Database seeding completed successfully!\n";
    echo "==========================================\n";
    echo "✅ Roles and permissions configured\n";
    echo "✅ Admin user created (if needed)\n";
    echo "✅ Sample data added\n\n";

    echo "🔐 Default Login Credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: password123\n\n";

    echo "🚀 Next Steps:\n";
    echo "1. Access: http://localhost:8000\n";
    echo "2. Login with admin credentials\n";
    echo "3. Complete setup via installation wizard\n";
    echo "4. Start using GCMS!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
