<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ __('Cylinder Details') }} - {{ $cylinder->unique_id }}
            </h2>
            <div class="flex space-x-2">
                @can('generate_qr_codes')
                    <a href="{{ route('cylinders.qr', $cylinder) }}" target="_blank" 
                       class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        📱 QR Code
                    </a>
                @endcan
                @can('edit_cylinders')
                    <a href="{{ route('cylinders.edit', $cylinder) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit
                    </a>
                @endcan
                <a href="{{ route('cylinders.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to List
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Cylinder Overview -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        
                        <!-- Basic Information -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                Cylinder Information
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Unique ID</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">{{ $cylinder->unique_id }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">QR Code</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">{{ $cylinder->qr_code }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Gas Type</label>
                                    <div class="mt-1 flex items-center">
                                        <div class="h-4 w-4 rounded-full mr-2" style="background-color: {{ $cylinder->gasType->getDisplayColor() }}"></div>
                                        <span class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->gasType->name }} ({{ $cylinder->gasType->code }})</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Status</label>
                                    @php $statusLabel = $cylinder->getStatusLabel(); @endphp
                                    <span class="mt-1 inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-{{ $statusLabel['color'] }}-100 text-{{ $statusLabel['color'] }}-800">
                                        {{ $statusLabel['label'] }}
                                    </span>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Capacity</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->capacity }} Liters</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tare Weight</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->tare_weight }} kg</p>
                                </div>
                                
                                @if($cylinder->current_weight)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Weight</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->current_weight }} kg</p>
                                </div>
                                @endif
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Location</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                        {{ $cylinder->location->name }}
                                        <span class="text-gray-500">({{ $cylinder->location->getTypeLabel() }})</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status & Alerts -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                Status & Alerts
                            </h3>
                            
                            <div class="space-y-4">
                                <!-- Current Status -->
                                <div class="p-4 rounded-lg border-2 border-{{ $statusLabel['color'] }}-200 bg-{{ $statusLabel['color'] }}-50 dark:bg-{{ $statusLabel['color'] }}-900">
                                    <div class="flex items-center">
                                        <div class="text-{{ $statusLabel['color'] }}-600 dark:text-{{ $statusLabel['color'] }}-400">
                                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-{{ $statusLabel['color'] }}-800 dark:text-{{ $statusLabel['color'] }}-200">
                                                {{ $statusLabel['label'] }}
                                            </p>
                                            <p class="text-sm text-{{ $statusLabel['color'] }}-600 dark:text-{{ $statusLabel['color'] }}-400">
                                                Current cylinder status
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Alerts -->
                                @if($cylinder->isExpired())
                                <div class="p-4 rounded-lg border-2 border-red-200 bg-red-50 dark:bg-red-900">
                                    <div class="flex items-center">
                                        <div class="text-red-600 dark:text-red-400">
                                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-red-800 dark:text-red-200">
                                                Expired Cylinder
                                            </p>
                                            <p class="text-sm text-red-600 dark:text-red-400">
                                                Expired on {{ $cylinder->expiry_date->format('M d, Y') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                
                                @if($cylinder->needsInspection())
                                <div class="p-4 rounded-lg border-2 border-yellow-200 bg-yellow-50 dark:bg-yellow-900">
                                    <div class="flex items-center">
                                        <div class="text-yellow-600 dark:text-yellow-400">
                                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                                Inspection Due
                                            </p>
                                            <p class="text-sm text-yellow-600 dark:text-yellow-400">
                                                Due: {{ $cylinder->next_inspection_date->format('M d, Y') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                
                                <!-- Dates -->
                                <div class="space-y-2">
                                    @if($cylinder->last_filled_at)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Filled</label>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->last_filled_at->format('M d, Y H:i') }}</p>
                                    </div>
                                    @endif
                                    
                                    @if($cylinder->expiry_date)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Expiry Date</label>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->expiry_date->format('M d, Y') }}</p>
                                    </div>
                                    @endif
                                    
                                    @if($cylinder->next_inspection_date)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Next Inspection</label>
                                        <p class="text-sm text-gray-900 dark:text-gray-100">{{ $cylinder->next_inspection_date->format('M d, Y') }}</p>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    @if($cylinder->notes)
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                        <p class="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            {{ $cylinder->notes }}
                        </p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Activity History -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
                        Activity History
                    </h3>
                    
                    @if($cylinder->logs->count() > 0)
                        <div class="flow-root">
                            <ul class="-mb-8">
                                @foreach($cylinder->logs as $log)
                                    @php $actionLabel = $log->getActionLabel(); @endphp
                                    <li>
                                        <div class="relative pb-8">
                                            @if(!$loop->last)
                                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                                            @endif
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-{{ $actionLabel['color'] }}-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800">
                                                        <span class="text-white text-xs font-bold">
                                                            {{ substr($actionLabel['label'], 0, 1) }}
                                                        </span>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-900 dark:text-gray-100">
                                                            <span class="font-medium">{{ $actionLabel['label'] }}</span>
                                                            @if($log->old_status && $log->new_status && $log->old_status !== $log->new_status)
                                                                - Status changed from 
                                                                <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $log->old_status)) }}</span>
                                                                to 
                                                                <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $log->new_status)) }}</span>
                                                            @endif
                                                        </p>
                                                        @if($log->notes)
                                                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $log->notes }}</p>
                                                        @endif
                                                        @if($log->location)
                                                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                                                📍 {{ $log->location->name }}
                                                            </p>
                                                        @endif
                                                    </div>
                                                    <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                                        <div>{{ $log->created_at->format('M d, Y') }}</div>
                                                        <div>{{ $log->created_at->format('H:i') }}</div>
                                                        @if($log->user)
                                                            <div class="text-xs">by {{ $log->user->name }}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @else
                        <p class="text-gray-500 dark:text-gray-400 text-center py-8">
                            No activity history available for this cylinder.
                        </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
